<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos SDK - Debug Component Loading</title>

        <!-- Use the production build -->
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            // Minimal required configuration
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            localStorage.setItem('AT', 'test-token');
            
            // Debug logging
            let debugMessages = [];
            function addDebugMessage(message) {
                debugMessages.push(`${new Date().toISOString()}: ${message}`);
                const debugDiv = document.getElementById('debug-log');
                if (debugDiv) {
                    debugDiv.innerHTML = debugMessages.slice(-20).join('<br>');
                }
                console.log(message);
            }
            
            // Override console.log to capture SDK logs
            const originalLog = console.log;
            console.log = function(...args) {
                if (args[0] && args[0].includes && (args[0].includes('🚀') || args[0].includes('✅') || args[0].includes('🔄') || args[0].includes('⏳'))) {
                    addDebugMessage(args.join(' '));
                }
                originalLog.apply(console, args);
            };
            
            // Listen for SDK events
            window.addEventListener('angelos:component-loaded', (e) => {
                addDebugMessage(`🎉 Component loaded event: ${e.detail.componentName} in ${e.detail.loadTime}ms`);
            });
            
            window.addEventListener('angelos:component-error', (e) => {
                addDebugMessage(`❌ Component error event: ${e.detail.componentName} - ${e.detail.error}`);
            });
        </script>
    </head>
    <body>
        <div style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h1>🔍 Angelos SDK Component Loading Debug</h1>
            
            <div style="display: flex; gap: 20px; margin: 20px 0;">
                <button onclick="addComponent()" style="padding: 10px 20px; font-size: 16px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Add Form Component
                </button>
                <button onclick="inspectComponent()" style="padding: 10px 20px; font-size: 16px; background: #2196F3; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Inspect Component
                </button>
                <button onclick="forceUpgrade()" style="padding: 10px 20px; font-size: 16px; background: #FF9800; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Force Upgrade
                </button>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <!-- Component Container -->
                <div>
                    <h3>📝 Component Container</h3>
                    <div id="component-container" style="border: 2px solid #4CAF50; padding: 15px; min-height: 200px; background: #f9f9f9;">
                        <p style="color: #666;">Components will appear here...</p>
                    </div>
                    
                    <div style="margin-top: 10px; font-size: 12px; color: #666;">
                        <strong>Component Info:</strong>
                        <div id="component-info">No component added yet</div>
                    </div>
                </div>

                <!-- Debug Log -->
                <div>
                    <h3>🔍 Debug Log</h3>
                    <div id="debug-log" style="border: 1px solid #ccc; padding: 10px; height: 400px; overflow-y: auto; background: #f5f5f5; font-family: monospace; font-size: 12px; white-space: pre-wrap;">
                        Waiting for SDK initialization...
                    </div>
                </div>
            </div>
        </div>

        <script>
            let componentCount = 0;
            
            function addComponent() {
                componentCount++;
                const container = document.getElementById('component-container');
                
                addDebugMessage(`🆕 Adding form component #${componentCount}`);
                
                const componentEl = document.createElement('zwe-angelos-create-form-v3');
                componentEl.setAttribute('entity-id', 'test-entity');
                componentEl.setAttribute('tenant-id', '0');
                componentEl.setAttribute('show-form-actions', 'true');
                componentEl.id = `form-component-${componentCount}`;
                
                container.appendChild(componentEl);
                addDebugMessage(`✨ Component element added to DOM with ID: ${componentEl.id}`);
                
                updateComponentInfo();
            }
            
            function inspectComponent() {
                const component = document.querySelector('zwe-angelos-create-form-v3');
                if (!component) {
                    addDebugMessage('❌ No component found to inspect');
                    return;
                }
                
                addDebugMessage(`🔍 Inspecting component:`);
                addDebugMessage(`   - Tag name: ${component.tagName}`);
                addDebugMessage(`   - Constructor: ${component.constructor.name}`);
                addDebugMessage(`   - Connected: ${component.isConnected}`);
                addDebugMessage(`   - Children count: ${component.children.length}`);
                addDebugMessage(`   - InnerHTML length: ${component.innerHTML.length}`);
                addDebugMessage(`   - Attributes: ${Array.from(component.attributes).map(a => `${a.name}="${a.value}"`).join(', ')}`);
                
                // Check if custom element is defined
                if (customElements.get('zwe-angelos-create-form-v3')) {
                    addDebugMessage(`✅ Custom element 'zwe-angelos-create-form-v3' is defined`);
                } else {
                    addDebugMessage(`❌ Custom element 'zwe-angelos-create-form-v3' is NOT defined`);
                }
                
                updateComponentInfo();
            }
            
            function forceUpgrade() {
                if (window.AngelosSDK && window.AngelosSDK.debug) {
                    addDebugMessage('🔧 Forcing component upgrade...');
                    window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3');
                } else {
                    addDebugMessage('❌ AngelosSDK debug methods not available');
                }
            }
            
            function updateComponentInfo() {
                const component = document.querySelector('zwe-angelos-create-form-v3');
                const infoDiv = document.getElementById('component-info');
                
                if (component) {
                    infoDiv.innerHTML = `
                        <strong>Found:</strong> ${component.tagName}<br>
                        <strong>Constructor:</strong> ${component.constructor.name}<br>
                        <strong>Children:</strong> ${component.children.length}<br>
                        <strong>InnerHTML:</strong> ${component.innerHTML.length} chars<br>
                        <strong>Defined:</strong> ${customElements.get('zwe-angelos-create-form-v3') ? 'Yes' : 'No'}
                    `;
                } else {
                    infoDiv.innerHTML = 'No component found';
                }
            }
            
            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', () => {
                addDebugMessage('📄 DOM loaded');
                
                // Wait for SDK
                function waitForSDK() {
                    if (window.AngelosSDK) {
                        addDebugMessage('🎉 Angelos SDK detected');
                        addDebugMessage(`📊 SDK Stats: ${JSON.stringify(window.AngelosSDK.getStats())}`);
                    } else {
                        setTimeout(waitForSDK, 100);
                    }
                }
                waitForSDK();
            });
        </script>
    </body>
</html>
