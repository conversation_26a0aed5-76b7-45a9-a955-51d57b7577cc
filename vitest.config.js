import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [vue()],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
            vue: 'vue/dist/vue.esm-bundler.js' // This is required for creating dynamic compoenent
        }
    },
    test: {
        globals: true,
        coverage: {
            provider: 'istanbul'
        },
        environment: 'happy-dom',
        testMatch: ['**/*.spec.ts']
    }
});
