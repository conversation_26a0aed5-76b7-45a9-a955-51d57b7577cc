/**
 * Production Server with Compression
 * Serves the optimized build with proper gzip compression
 */

const express = require('express');
const compression = require('compression');
const path = require('path');

const app = express();
const PORT = 8080;

// Enable gzip compression for all responses
app.use(compression({
    // Compress all responses
    filter: (req, res) => {
        // Don't compress responses with this request header
        if (req.headers['x-no-compression']) {
            return false;
        }
        // Use compression filter function
        return compression.filter(req, res);
    },
    // Compression level (1-9, 6 is default)
    level: 6,
    // Minimum response size to compress
    threshold: 1024,
    // Compression window bits
    windowBits: 15,
    // Memory level
    memLevel: 8
}));

// Serve static files from dist directory
app.use(express.static(path.join(__dirname, 'dist'), {
    // Enable etag for caching
    etag: true,
    // Set cache headers
    setHeaders: (res, filePath) => {
        // Cache JS files for 1 year
        if (filePath.endsWith('.js')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        }
        // Cache CSS files for 1 year
        if (filePath.endsWith('.css')) {
            res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        }
        // Don't cache HTML files
        if (filePath.endsWith('.html')) {
            res.setHeader('Cache-Control', 'no-cache');
        }
    }
}));

// Serve static files from root (for index.html, mocks, etc.)
app.use(express.static(__dirname, {
    // Don't serve node_modules
    dotfiles: 'ignore',
    index: false
}));

// Serve the main index.html at root
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        compression: 'enabled',
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Production server running at http://localhost:${PORT}`);
    console.log(`📊 Compression: ENABLED`);
    console.log(`📁 Serving from: ${path.join(__dirname, 'dist')}`);
    console.log(`🔧 Health check: http://localhost:${PORT}/health`);
    
    // Log expected file sizes
    console.log('\n📦 Expected bundle sizes:');
    console.log('   Main bundle: ~5.7 MB (uncompressed) → ~1.1 MB (gzipped)');
    console.log('   Monaco Editor: ~6.3 MB (uncompressed) → ~1.1 MB (gzipped)');
    console.log('   Total savings: ~82% with compression');
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Server shutting down...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 Server shutting down...');
    process.exit(0);
});
