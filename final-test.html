<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Production Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
        }
        .log {
            background: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #444;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1976D2;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Final Production Test</h1>
        <p>Testing if the Vue runtime initialization fix resolved the component rendering issue.</p>

        <div class="test-section">
            <h2>🧪 Component Tests</h2>
            <button onclick="testSimpleVue()">Test Simple Vue Component</button>
            <button onclick="testPlainJS()">Test Plain JS Component</button>
            <button onclick="testAngelosForm()">Test Angelos Form Component</button>
            <button onclick="clearComponents()">Clear Components</button>
            
            <div id="component-area" style="border: 1px dashed #ccc; padding: 20px; margin: 10px 0; min-height: 100px;">
                <em>Components will render here...</em>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Debug Log</h2>
            <button onclick="clearLog()">Clear Log</button>
            <div id="log" class="log">🎬 Production test initialized...\nClick test buttons to verify components work.\n</div>
        </div>
    </div>

    <!-- Load context and params -->
    <script src="./mocks/setContextAndParams.js"></script>
    <script src="./dist/angelos.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logEl.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        async function waitForSDK() {
            log('⏳ Waiting for AngelosSDK...', 'info');
            let attempts = 0;
            while (attempts < 50) {
                if (window.AngelosSDK) {
                    log('✅ AngelosSDK is available', 'success');
                    return window.AngelosSDK;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            log('❌ AngelosSDK not available after 5 seconds', 'error');
            return null;
        }

        async function testSimpleVue() {
            try {
                log('🚀 Testing Simple Vue Component...', 'info');
                
                const sdk = await waitForSDK();
                if (!sdk) return;

                log('📦 Force loading zwe-test-simple-component...', 'info');
                await sdk.debug.forceLoadAndUpgrade('zwe-test-simple-component');
                log('✅ Component module loaded', 'success');

                const element = document.createElement('zwe-test-simple-component');
                const area = document.getElementById('component-area');
                area.innerHTML = '<h3>Simple Vue Component Test:</h3>';
                area.appendChild(element);
                
                log('🏗️ Element created and added to DOM', 'info');

                setTimeout(() => {
                    const isDefined = customElements.get('zwe-test-simple-component');
                    const hasContent = element.innerHTML.trim().length > 0;
                    
                    if (isDefined && hasContent) {
                        log('🎉 SUCCESS: Simple Vue component rendered!', 'success');
                        log(`   Content: "${element.innerHTML.trim()}"`, 'success');
                    } else {
                        log('❌ FAILED: Simple Vue component did not render', 'error');
                        log(`   Defined: ${isDefined ? 'YES' : 'NO'}`, 'error');
                        log(`   Has content: ${hasContent ? 'YES' : 'NO'}`, 'error');
                    }
                }, 800);

            } catch (error) {
                log(`❌ ERROR: ${error.message}`, 'error');
                console.error('Simple Vue test error:', error);
            }
        }

        async function testPlainJS() {
            try {
                log('🚀 Testing Plain JS Component...', 'info');
                
                const sdk = await waitForSDK();
                if (!sdk) return;

                log('📦 Force loading zwe-plain-test-component...', 'info');
                await sdk.debug.forceLoadAndUpgrade('zwe-plain-test-component');
                log('✅ Component module loaded', 'success');

                const element = document.createElement('zwe-plain-test-component');
                const area = document.getElementById('component-area');
                area.innerHTML = '<h3>Plain JS Component Test:</h3>';
                area.appendChild(element);
                
                log('🏗️ Element created and added to DOM', 'info');

                setTimeout(() => {
                    const isDefined = customElements.get('zwe-plain-test-component');
                    const hasContent = element.innerHTML.trim().length > 0;
                    
                    if (isDefined && hasContent) {
                        log('✅ SUCCESS: Plain JS component rendered!', 'success');
                        log(`   Content: "${element.innerHTML.trim()}"`, 'success');
                    } else {
                        log('❌ FAILED: Plain JS component did not render', 'error');
                    }
                }, 500);

            } catch (error) {
                log(`❌ ERROR: ${error.message}`, 'error');
                console.error('Plain JS test error:', error);
            }
        }

        async function testAngelosForm() {
            try {
                log('🚀 Testing Angelos Create Form Component...', 'info');
                
                const sdk = await waitForSDK();
                if (!sdk) return;

                log('📦 Force loading zwe-angelos-create-form-v3...', 'info');
                await sdk.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3');
                log('✅ Component module loaded', 'success');

                const element = document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-key', 'test-entity');
                
                const area = document.getElementById('component-area');
                area.innerHTML = '<h3>Angelos Create Form Component Test:</h3>';
                area.appendChild(element);
                
                log('🏗️ Element created and added to DOM with entity-key="test-entity"', 'info');

                setTimeout(() => {
                    const isDefined = customElements.get('zwe-angelos-create-form-v3');
                    const hasContent = element.innerHTML.trim().length > 0;
                    const hasChildren = element.children.length > 0;
                    
                    if (isDefined && (hasContent || hasChildren)) {
                        log('🎉 SUCCESS: Angelos form component rendered!', 'success');
                        log(`   Has content: ${hasContent ? 'YES' : 'NO'} (${element.innerHTML.trim().length} chars)`, 'success');
                        log(`   Has children: ${hasChildren ? 'YES' : 'NO'} (${element.children.length})`, 'success');
                    } else {
                        log('❌ FAILED: Angelos form component did not render', 'error');
                        log(`   Defined: ${isDefined ? 'YES' : 'NO'}`, 'error');
                        log(`   Has content: ${hasContent ? 'YES' : 'NO'}`, 'error');
                        log(`   Has children: ${hasChildren ? 'YES' : 'NO'}`, 'error');
                    }
                }, 1500);

            } catch (error) {
                log(`❌ ERROR: ${error.message}`, 'error');
                console.error('Angelos form test error:', error);
            }
        }

        function clearComponents() {
            document.getElementById('component-area').innerHTML = '<em>Components will render here...</em>';
            log('🧹 Component area cleared', 'info');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '🧹 Log cleared...\n';
        }

        // Auto-run initialization checks
        document.addEventListener('DOMContentLoaded', async () => {
            log('📋 DOM loaded, checking SDK availability...', 'info');
            
            const sdk = await waitForSDK();
            if (sdk) {
                log('🎯 Ready for testing! Click buttons above to test components.', 'success');
            } else {
                log('⚠️ SDK not available - check console for errors', 'warning');
            }
        });
    </script>
</body>
</html>
