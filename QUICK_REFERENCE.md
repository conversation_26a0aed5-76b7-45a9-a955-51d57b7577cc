# Angelos SDK - Lazy Loading Quick Reference

## 🚀 Quick Start

### Development
```bash
# Development with hot reload
npm run dev

# Development with optimized build
npm run build && npm run serve:compressed
```

### Testing Lazy Loading
1. Open Network tab in DevTools
2. Load page - Monaco files should NOT appear
3. Interact with code fields - Monaco files should load
4. Verify ~1.2 MB total compressed size

## 📊 Bundle Sizes

| Component | Uncompressed | Compressed | Loading |
|-----------|--------------|------------|---------|
| Main Bundle | 1.05 MB | ~220 kB | Immediate |
| GDS Components | 3.18 MB | ~650 kB | Immediate |
| Icons | 1.29 MB | ~260 kB | Immediate |
| **Monaco Editor** | **6.32 MB** | **~1.1 MB** | **Lazy** |
| Vue Runtime | 393 kB | ~80 kB | Immediate |
| Lodash-ES | 686 kB | ~140 kB | Immediate |

**Total Initial: ~1.2 MB compressed (87% reduction)**

## 🔧 Key Files

```
src/
├── core/
│   └── monaco-lazy-loader.ts     # Monaco lazy loading logic
├── main.ts                       # Optimized entry point
└── components/
    └── atomic/form-controls/
        ├── CodeEditor.vue        # Monaco integration
        └── LazyCodeEditor.vue    # Lazy wrapper

vite.config.ts                    # Build optimization
serve-compressed.js               # Production server
package.json                      # Dependencies (lodash-es)
```

## ⚡ Commands

```bash
# Build & Test
npm run build-only              # Create optimized build
npm run serve:compressed        # Test with compression
npm run test                    # Run unit tests
npm run test:e2e               # Run E2E tests

# Development
npm run dev                     # Development server
npm run build:analyze          # Analyze bundle sizes

# Production
npm run build                   # Full production build
npm start                       # Start production server
```

## 🐛 Troubleshooting

### Monaco Not Loading
```bash
# Check Monaco chunk exists
ls dist/assets/monaco-editor-*.js

# Check console for errors
# Look for failed network requests
```

### Large Bundle Size
```bash
# Verify chunk separation
npm run build-only
ls -la dist/assets/

# Monaco should be separate chunk
# Main bundle should be ~1 MB
```

### Slow Loading
```bash
# Test with compression
npm run serve:compressed

# Check Network tab with throttling
# Should load in <1 second on Fast 3G
```

## 📝 Development Notes

### Adding Code Components
```typescript
// Use lazy loading for Monaco-dependent components
const CodeEditor = defineAsyncComponent(() => import('./CodeEditor.vue'));

// Regular components load immediately
import RegularComponent from './RegularComponent.vue';
```

### Bundle Monitoring
```bash
# After changes, verify:
npm run build-only

# Check that Monaco remains separate:
# dist/assets/monaco-editor-*.js should exist
# Main bundle should stay ~1 MB
```

### Performance Testing
```javascript
// In browser console
console.log('Monaco loaded:', window.MonacoLazyLoader?.getStats());

// Measure load time
performance.getEntriesByType('navigation')[0].loadEventEnd
```

## 🎯 Success Criteria

- [ ] Initial bundle < 1.5 MB compressed
- [ ] Monaco Editor in separate chunk
- [ ] Monaco loads only when code fields used
- [ ] All components work without Monaco
- [ ] Build completes without errors
- [ ] Tests pass

## 📚 Documentation

- `LAZY_LOADING_DOCUMENTATION.md` - Complete documentation
- `FINAL_OPTIMIZATION_REPORT.md` - Detailed optimization report
- `cypress/e2e/lazy-loading.cy.ts` - E2E test examples

---

**🎉 Result: 87% smaller initial bundle with Monaco Editor lazy loading!**
