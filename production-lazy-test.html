<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>🔥 Angelos SDK - Production Lazy Loading Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background: #f8f9fa;
            }
            .test-section {
                background: white;
                padding: 20px;
                margin: 15px 0;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .status {
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
                font-weight: bold;
            }
            .success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .info {
                background: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
            .button {
                padding: 10px 15px;
                margin: 5px;
                background: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            }
            .button:hover {
                background: #005a9e;
            }
            .component-box {
                border: 2px dashed #007acc;
                padding: 20px;
                margin: 15px 0;
                border-radius: 5px;
                min-height: 100px;
                background: #f8f9ff;
            }
        </style>
    </head>
    <body>
        <h1>🔥 Angelos SDK - Production Lazy Loading Test</h1>

        <div class="test-section">
            <h2>📊 Initial State</h2>
            <div id="initial-status" class="status info">Testing...</div>
            <p>This test verifies that components are only loaded on-demand, not eagerly.</p>
        </div>

        <div class="test-section">
            <h2>🧪 Component Addition Test</h2>
            <p>
                Click the button below to add a form component. The component's JavaScript should
                only load when the element is added to the DOM.
            </p>
            <button class="button" onclick="addFormComponent()">
                ➕ Add Form Component (Should Trigger Lazy Load)
            </button>
            <div id="form-container" class="component-box">
                <p><em>Component will appear here when added...</em></p>
            </div>
        </div>

        <div class="test-section">
            <h2>📈 Loading Statistics</h2>
            <div id="loading-stats" class="status info">No data yet...</div>
            <button class="button" onclick="updateStats()">🔄 Refresh Stats</button>
        </div>

        <div class="test-section">
            <h2>🔍 Network Activity</h2>
            <div id="network-activity" class="status info">
                <p>Open your browser's Network tab to see:</p>
                <ul>
                    <li>✅ Initial page load: Only angelos.min.js and basic vendor chunks</li>
                    <li>⚡ After clicking "Add Form Component": forms-*.js chunk should load</li>
                    <li>🎯 No unnecessary chunks should load until needed</li>
                </ul>
            </div>
        </div>

        <!-- Load the Angelos SDK -->
        <script type="module" src="./angelos.min.js"></script>

        <script>
            // Mock configuration
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };

            let testState = {
                sdkLoaded: false,
                componentsAdded: 0,
                loadEvents: []
            };

            // Track component loading events
            window.addEventListener('angelos:component-loaded', (event) => {
                const { componentName, loadTime } = event.detail;
                testState.loadEvents.push({
                    component: componentName,
                    time: loadTime,
                    timestamp: Date.now()
                });
                console.log(
                    `🎯 LAZY LOAD EVENT: ${componentName} loaded in ${loadTime.toFixed(2)}ms`
                );
                updateStats();
            });

            // Initial state check
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(checkInitialState, 1000);
            });

            function checkInitialState() {
                const statusDiv = document.getElementById('initial-status');

                if (window.AngelosSDK) {
                    testState.sdkLoaded = true;
                    const stats = window.AngelosSDK.getStats();

                    if (stats.loadedComponents === 0) {
                        statusDiv.className = 'status success';
                        statusDiv.innerHTML = `
                        ✅ <strong>PERFECT!</strong> SDK loaded but no components loaded yet.<br>
                        📊 Available: ${stats.availableComponents.length} components<br>
                        🎯 Loaded: ${stats.loadedComponents} components (exactly what we want!)
                    `;
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.innerHTML = `
                        ❌ <strong>ISSUE:</strong> Some components are already loaded!<br>
                        📊 Loaded: ${stats.loadedComponents} components<br>
                        📋 List: ${stats.loadedList.join(', ')}
                    `;
                    }
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ <strong>ERROR:</strong> AngelosSDK not loaded';
                }

                updateStats();
            }

            function addFormComponent() {
                testState.componentsAdded++;

                const container = document.getElementById('form-container');
                container.innerHTML = `
                <h3>📝 Form Component #${testState.componentsAdded}</h3>
                <p>Component should render below (may take a moment for lazy loading):</p>
                <zwe-angelos-create-form-v3
                    entity-id="lazy-test-${testState.componentsAdded}"
                    tenant-id="0"
                    show-form-actions="true"
                ></zwe-angelos-create-form-v3>
                <p><small>⏰ Added at: ${new Date().toLocaleTimeString()}</small></p>
            `;

                console.log(
                    `🧪 Added form component #${testState.componentsAdded} - watching for lazy load...`
                );

                // Update stats after a brief delay to catch the load event
                setTimeout(updateStats, 2000);
            }

            function updateStats() {
                const statsDiv = document.getElementById('loading-stats');

                if (!window.AngelosSDK) {
                    statsDiv.className = 'status error';
                    statsDiv.innerHTML = '❌ AngelosSDK not available';
                    return;
                }

                const stats = window.AngelosSDK.getStats();
                const customElementsCount = [
                    'zwe-angelos-create-form-v3',
                    'zwe-angelos-data-table-v3',
                    'zwe-angelos-details-view-v3',
                    'zwe-angelos-dynamic-view-v3'
                ].filter((name) => customElements.get(name)).length;

                let html = `
                <h4>📊 Current Statistics</h4>
                <p><strong>SDK Loaded:</strong> ${testState.sdkLoaded ? '✅ Yes' : '❌ No'}</p>
                <p><strong>Components Added to DOM:</strong> ${testState.componentsAdded}</p>
                <p><strong>Components Dynamically Loaded:</strong> ${stats.loadedComponents}/${stats.totalComponents}</p>
                <p><strong>Custom Elements Defined:</strong> ${customElementsCount}/4</p>
                <p><strong>Available Components:</strong> ${stats.availableComponents.join(', ')}</p>
                <p><strong>Loaded Components:</strong> ${stats.loadedList.join(', ') || 'None'}</p>
            `;

                if (testState.loadEvents.length > 0) {
                    html += `<h4>⚡ Load Events (${testState.loadEvents.length})</h4>`;
                    testState.loadEvents.forEach((event, index) => {
                        html += `<p>• ${event.component}: ${event.time.toFixed(2)}ms</p>`;
                    });
                }

                statsDiv.className = 'status info';
                statsDiv.innerHTML = html;
            }
        </script>
    </body>
</html>
