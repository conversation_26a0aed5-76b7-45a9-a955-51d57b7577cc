<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Vue Rendering Test</title>
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            localStorage.setItem('AT', 'test-token');
        </script>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
            button { padding: 10px 20px; margin: 5px; font-size: 16px; cursor: pointer; border: none; border-radius: 5px; background: #007acc; color: white; }
            .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; font-family: monospace; font-size: 12px; }
        </style>
    </head>
    <body>
        <h1>🧪 Vue Rendering Test</h1>
        
        <div class="test-section">
            <h2>Test 0: Plain Custom Element (No Vue)</h2>
            <p>This tests if custom elements work at all (without Vue).</p>
            <button onclick="testPlainComponent()">Load Plain Component</button>
            <div id="plain-test-area" style="margin: 20px 0; min-height: 100px; border: 2px dashed #999; padding: 20px;">
                Plain component will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test 1: Simple Vue Component</h2>
            <p>This tests if Vue rendering works at all in custom elements.</p>
            <button onclick="testSimpleComponent()">Load Simple Component</button>
            <div id="simple-test-area" style="margin: 20px 0; min-height: 100px; border: 2px dashed #ccc; padding: 20px;">
                Simple component will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Angelos Form Component</h2>
            <p>This tests the actual form component.</p>
            <button onclick="testFormComponent()">Load Form Component</button>
            <div id="form-test-area" style="margin: 20px 0; min-height: 200px; border: 2px dashed #007acc; padding: 20px;">
                Form component will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h2>Debug Output</h2>
            <div id="debug-output" class="status">
                Waiting for tests...
            </div>
        </div>

        <script>
            function log(message) {
                const output = document.getElementById('debug-output');
                output.innerHTML += `[${new Date().toLocaleTimeString()}] ${message}<br>`;
                console.log(message);
            }
            
            async function waitForSDK() {
                let attempts = 0;
                while (!window.AngelosSDK && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                return window.AngelosSDK;
            }
            
            async function testPlainComponent() {
                log('🔧 Testing plain custom element (no Vue)...');
                
                try {
                    const sdk = await waitForSDK();
                    if (!sdk) {
                        log('❌ SDK not available');
                        return;
                    }
                    
                    log('⚡ Force loading plain test component...');
                    
                    try {
                        await sdk.debug.forceLoadAndUpgrade('zwe-plain-test-component');
                        log('✅ Plain component force load completed');
                    } catch (error) {
                        log(`❌ Plain component force load failed: ${error.message}`);
                        console.error('Plain component error:', error);
                        return;
                    }
                    
                    log('🏗️ Creating plain component element...');
                    const element = document.createElement('zwe-plain-test-component');
                    
                    const container = document.getElementById('plain-test-area');
                    container.innerHTML = '';
                    container.appendChild(element);
                    
                    log('✅ Plain component added to DOM');
                    
                    // Check result after a delay
                    setTimeout(() => {
                        const isDefined = customElements.get('zwe-plain-test-component');
                        const hasContent = element.innerHTML.length > 0;
                        const hasChildren = element.children.length > 0;
                        
                        log(`🔍 Plain component result:`);
                        log(`   - Defined: ${isDefined ? 'YES' : 'NO'}`);
                        log(`   - Constructor: ${element.constructor.name}`);
                        log(`   - Has content: ${hasContent ? 'YES' : 'NO'} (${element.innerHTML.length} chars)`);
                        log(`   - Has children: ${hasChildren ? 'YES' : 'NO'} (${element.children.length})`);
                        
                        if (isDefined && hasContent) {
                            log('🎉 Plain custom element is WORKING! (Problem is with Vue)');
                        } else if (isDefined && !hasContent) {
                            log('⚠️ Plain component defined but not rendering');
                        } else {
                            log('❌ Plain component not defined');
                        }
                    }, 1000);
                    
                } catch (error) {
                    log(`❌ Error testing plain component: ${error.message}`);
                    console.error('Plain test error:', error);
                }
            }
            
            async function testSimpleComponent() {
                log('🧪 Testing simple Vue component...');
                
                try {
                    const sdk = await waitForSDK();
                    if (!sdk) {
                        log('❌ SDK not available');
                        return;
                    }
                    
                    log('⚡ Force loading simple test component...');
                    
                    // Add error handling for the force load
                    try {
                        await sdk.debug.forceLoadAndUpgrade('zwe-test-simple-component');
                        log('✅ Force load completed successfully');
                    } catch (error) {
                        log(`❌ Force load failed: ${error.message}`);
                        console.error('Force load error:', error);
                        return;
                    }
                    
                    log('🏗️ Creating simple component element...');
                    const element = document.createElement('zwe-test-simple-component');
                    
                    const container = document.getElementById('simple-test-area');
                    container.innerHTML = '';
                    container.appendChild(element);
                    
                    log('✅ Simple component added to DOM');
                    
                    // Check result after a delay
                    setTimeout(() => {
                        const isDefined = customElements.get('zwe-test-simple-component');
                        const hasContent = element.innerHTML.length > 0;
                        const hasChildren = element.children.length > 0;
                        
                        log(`🔍 Simple component result:`);
                        log(`   - Defined: ${isDefined ? 'YES' : 'NO'}`);
                        log(`   - Constructor: ${element.constructor.name}`);
                        log(`   - Has content: ${hasContent ? 'YES' : 'NO'} (${element.innerHTML.length} chars)`);
                        log(`   - Has children: ${hasChildren ? 'YES' : 'NO'} (${element.children.length})`);
                        
                        // Check shadow DOM
                        if (element.shadowRoot) {
                            log(`   - Shadow DOM: YES (${element.shadowRoot.children.length} children, ${element.shadowRoot.innerHTML.length} chars)`);
                        } else {
                            log(`   - Shadow DOM: NO`);
                        }
                        
                        if (isDefined && (hasContent || element.shadowRoot)) {
                            log('🎉 Simple Vue component is WORKING!');
                        } else if (isDefined && !hasContent && !element.shadowRoot) {
                            log('⚠️ Component defined but not rendering - Vue issue');
                        } else {
                            log('❌ Component not defined');
                        }
                    }, 2000);
                    
                } catch (error) {
                    log(`❌ Error testing simple component: ${error.message}`);
                    console.error('Test error:', error);
                }
            }
            
            async function testFormComponent() {
                log('📝 Testing Angelos form component...');
                
                try {
                    const sdk = await waitForSDK();
                    if (!sdk) {
                        log('❌ SDK not available');
                        return;
                    }
                    
                    log('⚡ Force loading form component...');
                    await sdk.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3');
                    
                    log('🏗️ Creating form component element...');
                    const element = document.createElement('zwe-angelos-create-form-v3');
                    element.setAttribute('entity-id', 'test-entity');
                    element.setAttribute('tenant-id', '0');
                    element.setAttribute('show-form-actions', 'true');
                    
                    const container = document.getElementById('form-test-area');
                    container.innerHTML = '';
                    container.appendChild(element);
                    
                    log('✅ Form component added to DOM');
                    
                    // Check result after a delay
                    setTimeout(() => {
                        const isDefined = customElements.get('zwe-angelos-create-form-v3');
                        const hasContent = element.innerHTML.length > 0;
                        const hasChildren = element.children.length > 0;
                        
                        log(`🔍 Form component result:`);
                        log(`   - Defined: ${isDefined ? 'YES' : 'NO'}`);
                        log(`   - Constructor: ${element.constructor.name}`);
                        log(`   - Has content: ${hasContent ? 'YES' : 'NO'} (${element.innerHTML.length} chars)`);
                        log(`   - Has children: ${hasChildren ? 'YES' : 'NO'} (${element.children.length})`);
                        
                        // Check for shadow DOM
                        if (element.shadowRoot) {
                            log(`   - Shadow DOM: YES (${element.shadowRoot.children.length} children, ${element.shadowRoot.innerHTML.length} chars)`);
                        } else {
                            log(`   - Shadow DOM: NO`);
                        }
                        
                        if (isDefined && (hasContent || element.shadowRoot)) {
                            log('🎉 Form component is working!');
                        } else if (isDefined && !hasContent && !element.shadowRoot) {
                            log('⚠️ Form component defined but not rendering - checking for errors...');
                            
                            // Additional debugging
                            const vueInstance = element.__vueParentComponent || element.__vue__;
                            log(`   - Vue instance: ${vueInstance ? 'YES' : 'NO'}`);
                            
                            if (window.__VUE_ERRORS__) {
                                log(`   - Vue errors: ${window.__VUE_ERRORS__.length}`);
                            }
                        } else {
                            log('❌ Form component not defined');
                        }
                    }, 3000);
                    
                } catch (error) {
                    log(`❌ Error testing form component: ${error.message}`);
                }
            }
            
            // Initialize
            document.addEventListener('DOMContentLoaded', () => {
                log('📄 Page loaded, ready for Vue rendering tests');
            });
        </script>
    </body>
</html>
