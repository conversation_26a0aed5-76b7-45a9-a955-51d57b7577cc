<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Load Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🧪 Direct Component Load Test</h1>

    <div class="test-container">
        <h3>Test Controls</h3>
        <button onclick="loadFormsDirectly()">Load Forms Entry Directly</button>
        <button onclick="testCreateForm()">Test Create Form</button>
        <button onclick="checkElements()">Check Custom Elements</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-container">
        <h3>Component Test Area</h3>
        <div id="component-area" style="border: 2px dashed #ccc; padding: 20px; min-height: 100px;">
            <p>Components will appear here...</p>
        </div>
    </div>

    <div class="test-container">
        <h3>Debug Log</h3>
        <div class="log" id="debug-log">Ready for testing...</div>
    </div>

    <script>
        // Global configuration
        window.__zeta__ = {
            angelos: {
                SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                OMS_SERVICE_BASE_URL: 'http://localhost:5000'
            }
        };
        window.__HERCULES__ = {
            $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
            $store: {}
        };

        // Enhanced logging
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Override console to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            log(args.join(' '), 'info');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            log(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            log(args.join(' '), 'warning');
            originalWarn.apply(console, args);
        };

        // Test functions
        async function loadFormsDirectly() {
            log('🔄 Loading forms entry directly...');
            
            try {
                // Import the forms entry directly
                const formsModule = await import('./dist/assets/component-forms-DGYDQZVQ.js');
                log('✅ Forms module loaded successfully');
                log('📦 Module contents: ' + Object.keys(formsModule).join(', '));
                
                // Check if custom elements are now defined
                setTimeout(() => {
                    checkElements();
                }, 500);
                
            } catch (error) {
                log('❌ Failed to load forms module: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }

        function testCreateForm() {
            log('🧪 Testing create form component...');
            
            const area = document.getElementById('component-area');
            const element = document.createElement('zwe-angelos-create-form-v3');
            
            // Set attributes
            element.setAttribute('entity-id', 'test-entity');
            element.setAttribute('tenant-id', 'test-tenant');
            element.style.cssText = 'border: 1px solid #007bff; padding: 15px; margin: 10px 0; display: block;';
            
            area.appendChild(element);
            log('📦 Create form element added to DOM');
            
            // Check upgrade status
            setTimeout(() => {
                const constructor = customElements.get('zwe-angelos-create-form-v3');
                if (constructor) {
                    log('✅ Custom element constructor exists');
                    
                    if (element instanceof constructor) {
                        log('✅ Element is properly upgraded');
                    } else {
                        log('⚠️ Element not upgraded, attempting manual upgrade...');
                        customElements.upgrade(element);
                        
                        setTimeout(() => {
                            if (element instanceof constructor) {
                                log('✅ Manual upgrade successful');
                            } else {
                                log('❌ Manual upgrade failed');
                            }
                        }, 200);
                    }
                } else {
                    log('❌ Custom element not defined');
                }
                
                // Check element content
                log('🔍 Element innerHTML length: ' + element.innerHTML.length);
                if (element.innerHTML.length > 0) {
                    log('✅ Element has content');
                } else {
                    log('⚠️ Element appears empty');
                }
                
            }, 1000);
        }

        function checkElements() {
            log('🔍 Checking custom element registry...');
            
            const components = [
                'zwe-angelos-create-form-v3',
                'zwe-angelos-update-form-v3',
                'zwe-angelos-data-table-v3',
                'zwe-angelos-details-view-v3',
                'zwe-angelos-dynamic-view-v3'
            ];
            
            let definedCount = 0;
            components.forEach(name => {
                const constructor = customElements.get(name);
                if (constructor) {
                    log(`✅ ${name} is defined`);
                    definedCount++;
                } else {
                    log(`❌ ${name} is NOT defined`);
                }
            });
            
            log(`📊 Total defined: ${definedCount}/${components.length}`);
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = 'Log cleared...\n';
        }

        log('🚀 Direct load test initialized');
    </script>
</body>
</html>
