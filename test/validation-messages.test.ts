import { describe, it, expect } from 'vitest';
import { rulesToAsyncValidatorSchema } from '../src/core/utils';

describe('Validation Messages', () => {
    it('should use custom message when provided', () => {
        const field = {
            label: 'Email Address',
            type: 'email'
        };

        const rules = {
            required: {
                value: true,
                message: 'Custom required message'
            }
        };

        const result = rulesToAsyncValidatorSchema(rules, field);

        expect(result[0].message).toBe('Custom required message');
    });

    it('should use field label in default message when no custom message is provided and field has a label', () => {
        const field = {
            label: 'Email Address',
            type: 'email'
        };

        const rules = {
            required: true
        };

        const result = rulesToAsyncValidatorSchema(rules, field);

        expect(result[0].message).toBe('Email Address is required');
    });

    it('should use fallback text in default message when no label or custom message is provided', () => {
        const field = {
            type: 'email'
        };

        const rules = {
            required: true
        };

        const result = rulesToAsyncValidatorSchema(rules, field);

        expect(result[0].message).toBe('This field is required');
    });

    it('should generate appropriate default messages for different validation rules', () => {
        const field = {
            label: 'Password',
            type: 'password'
        };

        const rules = {
            required: true,
            min: 8,
            max: 20
        };

        const result = rulesToAsyncValidatorSchema(rules, field);

        expect(result[0].message).toBe('Password is required');
        expect(result[1].message).toBe('Password must be at least 8 characters');
        expect(result[2].message).toBe('Password cannot exceed 20 characters');
    });
});
