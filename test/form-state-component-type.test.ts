import { describe, it, expect } from 'vitest';
import { useComponentStore } from '../src/composable/useComponentStore';

describe('Form State Component Type Isolation', () => {
    it('should create separate state instances for each call, ensuring complete isolation', () => {
        // Create form state instances - each call creates a new isolated instance
        const formState1 = useComponentStore('test-entity', 'tenant-1', 'data-table');
        const formState2 = useComponentStore('test-entity', 'tenant-1', 'details-view');
        const formState3 = useComponentStore('test-entity', 'tenant-1', 'create-form');

        // Set different values in each instance
        formState1.updateFormModel('name', 'Table Form');
        formState2.updateFormModel('name', 'Details Form');
        formState3.updateFormModel('name', 'Create Form');

        // Verify that each component type maintains its own state
        expect(formState1.getFormModelFromPath('name')).toBe('Table Form');
        expect(formState2.getFormModelFromPath('name')).toBe('Details Form');
        expect(formState3.getFormModelFromPath('name')).toBe('Create Form');

        // Verify they are different objects - complete isolation
        expect(formState1).not.toBe(formState2);
        expect(formState2).not.toBe(formState3);
        expect(formState1).not.toBe(formState3);
    });

    it('should create new instance for each call, even with same parameters', () => {
        const formState1 = useComponentStore('test-entity', 'tenant-1', 'data-table');
        const formState2 = useComponentStore('test-entity', 'tenant-1', 'data-table');

        // Should create different instances for complete isolation
        expect(formState1).not.toBe(formState2);

        // Each instance should have its own isolated state
        formState1.updateFormModel('name', 'First Instance');
        formState2.updateFormModel('name', 'Second Instance');

        expect(formState1.getFormModelFromPath('name')).toBe('First Instance');
        expect(formState2.getFormModelFromPath('name')).toBe('Second Instance');
    });

    it('should handle different tenant IDs correctly with complete isolation', () => {
        const formState1 = useComponentStore('entity-1', 'tenant-1', 'data-table');
        const formState2 = useComponentStore('entity-1', 'tenant-2', 'data-table');

        // Should create separate instances for different tenants
        expect(formState1).not.toBe(formState2);

        formState1.updateFormModel('name', 'Tenant 1');
        formState2.updateFormModel('name', 'Tenant 2');

        expect(formState1.getFormModelFromPath('name')).toBe('Tenant 1');
        expect(formState2.getFormModelFromPath('name')).toBe('Tenant 2');
    });

    it('should clear all instances when creating new ones (isolated approach)', () => {
        // Create multiple instances
        const formState1 = useComponentStore('test-entity', 'tenant-1', 'data-table');
        const formState2 = useComponentStore('test-entity', 'tenant-1', 'details-view');

        // Set values
        formState1.updateFormModel('name', 'Table Form');
        formState2.updateFormModel('name', 'Details Form');

        // Create new instances - should be completely new due to isolated approach
        const newFormState1 = useComponentStore('test-entity', 'tenant-1', 'data-table');
        const newFormState2 = useComponentStore('test-entity', 'tenant-1', 'details-view');

        // New instances should always be different from previous ones
        expect(newFormState1).not.toBe(formState1);
        expect(newFormState2).not.toBe(formState2);

        // New instances should have clean state
        expect(newFormState1.getFormModelFromPath('name')).toBe(null);
        expect(newFormState2.getFormModelFromPath('name')).toBe(null);
    });

    it('should handle different entity IDs correctly', () => {
        const formState1 = useComponentStore('entity-1', 'tenant-1', 'data-table');
        const formState2 = useComponentStore('entity-2', 'tenant-1', 'data-table');

        // Should create separate instances for different entities
        expect(formState1).not.toBe(formState2);

        formState1.updateFormModel('name', 'Entity 1');
        formState2.updateFormModel('name', 'Entity 2');

        expect(formState1.getFormModelFromPath('name')).toBe('Entity 1');
        expect(formState2.getFormModelFromPath('name')).toBe('Entity 2');
    });

    it('should handle component types with special characters', () => {
        const formState1 = useComponentStore('test-entity', 'tenant-1', 'custom-form-v2');
        const formState2 = useComponentStore('test-entity', 'tenant-1', 'admin_panel.create');

        // Should create separate instances
        expect(formState1).not.toBe(formState2);

        formState1.updateFormModel('type', 'custom');
        formState2.updateFormModel('type', 'admin');

        expect(formState1.getFormModelFromPath('type')).toBe('custom');
        expect(formState2.getFormModelFromPath('type')).toBe('admin');
    });
});
