import { describe, it, expect } from 'vitest';
import { rulesToAsyncValidatorSchema } from '../src/core/utils';

describe('Email validation', () => {
    it('should add email validation when field type is email', () => {
        const field = {
            type: 'email',
            label: 'Email Address'
        };

        const rules = {};

        const result = rulesToAsyncValidatorSchema(rules, field);

        // Check if email validation rule was added
        expect(result.some((rule) => rule.type === 'email')).toBe(true);
    });

    it('should not override existing email validation rule', () => {
        const field = {
            type: 'email',
            label: 'Email Address'
        };

        const rules = {
            email: {
                value: true,
                message: 'Please enter a valid email address'
            }
        };

        const result = rulesToAsyncValidatorSchema(rules, field);

        // Check if email validation rule was added with custom message
        const emailRule = result.find((rule) => rule.type === 'email');
        expect(emailRule).toBeDefined();
        expect(emailRule?.message).toBe('Please enter a valid email address');
    });

    it('should not add email validation when field type is not email', () => {
        const field = {
            type: 'text',
            label: 'Name'
        };

        const rules = {};

        const result = rulesToAsyncValidatorSchema(rules, field);

        // Check that no email validation rule was added
        expect(result.some((rule) => rule.type === 'email')).toBe(false);
    });

    it('should handle null rules', () => {
        const field = {
            type: 'email',
            label: 'Email Address'
        };

        const result = rulesToAsyncValidatorSchema(undefined, field);

        // Check if email validation rule was added
        expect(result.some((rule) => rule.type === 'email')).toBe(true);
    });
});
