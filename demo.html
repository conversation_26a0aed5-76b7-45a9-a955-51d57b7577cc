<!doctype html>
<html lang="en">
    <head>
        <link rel="icon" href="/favicon.ico" />
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'unsafe-inline' 'self'; connect-src 'self' https://*.zetaapps.in/ "> -->
        <title>Document</title>
        <!-- <script src="https://hercules-assets.mum1-pp.zetaapps.in/angelos-sdk/3.0.0-beta.0/angelos.min.js" type="module"></script> -->
        <script src="./dist/angelos.min.js" type="module"></script>
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.__BUSINESS_COMPONENTS_BASE_URL__ = 'https://hercules-assets.mum1-pp.zetaapps.in';
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
            const token = '';
            localStorage.setItem('@zeta::authToken', token);
            localStorage.setItem('authToken', token);
            localStorage.setItem('AT', token);
        </script>
        <!-- <link href="dist/style.css" rel="stylesheet"></link> -->
    </head>
    <body>
        <zwe-angelos-details-view-v3
            entity-id="business-comp-example-v3-search"
            tenant-id="0"
            context='{"id":"12","tenantId": "600309","aetherData":{"id":"12345","vboID":"67890","shortCode":"ABC123","name":"Sample Name","description":"This is a sample description.","status":"active","createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"free":[{"id":"12","name":"Angelos","age":12,"address":"Athens","nested":{"id":"12","name":"Angelos","age":12,"address":"Athens"},"array":[{"id":"12","name":"Angelos","age":12,"address":"Athens"},{"id":"12","name":"Angelos","age":12,"address":"Athens"}],"arrayofstrings":["a","b","c"]},{"id":"12","name":"Angelos","age":12,"address":"Athens","nested":{"id":"12","name":"Angelos","age":12,"address":"Athens"},"array":[{"id":"12","name":"Angelos","age":12,"address":"Athens"},{"id":"12","name":"Angelos","age":12,"address":"Athens"}],"arrayofstrings":["a","b","c"]}],"test":{"id":"56","name":"Abel","age":12,"address":"Switzerland","nested":{"id":"12","name":"Abel","age":12,"address":"Switzerland"}},"code":"12345","tableData":[{"category":"Category 1","limitType":"Limit Type 1","limitValue":"Limit Value 1"},{"category":"Category 2","limitType":"Limit Type 2","limitValue":"Limit Value 2"},{"category":"Category 3","limitType":"Limit Type 3","limitValue":"Limit Value 3"},{"category":"Category 4","limitType":"Limit Type 4","limitValue":"Limit Value 4"},{"category":"Category 5","limitType":"Limit Type 5","limitValue":"Limit Value 5"},{"category":"Category 6","limitType":"Limit Type 6","limitValue":"Limit Value 6"},{"category":"Category 7","limitType":"Limit Type 7","limitValue":"Limit Value 7"},{"category":"Category 8","limitType":"Limit Type 8","limitValue":"Limit Value 8"},{"category":"Category 9","limitType":"Limit Type 9","limitValue":"Limit Value 9"},{"category":"Category 10","limitType":"Limit Type 10","limitValue":"Limit Value 10"},{"category":"Category 11","limitType":"Limit Type 11","limitValue":"Limit Value 11"}],"first":{"templateCode":"098","second":{"address":"Somewhere in the world","third":{"city":"Athens"}}},"showField":false}'
        />
        <zwe-angelos-create-form-v3
            entity-id="business-comp-example-v3-search"
            tenant-id="0"
            context='{
        "metadata": {
          "id": "aether.feature.feature-flag-configs",
          "name": "featureFlagConfigs",
          "description": "Sample Feature",
          "requester": {
            "module": "aether",
            "moduleVersion": "0.0.1"
          },
          "tenantId": "600309",
          "tenantCode": "lsg"
        },
        "state": "ENABLED",
        "viewConfig": {
          "displayName": "feature-flags"
        }
      }'
        />
    </body>
</html>
