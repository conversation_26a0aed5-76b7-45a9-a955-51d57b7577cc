<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Manual Component Load Test</title>
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            localStorage.setItem('AT', 'test-token');
        </script>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .controls { margin: 20px 0; }
            button { padding: 10px 20px; margin: 5px; font-size: 16px; cursor: pointer; border: none; border-radius: 5px; }
            .primary { background: #007acc; color: white; }
            .success { background: #28a745; color: white; }
            .warning { background: #ffc107; color: black; }
            .component-area { border: 3px solid #007acc; padding: 20px; margin: 20px 0; min-height: 200px; background: #f8f9fa; }
            .status { margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 5px; font-family: monospace; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 Manual Component Loading Test</h1>
            <p>Since auto-detection isn't working, let's test manual loading...</p>
            
            <div class="controls">
                <button class="primary" onclick="manualLoadAndCreate()">🚀 Manual Load & Create</button>
                <button class="success" onclick="checkStatus()">📊 Check Status</button>
                <button class="warning" onclick="clearComponent()">🗑️ Clear</button>
            </div>
            
            <div class="status" id="status">
                Ready to test manual loading...
            </div>
            
            <div class="component-area" id="component-area">
                <p style="text-align: center; color: #666;">Component will appear here after manual loading...</p>
            </div>
        </div>

        <script>
            function updateStatus(message) {
                document.getElementById('status').textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                console.log(message);
            }
            
            async function manualLoadAndCreate() {
                updateStatus('🚀 Starting manual load and create process...');
                
                try {
                    // Step 1: Wait for SDK
                    updateStatus('⏳ Waiting for Angelos SDK...');
                    
                    let attempts = 0;
                    while (!window.AngelosSDK && attempts < 50) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        attempts++;
                    }
                    
                    if (!window.AngelosSDK) {
                        updateStatus('❌ SDK not found after 5 seconds');
                        return;
                    }
                    
                    updateStatus('✅ SDK found, checking stats...');
                    const initialStats = window.AngelosSDK.getStats();
                    updateStatus(`📊 Initial stats: ${initialStats.loadedComponents}/${initialStats.totalComponents} loaded`);
                    
                    // Step 2: Force load the component
                    updateStatus('⚡ Force loading form component...');
                    
                    if (!window.AngelosSDK.debug) {
                        updateStatus('❌ SDK debug methods not available');
                        return;
                    }
                    
                    await window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3');
                    updateStatus('✅ Force load completed');
                    
                    // Step 3: Check if component is now defined
                    const isDefined = customElements.get('zwe-angelos-create-form-v3');
                    updateStatus(`🔍 Component defined: ${isDefined ? 'YES' : 'NO'}`);
                    
                    if (!isDefined) {
                        updateStatus('❌ Component still not defined after force load');
                        return;
                    }
                    
                    // Step 4: Create and add the component
                    updateStatus('🏗️ Creating component element...');
                    
                    const element = document.createElement('zwe-angelos-create-form-v3');
                    element.setAttribute('entity-id', 'test-entity');
                    element.setAttribute('tenant-id', '0');
                    element.setAttribute('show-form-actions', 'true');
                    element.id = 'manual-test-form';
                    
                    const container = document.getElementById('component-area');
                    container.innerHTML = '';
                    container.appendChild(element);
                    
                    updateStatus('✅ Component element created and added to DOM');
                    
                    // Step 5: Wait and check result
                    updateStatus('⏳ Waiting for component to render...');
                    
                    setTimeout(() => {
                        checkComponentResult(element);
                    }, 3000);
                    
                } catch (error) {
                    updateStatus(`❌ Error during manual load: ${error.message}`);
                }
            }
            
            function checkComponentResult(element) {
                try {
                    const hasChildren = element.children.length > 0;
                    const hasContent = element.innerHTML.trim().length > 0;
                    const constructorName = element.constructor.name;
                    
                    updateStatus(`📊 Final result: Constructor=${constructorName}, Children=${element.children.length}, Content=${element.innerHTML.length} chars`);
                    
                    if (hasChildren && hasContent) {
                        updateStatus('🎉 SUCCESS! Component is working correctly!');
                    } else if (constructorName !== 'HTMLElement') {
                        updateStatus('⚠️ Component defined but not rendering content (Vue issue?)');
                        
                        // Check for shadow DOM
                        if (element.shadowRoot) {
                            updateStatus(`🔍 Shadow DOM found: ${element.shadowRoot.children.length} children, ${element.shadowRoot.innerHTML.length} chars`);
                        } else {
                            updateStatus('🔍 No shadow DOM found');
                        }
                    } else {
                        updateStatus('❌ Component not properly upgraded');
                    }
                    
                } catch (error) {
                    updateStatus(`❌ Error checking result: ${error.message}`);
                }
            }
            
            function checkStatus() {
                try {
                    if (!window.AngelosSDK) {
                        updateStatus('❌ Angelos SDK not available');
                        return;
                    }
                    
                    const stats = window.AngelosSDK.getStats();
                    updateStatus(`📊 Current stats: ${stats.loadedComponents}/${stats.totalComponents} loaded: [${stats.loadedList.join(', ')}]`);
                    
                    const isDefined = customElements.get('zwe-angelos-create-form-v3');
                    updateStatus(`🔍 Form component defined: ${isDefined ? 'YES' : 'NO'}`);
                    
                    const elements = document.querySelectorAll('zwe-angelos-create-form-v3');
                    updateStatus(`📝 Form elements in DOM: ${elements.length}`);
                    
                    elements.forEach((el, index) => {
                        updateStatus(`   Element ${index + 1}: ${el.constructor.name}, ${el.children.length} children, ${el.innerHTML.length} chars`);
                    });
                    
                } catch (error) {
                    updateStatus(`❌ Error checking status: ${error.message}`);
                }
            }
            
            function clearComponent() {
                document.getElementById('component-area').innerHTML = '<p style="text-align: center; color: #666;">Component cleared...</p>';
                updateStatus('🗑️ Component area cleared');
            }
            
            // Initialize
            document.addEventListener('DOMContentLoaded', () => {
                updateStatus('📄 Page loaded, ready for manual testing');
            });
        </script>
    </body>
</html>
