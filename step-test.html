<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Step by Step Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .step { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
            .success { background: #d4edda; border-color: #c3e6cb; }
            .error { background: #f8d7da; border-color: #f5c6cb; }
            .loading { background: #fff3cd; border-color: #ffeaa7; }
            .pending { background: #f8f9fa; border-color: #dee2e6; }
        </style>
    </head>
    <body>
        <h1>🧪 Step by Step Component Test</h1>
        
        <div id="step1" class="step pending">
            <strong>Step 1:</strong> Load main JS bundle
        </div>
        
        <div id="step2" class="step pending">
            <strong>Step 2:</strong> Check for AngelosSDK
        </div>
        
        <div id="step3" class="step pending">
            <strong>Step 3:</strong> Create component element
        </div>
        
        <div id="step4" class="step pending">
            <strong>Step 4:</strong> Check component status
        </div>
        
        <hr>
        
        <div id="component-area" style="border: 2px solid #007acc; padding: 20px; margin: 20px 0; min-height: 150px; background: #f9f9f9;">
            <p style="color: #666; text-align: center;">Component will appear here...</p>
        </div>
        
        <hr>
        
        <div id="logs" style="font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 15px; background: #f5f5f5;">
            <div style="color: #666;">[Starting test...]</div>
        </div>

        <script>
            let stepCount = 0;
            
            function updateStep(stepId, status, message) {
                const step = document.getElementById(stepId);
                step.className = `step ${status}`;
                const strongText = step.querySelector('strong').textContent;
                step.innerHTML = `<strong>${strongText}</strong> ${message}`;
            }
            
            function log(message, type = 'info') {
                const logs = document.getElementById('logs');
                const time = new Date().toLocaleTimeString();
                const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
                logs.innerHTML += `<div style="color: ${color};">[${time}] ${message}</div>`;
                logs.scrollTop = logs.scrollHeight;
                console.log(`[${time}] ${message}`);
            }
            
            // Catch errors
            window.addEventListener('error', (e) => {
                log(`❌ JavaScript Error: ${e.message} (${e.filename}:${e.lineno})`, 'error');
            });
            
            window.addEventListener('unhandledrejection', (e) => {
                log(`❌ Promise Rejection: ${e.reason}`, 'error');
            });
            
            // Start the test
            log('🚀 Starting component loading test...');
            
            // Set up basic configuration first
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            localStorage.setItem('AT', 'test-token');
            
            log('⚙️ Configuration set up');
        </script>
        
        <!-- Load the main bundle in a separate script to catch loading issues -->
        <script>
            // Step 1: Load main bundle
            updateStep('step1', 'loading', 'Loading main bundle...');
            log('📦 Loading ./dist/angelos.min.js...');
            
            const script = document.createElement('script');
            script.type = 'module';
            script.src = './dist/angelos.min.js';
            
            script.onload = function() {
                log('✅ Main bundle loaded successfully', 'success');
                updateStep('step1', 'success', 'Main bundle loaded successfully ✅');
                
                // Wait a moment for initialization
                setTimeout(checkForSDK, 500);
            };
            
            script.onerror = function(e) {
                log('❌ Failed to load main bundle', 'error');
                updateStep('step1', 'error', 'Failed to load main bundle ❌');
            };
            
            document.head.appendChild(script);
            
            function checkForSDK() {
                // Step 2: Check for SDK
                updateStep('step2', 'loading', 'Checking for AngelosSDK...');
                log('🔍 Checking for window.AngelosSDK...');
                
                let attempts = 0;
                const maxAttempts = 30; // 3 seconds
                
                function pollForSDK() {
                    attempts++;
                    
                    if (window.AngelosSDK) {
                        log('✅ AngelosSDK found!', 'success');
                        updateStep('step2', 'success', 'AngelosSDK found ✅');
                        
                        // Log SDK stats
                        try {
                            const stats = window.AngelosSDK.getStats();
                            log(`📊 SDK Stats: ${JSON.stringify(stats)}`);
                        } catch (e) {
                            log(`⚠️ Could not get SDK stats: ${e.message}`);
                        }
                        
                        // Proceed to component test
                        setTimeout(createComponent, 500);
                        
                    } else if (attempts >= maxAttempts) {
                        log('❌ AngelosSDK not found after 3 seconds', 'error');
                        updateStep('step2', 'error', 'AngelosSDK timeout ❌');
                    } else {
                        log(`⏳ Attempt ${attempts}/${maxAttempts}: SDK not ready yet...`);
                        setTimeout(pollForSDK, 100);
                    }
                }
                
                pollForSDK();
            }
            
            function createComponent() {
                // Step 3: Create component
                updateStep('step3', 'loading', 'Creating component element...');
                log('🏗️ Creating zwe-angelos-create-form-v3 element...');
                
                try {
                    const element = document.createElement('zwe-angelos-create-form-v3');
                    element.setAttribute('entity-id', 'test-entity');
                    element.setAttribute('tenant-id', '0');
                    element.setAttribute('show-form-actions', 'true');
                    element.id = 'test-form-component';
                    
                    log('📝 Element created with attributes');
                    
                    // Add to DOM
                    const container = document.getElementById('component-area');
                    container.innerHTML = ''; // Clear placeholder text
                    container.appendChild(element);
                    
                    log('🎯 Element added to DOM');
                    updateStep('step3', 'success', 'Component element created ✅');
                    
                    // Wait and check component status
                    setTimeout(() => checkComponentStatus(element), 3000);
                    
                } catch (error) {
                    log(`❌ Failed to create component: ${error.message}`, 'error');
                    updateStep('step3', 'error', `Failed to create component: ${error.message}`);
                }
            }
            
            function checkComponentStatus(element) {
                // Step 4: Check component status
                updateStep('step4', 'loading', 'Checking component status...');
                log('🔍 Checking component status...');
                
                try {
                    const customElementDefined = customElements.get('zwe-angelos-create-form-v3');
                    const hasChildren = element.children.length > 0;
                    const hasContent = element.innerHTML.trim().length > 0;
                    const constructorName = element.constructor.name;
                    
                    log(`🔍 Component analysis:`);
                    log(`   - Custom element defined: ${customElementDefined ? 'YES' : 'NO'}`);
                    log(`   - Constructor: ${constructorName}`);
                    log(`   - Children count: ${element.children.length}`);
                    log(`   - Content length: ${element.innerHTML.length} characters`);
                    log(`   - Is connected: ${element.isConnected}`);
                    
                    if (customElementDefined && hasChildren && hasContent) {
                        log('✅ Component is working correctly!', 'success');
                        updateStep('step4', 'success', 'Component is working ✅');
                    } else if (!customElementDefined) {
                        log('❌ Custom element not defined - chunk may not have loaded', 'error');
                        updateStep('step4', 'error', 'Custom element not defined ❌');
                    } else if (customElementDefined && !hasContent) {
                        log('⚠️ Custom element defined but no content rendered', 'error');
                        updateStep('step4', 'error', 'Component defined but empty ⚠️');
                    } else {
                        log('⚠️ Component in unknown state', 'error');
                        updateStep('step4', 'error', 'Unknown state ⚠️');
                    }
                    
                    // Try manual loading since auto-detection failed
                    if (!customElementDefined) {
                        log('🔧 Custom element not defined, trying manual load...');
                        
                        if (window.AngelosSDK && window.AngelosSDK.debug) {
                            log('⚡ Using SDK debug.forceLoadAndUpgrade...');
                            window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3')
                                .then(() => {
                                    log('✅ Manual load completed, checking result...');
                                    setTimeout(() => {
                                        const nowDefined = customElements.get('zwe-angelos-create-form-v3');
                                        const newContent = element.innerHTML.length;
                                        const newChildren = element.children.length;
                                        
                                        log(`📊 After manual load:`);
                                        log(`   - Now defined: ${nowDefined ? 'YES' : 'NO'}`);
                                        log(`   - Children: ${newChildren}`);
                                        log(`   - Content: ${newContent} characters`);
                                        
                                        if (nowDefined && newContent > 0) {
                                            log('🎉 Manual load SUCCESS! Component now working.', 'success');
                                            updateStep('step4', 'success', 'Component working after manual load ✅');
                                        } else if (nowDefined && newContent === 0) {
                                            log('⚠️ Component defined but still empty - Vue rendering issue', 'error');
                                            updateStep('step4', 'error', 'Component defined but Vue not rendering ⚠️');
                                        } else {
                                            log('❌ Manual load failed', 'error');
                                        }
                                    }, 2000);
                                })
                                .catch(err => {
                                    log(`❌ Manual load failed: ${err.message}`, 'error');
                                });
                        } else {
                            log('❌ SDK debug methods not available', 'error');
                        }
                    }
                    
                    // Additional debugging if component is empty but defined
                    if (customElementDefined && !hasContent) {
                        log('🔧 Component defined but empty - checking Vue rendering...');
                        
                        // Check if it's a Vue component issue
                        setTimeout(() => {
                            const shadowRoot = element.shadowRoot;
                            log(`   - Has shadow root: ${shadowRoot ? 'YES' : 'NO'}`);
                            if (shadowRoot) {
                                log(`   - Shadow root children: ${shadowRoot.children.length}`);
                                log(`   - Shadow root content: ${shadowRoot.innerHTML.length} chars`);
                            }
                        }, 1000);
                    }
                    
                } catch (error) {
                    log(`❌ Status check failed: ${error.message}`, 'error');
                    updateStep('step4', 'error', `Status check failed: ${error.message}`);
                }
            }
        </script>
    </body>
</html>
