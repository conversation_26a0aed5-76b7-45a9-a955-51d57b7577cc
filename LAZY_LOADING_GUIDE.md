# Angelos SDK - True Lazy Loading Guide

## Overview

The Angelos SDK has been optimized for **true lazy loading** - components are only loaded when they are actually needed in the DOM. This ensures minimal initial bundle size and optimal performance.

## 🚀 Key Features

### ✅ True Lazy Loading

- Components are loaded **on-demand** when they appear in the DOM
- Only necessary chunks are downloaded
- Automatic detection of dynamically added components
- Custom element definitions are loaded just-in-time

### ✅ Production vs Development Behavior

- **Production**: True lazy loading with chunk splitting
- **Development**: Eager loading due to Vite dev server behavior (this is normal)

### ✅ Automatic Component Detection

- Mutation observer monitors DOM changes
- Intersection observer for viewport-based loading
- Automatic upgrade of custom elements after loading

## 📦 Bundle Structure

### Main Entry Points

- `angelos.min.js` - Main SDK entry (4.36 kB gzipped)
- Component entries (loaded on-demand):
    - `forms-*.js` - Form components (0.28 kB)
    - `data-table-*.js` - Data table component (0.19 kB)
    - `details-view-*.js` - Details view component (0.19 kB)
    - `dynamic-view-*.js` - Dynamic view component (0.19 kB)

### Vendor Chunks (shared, loaded as needed)

- `vendor-vue-*.js` - Vue framework (387 kB)
- `vendor-zeta-*.js` - Zeta UI components (1.3 MB)
- `vendor-icons-*.js` - Icon library (829 kB)
- `vendor-monaco-*.js` - Monaco editor (3.1 MB)

## 🔧 Usage

### Basic Integration

```html
<!-- Load the main SDK -->
<script type="module" src="./angelos.min.js"></script>

<!-- Components are loaded automatically when detected -->
<zwe-angelos-create-form-v3 entity-id="my-entity" tenant-id="0"> </zwe-angelos-create-form-v3>
```

### Global API

```javascript
// Get loading statistics
const stats = window.AngelosSDK.getStats();
console.log(`${stats.loadedComponents}/${stats.totalComponents} components loaded`);

// Preload specific components
await window.AngelosSDK.preload(['zwe-angelos-create-form-v3']);

// Debug methods
const isLoaded = window.AngelosSDK.debug.isLoaded('zwe-angelos-create-form-v3');
await window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3');
await window.AngelosSDK.debug.upgradeAllElements();
```

## 🐞 Debug Methods

### Available Debug Functions

#### `window.AngelosSDK.debug.isLoaded(componentName)`

Check if a component is already loaded.

```javascript
const isLoaded = window.AngelosSDK.debug.isLoaded('zwe-angelos-create-form-v3');
```

#### `window.AngelosSDK.debug.forceLoadAndUpgrade(componentName)`

Force load a component and upgrade all existing elements.

```javascript
await window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3');
```

#### `window.AngelosSDK.debug.upgradeAllElements()`

Scan the entire DOM and upgrade all Angelos components.

```javascript
await window.AngelosSDK.debug.upgradeAllElements();
```

### Debug Testing

Use the `debug-test.html` page for comprehensive testing:

- Real-time component loading status
- Custom element registry inspection
- Dynamic component addition testing
- Force upgrade functionality

## 🏗️ Architecture

### Dynamic Component Loader

- **Entry-based loading**: Each component type has its own entry file
- **Custom element registration**: Components are registered as custom elements
- **Automatic upgrade**: Elements are upgraded after their definition loads
- **Smart detection**: Uses MutationObserver and IntersectionObserver

### Component Registry

```typescript
// Component to entry file mapping
{
  'zwe-angelos-create-form-v3': () => import('@/entries/forms'),
  'zwe-angelos-update-form-v3': () => import('@/entries/forms'),
  'zwe-angelos-data-table-v3': () => import('@/entries/data-table'),
  'zwe-angelos-details-view-v3': () => import('@/entries/details-view'),
  'zwe-angelos-dynamic-view-v3': () => import('@/entries/dynamic-view')
}
```

## ⚡ Performance Benefits

### Initial Load

- **Before**: All components loaded upfront (~6MB+)
- **After**: Only main SDK loaded initially (~4.36KB)
- **Savings**: ~99% reduction in initial bundle size

### Runtime Loading

- Components load in <100ms when needed
- Shared dependencies are cached and reused
- Browser caching reduces subsequent loads

### Memory Usage

- Only active components consume memory
- Unused components never load
- Automatic garbage collection of unused modules

## 🔍 Verification

### Production Testing

1. Build: `npm run build`
2. Serve: `npx http-server dist -p 8080`
3. Open: `http://localhost:8080/debug-test.html`
4. Monitor Network tab to see chunk loading

### Expected Behavior

- Initial page load: Only main SDK and essential chunks
- Component interaction: Relevant chunks load on-demand
- Network requests: Minimal, only for needed components

## 🚨 Important Notes

### Development vs Production

- **Development**: Vite dev server loads all chunks eagerly for faster HMR
- **Production**: True lazy loading with proper chunk splitting
- This behavior difference is normal and expected

### Custom Element Upgrade

- Components are automatically upgraded when their definition loads
- Use debug methods if manual upgrade is needed
- Dynamic DOM changes are automatically detected

### Browser Support

- Modern browsers with ES2020+ support
- Custom Elements v1 support required
- Dynamic imports support required

## 📊 Monitoring

### Real-time Statistics

```javascript
// Get current loading status
const stats = window.AngelosSDK.getStats();
console.log('Available:', stats.availableComponents);
console.log('Loaded:', stats.loadedList);
console.log('Progress:', `${stats.loadedComponents}/${stats.totalComponents}`);
```

### Network Monitoring

- Use browser DevTools Network tab
- Filter by "JS" to see chunk loading
- Monitor timing and cache hits

This optimized lazy loading approach ensures the Angelos SDK provides excellent performance while maintaining full functionality and developer experience.
