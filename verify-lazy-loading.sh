#!/bin/bash

echo "🧪 Testing Angelos SDK Lazy Loading in Production"
echo "================================================="

echo ""
echo "🔍 Checking production build files:"
echo "-----------------------------------"
echo "Main bundle:"
ls -la dist/angelos.min.js | awk '{print $9 " - " $5 " bytes"}'

echo ""
echo "Component chunks:"
ls -la dist/assets/ | grep -E "(forms|data-table|details-view|dynamic-view)" | awk '{print $9 " - " $5 " bytes"}'

echo ""
echo "🌐 Testing server accessibility:"
echo "--------------------------------"
curl -s -I http://127.0.0.1:8081/angelos.min.js | head -1
curl -s -I http://127.0.0.1:8081/index.html | head -1
curl -s -I http://127.0.0.1:8081/test-lazy-loading.html | head -1

echo ""
echo "✅ TESTING INSTRUCTIONS:"
echo "========================"
echo "1. Open: http://127.0.0.1:8081/test-lazy-loading.html"
echo "2. Open DevTools (F12) → Network Tab → Filter by 'JS'"
echo "3. Reload page - only main bundle should load initially"
echo "4. Click 'Add Form Component' - watch for forms chunk"
echo "5. Click 'Add Table Component' - watch for data-table chunk"
echo "6. Click 'Add Details Component' - watch for details chunk"
echo ""
echo "Expected: Each button click loads a new JS chunk on demand!"
echo "========================================================="
