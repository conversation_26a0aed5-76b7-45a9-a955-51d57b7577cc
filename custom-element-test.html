<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Custom Element Test - Plain JS vs Vue</title>
        <style>
            .test-section {
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #ccc;
                background: #f9f9f9;
            }
            .log {
                background: #000;
                color: #0f0;
                padding: 10px;
                margin: 10px 0;
                font-family: monospace;
                white-space: pre-wrap;
                max-height: 300px;
                overflow-y: auto;
            }
        </style>
    </head>
    <body>
        <h1>🧪 Custom Element Test - Plain JS vs Vue</h1>
        
        <div class="test-section">
            <h2>📊 Test Status</h2>
            <button onclick="runTests()">🚀 Run All Tests</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
            <div id="log" class="log">Click "Run All Tests" to begin...\n</div>
        </div>

        <div class="test-section">
            <h2>🔧 Test 1: Plain JavaScript Custom Element</h2>
            <p>This tests if basic custom elements work at all.</p>
            <div id="plain-test-container">
                <!-- Will be populated by test -->
            </div>
        </div>

        <div class="test-section">
            <h2>🖖 Test 2: Vue Custom Element (Simple)</h2>
            <p>This tests if Vue's defineCustomElement works in production.</p>
            <div id="vue-test-container">
                <!-- Will be populated by test -->
            </div>
        </div>

        <div class="test-section">
            <h2>🏗️ Test 3: Complex Angelos Component</h2>
            <p>This tests if the full Angelos form component works.</p>
            <div id="angelos-test-container">
                <!-- Will be populated by test -->
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Test 4: Loaded Plain JS Custom Element</h2>
            <p>This tests the plain JS element loaded via the dynamic loader.</p>
            <div id="loaded-plain-test-container">
                <!-- Will be populated by test -->
            </div>
        </div>

        <script>
            function log(message) {
                console.log(message);
                const logDiv = document.getElementById('log');
                logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
                logDiv.scrollTop = logDiv.scrollHeight;
            }

            function clearLog() {
                document.getElementById('log').textContent = '';
            }

            // Test 1: Define a plain JS custom element inline
            function testPlainJSCustomElement() {
                log('🔧 TEST 1: Plain JavaScript Custom Element');
                
                // Define the element if not already defined
                if (!customElements.get('test-plain-element')) {
                    class TestPlainElement extends HTMLElement {
                        constructor() {
                            super();
                            log('  🏗️ Plain element constructor called');
                        }
                        
                        connectedCallback() {
                            log('  🔌 Plain element connected to DOM');
                            this.innerHTML = `
                                <div style="padding: 15px; border: 2px solid green; background: #e8f5e8;">
                                    <h4>✅ Plain JS Custom Element Works!</h4>
                                    <p>This is rendered by a native custom element.</p>
                                    <button onclick="this.nextElementSibling.textContent = 'Clicked at ' + new Date().toLocaleTimeString()">Click me</button>
                                    <div>Not clicked yet</div>
                                </div>
                            `;
                            log('  ✅ Plain element rendered successfully');
                        }
                    }
                    
                    customElements.define('test-plain-element', TestPlainElement);
                    log('  📝 Plain element defined');
                } else {
                    log('  ♻️ Plain element already defined');
                }
                
                // Add it to the container
                const container = document.getElementById('plain-test-container');
                container.innerHTML = '<test-plain-element></test-plain-element>';
                log('  📍 Plain element added to DOM');
                
                // Check if it upgraded
                setTimeout(() => {
                    const element = container.querySelector('test-plain-element');
                    if (element && element.innerHTML.includes('Plain JS Custom Element Works')) {
                        log('  🎉 TEST 1 PASSED: Plain element rendered correctly');
                    } else {
                        log('  ❌ TEST 1 FAILED: Plain element did not render');
                    }
                }, 100);
            }

            // Test 2: Load Vue custom element
            function testVueCustomElement() {
                log('🖖 TEST 2: Vue Custom Element (via Angelos SDK)');
                
                if (!window.AngelosSDK) {
                    log('  ❌ AngelosSDK not loaded, skipping Vue test');
                    return;
                }
                
                // Add Vue test element to container
                const container = document.getElementById('vue-test-container');
                container.innerHTML = '<zwe-test-simple-component></zwe-test-simple-component>';
                log('  📍 Vue test element added to DOM');
                
                // Check if it upgraded after some time
                setTimeout(() => {
                    const element = container.querySelector('zwe-test-simple-component');
                    if (element && element.innerHTML.includes('Simple Vue Component')) {
                        log('  🎉 TEST 2 PASSED: Vue element rendered correctly');
                    } else {
                        log('  ❌ TEST 2 FAILED: Vue element did not render');
                        log('  📊 Element content: ' + (element ? element.innerHTML : 'null'));
                    }
                }, 2000);
            }

            // Test 3: Load complex Angelos component
            function testAngelosComponent() {
                log('🏗️ TEST 3: Complex Angelos Component');
                
                if (!window.AngelosSDK) {
                    log('  ❌ AngelosSDK not loaded, skipping Angelos test');
                    return;
                }
                
                // Add Angelos form to container  
                const container = document.getElementById('angelos-test-container');
                container.innerHTML = '<zwe-angelos-create-form-v3 entity-name="test-entity"></zwe-angelos-create-form-v3>';
                log('  📍 Angelos form element added to DOM');
                
                // Check if it upgraded after some time
                setTimeout(() => {
                    const element = container.querySelector('zwe-angelos-create-form-v3');
                    if (element && (element.innerHTML.trim() !== '' || element.shadowRoot)) {
                        log('  🎉 TEST 3 PASSED: Angelos component rendered');
                    } else {
                        log('  ❌ TEST 3 FAILED: Angelos component did not render');
                        log('  📊 Element content: ' + (element ? element.innerHTML : 'null'));
                        log('  🌒 Element shadowRoot: ' + (element && element.shadowRoot ? 'exists' : 'null'));
                    }
                }, 3000);
            }

            // Test 4: Test loaded plain JS custom element
            function testLoadedPlainJSCustomElement() {
                log('🧪 TEST 4: Loaded Plain JS Custom Element');
                
                if (!window.AngelosSDK) {
                    log('  ❌ AngelosSDK not loaded, skipping loaded plain test');
                    return;
                }
                
                // Add loaded plain element to container
                const container = document.getElementById('loaded-plain-test-container');
                container.innerHTML = '<zwe-plain-test-component></zwe-plain-test-component>';
                log('  📍 Loaded plain test element added to DOM');
                
                // Check if it upgraded after some time
                setTimeout(() => {
                    const element = container.querySelector('zwe-plain-test-component');
                    if (element && element.innerHTML.includes('Plain Custom Element Working')) {
                        log('  🎉 TEST 4 PASSED: Loaded plain element rendered correctly');
                    } else {
                        log('  ❌ TEST 4 FAILED: Loaded plain element did not render');
                        log('  📊 Element content: ' + (element ? element.innerHTML : 'null'));
                    }
                }, 2000);
            }

            function runTests() {
                log('🚀 Starting custom element tests...');
                clearLog();
                log('🚀 Starting custom element tests...');
                
                // Test 1: Plain JS (should work immediately)
                testPlainJSCustomElement();
                
                // Check if SDK is loaded, if not load it first
                if (!window.AngelosSDK) {
                    log('📦 Loading Angelos SDK...');
                    const script = document.createElement('script');
                    script.type = 'module';
                    script.src = './dist/angelos.min.js';
                    script.onload = () => {
                        log('✅ Angelos SDK loaded');
                        // Wait a bit for initialization
                        setTimeout(() => {
                            testVueCustomElement();
                            testAngelosComponent();
                            testLoadedPlainJSCustomElement();
                        }, 1000);
                    };
                    script.onerror = (error) => {
                        log('❌ Failed to load Angelos SDK: ' + error);
                    };
                    document.head.appendChild(script);
                } else {
                    log('♻️ Angelos SDK already loaded');
                    testVueCustomElement();
                    testAngelosComponent();
                    testLoadedPlainJSCustomElement();
                }
            }

            log('📝 Custom element test page initialized');
        </script>
    </body>
</html>
