<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos SDK - Optimized Build Test</title>
        <style>
            body {
                font-family: 'IBM Plex Sans', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e0e0e0;
            }
            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 6px;
                border-left: 4px solid #007bff;
            }
            .stat-title {
                font-weight: bold;
                color: #333;
                margin-bottom: 5px;
            }
            .stat-value {
                font-size: 1.2em;
                color: #007bff;
            }
            .test-section {
                margin: 30px 0;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 6px;
            }
            .test-section h3 {
                margin-top: 0;
                color: #333;
            }
            .button-group {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
                margin: 15px 0;
            }
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s;
            }
            .btn-primary {
                background: #007bff;
                color: white;
            }
            .btn-primary:hover {
                background: #0056b3;
            }
            .btn-success {
                background: #28a745;
                color: white;
            }
            .btn-success:hover {
                background: #1e7e34;
            }
            .btn-warning {
                background: #ffc107;
                color: #212529;
            }
            .btn-warning:hover {
                background: #e0a800;
            }
            .component-container {
                margin: 20px 0;
                padding: 15px;
                border: 2px dashed #ddd;
                border-radius: 6px;
                min-height: 100px;
            }
            .log {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 15px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                max-height: 300px;
                overflow-y: auto;
                white-space: pre-wrap;
            }
            .network-info {
                background: #e7f3ff;
                padding: 15px;
                border-radius: 6px;
                margin: 15px 0;
            }
            .performance-metrics {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }
            .metric {
                text-align: center;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 4px;
            }
            .metric-value {
                font-size: 1.5em;
                font-weight: bold;
                color: #007bff;
            }
            .metric-label {
                font-size: 0.9em;
                color: #666;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Angelos SDK - Optimized Build Test</h1>
                <p>Testing lazy loading, code splitting, and performance optimizations</p>
            </div>

            <div class="stats" id="stats">
                <div class="stat-card">
                    <div class="stat-title">SDK Status</div>
                    <div class="stat-value" id="sdk-status">Loading...</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">Components Loaded</div>
                    <div class="stat-value" id="components-loaded">0/5</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">Monaco Status</div>
                    <div class="stat-value" id="monaco-status">Not Loaded</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">Icons Loaded</div>
                    <div class="stat-value" id="icons-loaded">0</div>
                </div>
            </div>

            <div class="network-info">
                <strong>📡 Network Monitoring Instructions:</strong>
                <ol>
                    <li>Open DevTools (F12) → Network tab</li>
                    <li>Filter by "JS" to see JavaScript chunks</li>
                    <li>Clear network log and reload page</li>
                    <li>Watch chunks load on-demand as you click buttons below</li>
                </ol>
            </div>

            <div class="test-section">
                <h3>🧪 Component Loading Tests</h3>
                <p>Each button will trigger lazy loading of a specific component chunk:</p>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="testComponent('create-form')">
                        📝 Load Create Form
                    </button>
                    <button class="btn btn-primary" onclick="testComponent('update-form')">
                        ✏️ Load Update Form
                    </button>
                    <button class="btn btn-success" onclick="testComponent('data-table')">
                        📊 Load Data Table
                    </button>
                    <button class="btn btn-warning" onclick="testComponent('details-view')">
                        👁️ Load Details View
                    </button>
                    <button class="btn btn-warning" onclick="testComponent('dynamic-view')">
                        🔄 Load Dynamic View
                    </button>
                </div>

                <div class="component-container" id="component-container">
                    <p style="color: #666; text-align: center">
                        Components will appear here when loaded
                    </p>
                </div>
            </div>

            <div class="test-section">
                <h3>🎨 Monaco Editor Test</h3>
                <p>Monaco Editor loads only when code fields are needed:</p>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="testMonaco('javascript')">
                        🟨 Load JavaScript Editor
                    </button>
                    <button class="btn btn-primary" onclick="testMonaco('typescript')">
                        🔷 Load TypeScript Editor
                    </button>
                    <button class="btn btn-success" onclick="testMonaco('json')">
                        📄 Load JSON Editor
                    </button>
                </div>

                <div
                    id="monaco-container"
                    style="height: 200px; border: 1px solid #ddd; margin: 15px 0"
                ></div>
            </div>

            <div class="test-section">
                <h3>📊 Performance Metrics</h3>
                <div class="performance-metrics" id="performance-metrics">
                    <div class="metric">
                        <div class="metric-value" id="initial-load-time">-</div>
                        <div class="metric-label">Initial Load (ms)</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="chunks-loaded">0</div>
                        <div class="metric-label">Chunks Loaded</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="total-size">-</div>
                        <div class="metric-label">Total Downloaded</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="cache-hits">0</div>
                        <div class="metric-label">Cache Hits</div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>🔧 Debug Controls</h3>
                <div class="button-group">
                    <button class="btn btn-success" onclick="refreshStats()">
                        🔄 Refresh Stats
                    </button>
                    <button class="btn btn-warning" onclick="clearComponents()">
                        🧹 Clear Components
                    </button>
                    <button class="btn btn-primary" onclick="preloadAll()">⚡ Preload All</button>
                    <button class="btn btn-primary" onclick="showDebugInfo()">🐛 Debug Info</button>
                </div>
            </div>

            <div class="test-section">
                <h3>📝 Activity Log</h3>
                <div class="log" id="activity-log">Waiting for activity...</div>
            </div>
        </div>

        <!-- Global configuration -->
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };

            // Performance tracking
            const startTime = performance.now();
            let chunksLoaded = 0;
            let totalSize = 0;

            // Logging function
            function log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('activity-log');
                const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
                logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(`[${type.toUpperCase()}] ${message}`);
            }

            // Load the optimized SDK
            log('Loading Angelos SDK with optimizations...');
        </script>

        <!-- Load the optimized SDK -->
        <script type="module" src="./dist/angelos.min.js"></script>

        <script>
            // Wait for SDK to load
            let sdkReady = false;
            let checkInterval = setInterval(() => {
                if (window.AngelosSDK) {
                    sdkReady = true;
                    clearInterval(checkInterval);
                    log('✅ Angelos SDK loaded successfully');
                    document.getElementById('sdk-status').textContent = 'Ready';
                    document.getElementById('initial-load-time').textContent = Math.round(
                        performance.now() - startTime
                    );
                    refreshStats();
                }
            }, 100);

            // Test component loading
            async function testComponent(type) {
                if (!sdkReady) {
                    log('⚠️ SDK not ready yet', 'error');
                    return;
                }

                const container = document.getElementById('component-container');
                const componentMap = {
                    'create-form': 'zwe-angelos-create-form-v3',
                    'update-form': 'zwe-angelos-update-form-v3',
                    'data-table': 'zwe-angelos-data-table-v3',
                    'details-view': 'zwe-angelos-details-view-v3',
                    'dynamic-view': 'zwe-angelos-dynamic-view-v3'
                };

                const componentName = componentMap[type];
                if (!componentName) {
                    log(`❌ Unknown component type: ${type}`, 'error');
                    return;
                }

                log(`🔄 Testing ${type} component...`);

                // Create component element
                const element = document.createElement(componentName);
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.style.cssText =
                    'margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px;';

                // Add to container
                container.appendChild(element);

                log(`✅ ${type} component added to DOM - watch Network tab for chunk loading`);

                // Update stats after a delay to allow loading
                setTimeout(refreshStats, 1000);
            }

            // Test Monaco Editor
            async function testMonaco(language) {
                if (!window.MonacoLazyLoader) {
                    log('⚠️ Monaco Lazy Loader not available', 'error');
                    return;
                }

                log(`🎨 Loading Monaco Editor with ${language} language...`);

                const container = document.getElementById('monaco-container');
                container.innerHTML = ''; // Clear previous editor

                try {
                    const editor = await window.MonacoLazyLoader.createEditor(container, {
                        language: language,
                        value: `// ${language.toUpperCase()} Example\nconsole.log('Hello from ${language}!');`,
                        theme: 'vs-dark'
                    });

                    log(`✅ Monaco Editor loaded with ${language} support`);
                    document.getElementById('monaco-status').textContent = `Loaded (${language})`;
                } catch (error) {
                    log(`❌ Failed to load Monaco Editor: ${error.message}`, 'error');
                }
            }

            // Refresh statistics
            function refreshStats() {
                if (window.AngelosSDK) {
                    const stats = window.AngelosSDK.getStats();
                    document.getElementById('components-loaded').textContent =
                        `${stats.loadedComponents}/${stats.totalComponents}`;
                }

                if (window.IconLazyLoader) {
                    const iconStats = window.IconLazyLoader.getStats();
                    document.getElementById('icons-loaded').textContent = iconStats.loaded;
                }

                if (window.MonacoLazyLoader) {
                    const monacoStats = window.MonacoLazyLoader.getStats();
                    if (monacoStats.coreLoaded) {
                        document.getElementById('monaco-status').textContent =
                            `Loaded (${monacoStats.loadedLanguages.length} languages)`;
                    }
                }
            }

            // Clear components
            function clearComponents() {
                const container = document.getElementById('component-container');
                container.innerHTML =
                    '<p style="color: #666; text-align: center;">Components will appear here when loaded</p>';
                log('🧹 Components cleared');
            }

            // Preload all components
            async function preloadAll() {
                if (!window.AngelosSDK) {
                    log('⚠️ SDK not ready', 'error');
                    return;
                }

                log('⚡ Preloading all components...');

                const components = [
                    'zwe-angelos-create-form-v3',
                    'zwe-angelos-update-form-v3',
                    'zwe-angelos-data-table-v3',
                    'zwe-angelos-details-view-v3',
                    'zwe-angelos-dynamic-view-v3'
                ];

                try {
                    await window.AngelosSDK.preload(components);
                    log('✅ All components preloaded');
                    refreshStats();
                } catch (error) {
                    log(`❌ Preload failed: ${error.message}`, 'error');
                }
            }

            // Show debug information
            function showDebugInfo() {
                const debugInfo = {
                    sdk: window.AngelosSDK ? window.AngelosSDK.getStats() : 'Not loaded',
                    monaco: window.MonacoLazyLoader
                        ? window.MonacoLazyLoader.getStats()
                        : 'Not loaded',
                    icons: window.IconLazyLoader ? window.IconLazyLoader.getStats() : 'Not loaded'
                };

                log('🐛 Debug Info:');
                log(JSON.stringify(debugInfo, null, 2));
            }

            // Monitor network requests (basic implementation)
            const originalFetch = window.fetch;
            window.fetch = function (...args) {
                const url = args[0];
                if (typeof url === 'string' && url.includes('.js')) {
                    chunksLoaded++;
                    log(`📦 Chunk loaded: ${url.split('/').pop()}`);
                    document.getElementById('chunks-loaded').textContent = chunksLoaded;
                }
                return originalFetch.apply(this, args);
            };

            // Auto-refresh stats every 2 seconds
            setInterval(refreshStats, 2000);

            log('🚀 Test page initialized - ready for testing!');
        </script>
    </body>
</html>
