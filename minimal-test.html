<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Minimal Test</title>
    </head>
    <body>
        <h1>🧪 Minimal Test Page</h1>
        <p>If you can see this, the server is working.</p>
        
        <div id="status">Loading...</div>
        
        <script>
            console.log('📝 Basic script executed');
            document.getElementById('status').textContent = 'Basic JavaScript works!';
        </script>
        
        <script type="module">
            console.log('📦 ES6 module script executed');
            document.getElementById('status').textContent = 'ES6 modules work!';
        </script>
        
        <script type="module" src="./dist/angelos.min.js"></script>
        <script type="module">
            setTimeout(() => {
                console.log('🔍 Checking Angelos SDK...');
                if (window.AngelosSDK) {
                    console.log('✅ AngelosSDK is available');
                    document.getElementById('status').textContent = 'AngelosSDK loaded successfully!';
                } else {
                    console.log('❌ AngelosSDK is NOT available');
                    document.getElementById('status').textContent = 'AngelosSDK failed to load';
                }
            }, 2000);
        </script>
    </body>
</html>
