{"payments": [{"id": 360638000290, "ifi": 600309, "requestChannelType": "SUPER_CARD", "state": "PAYMENT_REQUEST_DECLINED", "value": {"currency": "USD", "amount": 8350}, "stateTransitions": {"PAYMENT_AUTHORIZATION_REQUESTED": 1737978085202, "PAYMENT_REQUESTED": *************, "PAYMENT_REQUEST_DECLINED": *************}, "detailedStateTransitions": {"PAYMENT_REQUESTED": {"time": *************}, "PAYMENT_AUTHORIZATION_REQUESTED": {"time": 1737978085202}, "PAYMENT_REQUEST_DECLINED": {"reason": "AuthenticationException", "code": "AUTHENTICATION_FAILED", "details": {"failureCode": "INCORRECT_CVV2", "failureReason": "Incorrect CVV2", "errorCode": "AUTHENTICATION_FAILED", "source": "CMS", "message": "Resource Authentication Failed", "identityProvider": "CMS", "extendedAttributes": {"failureCode": "INCORRECT_CVV2", "failureReason": "Incorrect CVV2", "source": "CMS", "identityProvider": "CMS", "status": "FAILURE"}, "status": "FAILURE"}, "time": *************, "message": "Incorrect CVV2"}}, "payer": {"resourceID": "fcf73c00-281e-4e55-aeb9-ac5f001422a9", "formFactorTargetURI": "paymentAccount://f69ba504-1807-4127-b596-fcc2624bf351", "formFactorProductID": "5415c3f3-8234-4bf9-b6f2-838298c19e6b", "formFactorURI": "card://f6870c00-7698-40af-8ea4-4d2693f5df3d", "requestFormFactorExistenceType": "BASE", "targetURI": "paymentAccount://f69ba504-1807-4127-b596-fcc2624bf351", "resourceProductID": "6ffbf336-b4ee-42cf-8f6f-4dec7877e54b", "resourceVectors": "vector://ACCOUNTHOLDER/a8ac1ca7-d8ff-4f5d-8b16-a4945e09f840", "formFactorReferenceID": "55c7776e-07d9-467d-a926-add71eb20a1c", "formFactorStatus": "ACTIVE", "type": "RESOURCE", "formFactor:tags": "[]"}, "payee": {"type": "EXTERNAL_BUSINESS", "name": "TPAUTOMATIONMASTERCARD", "location": "SEATTLE"}, "paymentRequest": {"paymentRequestID": "GRFPGVALO0127_f6870c00-7698-40af-8ea4-4d2693f5df3d", "requestFrom": "<EMAIL>", "requestTo": "card://f6870c00-7698-40af-8ea4-4d2693f5df3d", "value": [{"currency": "USD", "amount": 8350}], "dueBy": *************, "towards": "TPAUTOMATIONMASTERCARD SEATTLE WASHINGTON USA", "paymentPlan": {"debits": [], "credits": []}, "attributes": {"super-card:isTerminalCardCaptureCapable": "0", "super-card:isDomestic": "true", "super-card:avs-result": "R", "super-card:card-id": "f6870c00-7698-40af-8ea4-4d2693f5df3d", "super-card:terminalCardCaptureCapability": "1", "super-card:life-cycle-id": "f6870c00-7698-40af-8ea4-4d2693f5df3d", "channel:token:txnFlag": "false", "super-card:card-bin": "535998", "super-card:trans-id": "GRFPGVALO0127_f6870c00-7698-40af-8ea4-4d2693f5df3d", "super-card:eci": "212", "channelCodeName": "MC DMS US Offus Channel", "super-card:mcc": "5611", "super-card:terminal-type": "BROWSER", "super-card:tid": "13796001", "super-card:terminal-partial-auth-indicator": "false", "super-card:merchant-name": "TPAUTOMATIONMASTERCARD", "super-card:transaction-fee": "D00000000", "transactionMetadata": "{\"isoPayload\":{\"timeLocalTransaction\":{\"isoFieldNumber\":\"12\",\"rawValue\":\"043000\",\"translatedValue\":\"\",\"subfields\":{}},\"systemTraceAuditNumber\":{\"isoFieldNumber\":\"11\",\"rawValue\":\"367723\",\"translatedValue\":\"\",\"subfields\":{}},\"pointOfServiceEntryMode\":{\"isoFieldNumber\":\"22\",\"rawValue\":\"810\",\"translatedValue\":\"\",\"subfields\":{}},\"processingCode\":{\"isoFieldNumber\":\"3\",\"rawValue\":\"000000\",\"translatedValue\":\"\",\"subfields\":{}},\"primaryAccountNumber\":{\"isoFieldNumber\":\"2\",\"rawValue\":\"535998-xxxxxx-9763\",\"translatedValue\":\"\",\"subfields\":{}},\"dateLocalTransaction\":{\"isoFieldNumber\":\"13\",\"rawValue\":\"0126\",\"translatedValue\":\"\",\"subfields\":{}},\"forwardingInstitutionIdentificationCode\":{\"isoFieldNumber\":\"33\",\"translatedValue\":\"\",\"subfields\":{}},\"amountTransactionFee\":{\"isoFieldNumber\":\"28\",\"rawValue\":\"D00000000\",\"translatedValue\":\"\",\"subfields\":{}}},\"isoPayloadScheme\":\"MASTERCARD\"}", "payment:formFactorURI": "card://f6870c00-7698-40af-8ea4-4d2693f5df3d", "super-card:acquirer": "968500", "super-card:pin-entry-capability": "0", "super-card:card-6x4": "535998-xxxxxx-9763", "super-card:scheme": "MASTERCARD", "paymentCode": "PMCUSZZ110111069", "super-card:txn-type": "ECOM", "super-card:fromAccount-type-code": "00", "super-card:ifi": "600309", "avs:match:result": "ERROR", "super-card:cvv2-flag": "0", "paymentCodeName": "DOM_ECOM_PURCHASE_SECURE", "super-card:init-time": "************", "super-card:txn:fraudScoringServices": "{\"05\":{\"result1\":\"V\",\"result2\":\" \"},\"18\":{\"result1\":\"C\",\"result2\":\" \"}}", "super-card:txn-sub-type": "NORMAL", "super-card:card-present": "1", "super-card:cvv2-presence-flag": "1", "super-card:payerAccountType": "Not Applicable or Not Specified", "super-card:merchant-city": "SEATTLE", "super-card:valueTime": "*************", "super-card:payment-network": "MASTERCARD", "super-card:txn-auth-type": "SECURE_MASTERCARD_SMART_AUTHENTICATION_OBS_SECURE_FULLY_AUTHENTICATED", "super-card:txn-region": "DOMESTIC", "super-card:transactionIdentifier": "GRFPGVALO0127", "super-card:stan": "367723", "super-card:mid": "13796", "super-card:purchase-txn-sub-category": "NOT_APPLICABLE", "journal:voucherCode": "MASTERCARD-535998_ECOM_AUTH", "logMessageId": "297f7dee-4fba-40ec-8a80-f13b4f8cd5ab", "super-card:transactionLinkID": "1737977843243259639997", "super-card:card-holder-present": "5", "channelCode": "CHNUSZZ1101", "super-card:txn-sub-category": "Purchase", "super-card:mti": "0100", "payment:channelType": "SUPER_CARD", "super-card:DCC": "false", "super-card:otp-enter-mode": "manual", "avs:response:match": "System is unable to process AVS", "super-card:processingCode": "Purchase", "super-card:entry-mode": "ECOM", "super-card:card-acceptor-state": "WA ", "super-card:pointOfService-transaction-code": "0", "bin:type": "CREDIT", "super-card:payeeAccountType": "Not Applicable or Not Specified", "super-card:merchant-country": "USA", "super-card:db-cr-ind": "D", "authRequest": "{\"value\":[{\"type\":\"Cardholder Billing\",\"currency\":\"USD\",\"amount\":8350},{\"type\":\"Transaction\",\"currency\":\"USD\",\"amount\":100}],\"conversionRate\":\"83.50000\"}", "super-card:rrn": "************"}, "originalAuthRequestID": "GRFPGVALO0127_f6870c00-7698-40af-8ea4-4d2693f5df3d", "adviceType": "NONE", "ifi": 600309}, "receipts": [], "paymentCode": "PMCUSZZ110111069", "channelCode": "CHNUSZZ1101", "error": {"Code": "AUTHENTICATION_FAILED", "details": {"failureCode": "INCORRECT_CVV2", "failureReason": "Incorrect CVV2", "errorCode": "AUTHENTICATION_FAILED", "source": "CMS", "message": "Resource Authentication Failed", "identityProvider": "CMS", "extendedAttributes": {"failureCode": "INCORRECT_CVV2", "failureReason": "Incorrect CVV2", "source": "CMS", "identityProvider": "CMS", "status": "FAILURE"}, "status": "FAILURE"}}, "originalAuthRequestID": "GRFPGVALO0127_f6870c00-7698-40af-8ea4-4d2693f5df3d", "requestType": "AUTHORIZATION", "paymentPlan": {"debits": [], "credits": []}, "adviceType": "NONE", "paymentMessageFragmentResponseList": [{"receiptId": 360638000290, "paymentRequestId": "GRFPGVALO0127_f6870c00-7698-40af-8ea4-4d2693f5df3d", "requestFrom": "<EMAIL>", "requestTo": "card://f6870c00-7698-40af-8ea4-4d2693f5df3d", "state": "PAYMENT_REQUEST_DECLINED", "requestType": "AUTHORIZATION", "adviceType": "NONE", "stateTransitions": {"PAYMENT_REQUESTED": *************, "PAYMENT_AUTHORIZATION_REQUESTED": 1737978085202, "PAYMENT_REQUEST_DECLINED": 1737978089313}, "value": {"currency": "USD", "amount": 8350}, "dueBy": *************, "requestAttributes": {"super-card:isTerminalCardCaptureCapable": "0", "super-card:isDomestic": "true", "super-card:avs-result": "R", "super-card:card-id": "f6870c00-7698-40af-8ea4-4d2693f5df3d", "super-card:terminalCardCaptureCapability": "1", "super-card:life-cycle-id": "f6870c00-7698-40af-8ea4-4d2693f5df3d", "channel:token:txnFlag": "false", "super-card:card-bin": "535998", "super-card:trans-id": "GRFPGVALO0127_f6870c00-7698-40af-8ea4-4d2693f5df3d", "super-card:eci": "212", "channelCodeName": "MC DMS US Offus Channel", "super-card:mcc": "5611", "super-card:tid": "13796001", "super-card:terminal-type": "BROWSER", "super-card:terminal-partial-auth-indicator": "false", "super-card:merchant-name": "TPAUTOMATIONMASTERCARD", "transactionMetadata": "{\"isoPayload\":{\"timeLocalTransaction\":{\"isoFieldNumber\":\"12\",\"rawValue\":\"043000\",\"translatedValue\":\"\",\"subfields\":{}},\"systemTraceAuditNumber\":{\"isoFieldNumber\":\"11\",\"rawValue\":\"367723\",\"translatedValue\":\"\",\"subfields\":{}},\"pointOfServiceEntryMode\":{\"isoFieldNumber\":\"22\",\"rawValue\":\"810\",\"translatedValue\":\"\",\"subfields\":{}},\"processingCode\":{\"isoFieldNumber\":\"3\",\"rawValue\":\"000000\",\"translatedValue\":\"\",\"subfields\":{}},\"primaryAccountNumber\":{\"isoFieldNumber\":\"2\",\"rawValue\":\"535998-xxxxxx-9763\",\"translatedValue\":\"\",\"subfields\":{}},\"dateLocalTransaction\":{\"isoFieldNumber\":\"13\",\"rawValue\":\"0126\",\"translatedValue\":\"\",\"subfields\":{}},\"forwardingInstitutionIdentificationCode\":{\"isoFieldNumber\":\"33\",\"translatedValue\":\"\",\"subfields\":{}},\"amountTransactionFee\":{\"isoFieldNumber\":\"28\",\"rawValue\":\"D00000000\",\"translatedValue\":\"\",\"subfields\":{}}},\"isoPayloadScheme\":\"MASTERCARD\"}", "super-card:transaction-fee": "D00000000", "payment:formFactorURI": "card://f6870c00-7698-40af-8ea4-4d2693f5df3d", "super-card:acquirer": "968500", "super-card:pin-entry-capability": "0", "paymentCode": "PMCUSZZ110111069", "super-card:scheme": "MASTERCARD", "super-card:card-6x4": "535998-xxxxxx-9763", "super-card:txn-type": "ECOM", "super-card:fromAccount-type-code": "00", "super-card:ifi": "600309", "avs:match:result": "ERROR", "paymentCodeName": "DOM_ECOM_PURCHASE_SECURE", "super-card:cvv2-flag": "0", "super-card:init-time": "************", "super-card:txn:fraudScoringServices": "{\"05\":{\"result1\":\"V\",\"result2\":\" \"},\"18\":{\"result1\":\"C\",\"result2\":\" \"}}", "super-card:txn-sub-type": "NORMAL", "super-card:card-present": "1", "super-card:cvv2-presence-flag": "1", "super-card:merchant-city": "SEATTLE", "super-card:payerAccountType": "Not Applicable or Not Specified", "super-card:valueTime": "*************", "super-card:payment-network": "MASTERCARD", "super-card:txn-auth-type": "SECURE_MASTERCARD_SMART_AUTHENTICATION_OBS_SECURE_FULLY_AUTHENTICATED", "super-card:txn-region": "DOMESTIC", "super-card:stan": "367723", "super-card:transactionIdentifier": "GRFPGVALO0127", "super-card:mid": "13796", "super-card:purchase-txn-sub-category": "NOT_APPLICABLE", "journal:voucherCode": "MASTERCARD-535998_ECOM_AUTH", "logMessageId": "297f7dee-4fba-40ec-8a80-f13b4f8cd5ab", "super-card:transactionLinkID": "1737977843243259639997", "super-card:card-holder-present": "5", "channelCode": "CHNUSZZ1101", "super-card:txn-sub-category": "Purchase", "super-card:mti": "0100", "payment:channelType": "SUPER_CARD", "super-card:DCC": "false", "super-card:otp-enter-mode": "manual", "avs:response:match": "System is unable to process AVS", "super-card:processingCode": "Purchase", "super-card:entry-mode": "ECOM", "super-card:card-acceptor-state": "WA ", "super-card:pointOfService-transaction-code": "0", "bin:type": "CREDIT", "super-card:payeeAccountType": "Not Applicable or Not Specified", "super-card:merchant-country": "USA", "super-card:db-cr-ind": "D", "authRequest": "{\"value\":[{\"type\":\"Cardholder Billing\",\"currency\":\"USD\",\"amount\":8350},{\"type\":\"Transaction\",\"currency\":\"USD\",\"amount\":100}],\"conversionRate\":\"83.50000\"}", "super-card:rrn": "************"}, "error": {"Code": "AUTHENTICATION_FAILED", "details": {"failureCode": "INCORRECT_CVV2", "failureReason": "Incorrect CVV2", "errorCode": "AUTHENTICATION_FAILED", "source": "CMS", "message": "Resource Authentication Failed", "identityProvider": "CMS", "extendedAttributes": {"failureCode": "INCORRECT_CVV2", "failureReason": "Incorrect CVV2", "source": "CMS", "identityProvider": "CMS", "status": "FAILURE"}, "status": "FAILURE"}}, "createdAt": *************}], "reversalTransactions": [], "headers": {}}], "totalRecords": 1, "recordsReturned": 1, "headers": {}}