{"summaries": [{"postings": [{"ledgerID": "5014347300311961992", "postingID": 102143, "postingCode": "redeem-coupons", "postingCodeVersion": 1, "transactionID": "20250106102134107_1424_CREDIT_ALLOCATION_5014347300311961992_102140_0", "txnID": "20250106102134107_1424_CREDIT_ALLOCATION_5014347300311961992_102140_0", "postingIndex": 0, "recordType": "META_DETAIL_CHANGE", "value": {"currency": "USD", "amount": 0}, "previousBalance": 0, "newBalance": 0, "timestamp": *************, "transactionTime": *************, "createdAt": *************, "remarks": "Credit allocation for ledger 5014347300311961992 and posting id 102140", "attributes": {"ruby.redeemer-posting-id": "102140", "ruby.parentPostingVoucherCode": "RECOVERY_POST_CHARGE_OFF", "creditAgainstBilledTransaction": "false", "ruby.parentPostingTransactionID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0", "creditAgainstMatchedTransaction": "false", "defaultTimeReference": "BOOK", "journal.voucherCode": "CREDIT_ALLOCATION", "journal.self": "false", "isP2PTransfer": "false", "isForced": "true"}, "categories": [{"categoryCode": "NetCashAdvanceFee", "value": 0, "recordType": "CREDIT"}, {"categoryCode": "net-cash-advance-fee", "value": 0, "recordType": "DEBIT"}], "bookTime": *************, "valueTime": *************, "statementTime": *************, "requestPayload": {"signedDoc": {"ifiID": 600309, "coaID": "1441665598567441191", "ledgerID": "5014347300311961992", "transactionID": "20250106102134107_1424_CREDIT_ALLOCATION_5014347300311961992_102140_0", "txnID": "20250106102134107_1424_CREDIT_ALLOCATION_5014347300311961992_102140_0", "postingIndex": 0, "authorization": {"postingCategories": [], "attributes": {}, "otherLegInfo": {}, "headers": {}, "isForced": true, "type": "meta-record", "ledgerID": "5014347300311961992", "towards": "Credit allocation for ledger 5014347300311961992 and posting id 102140", "authID": "CREDIT_ALLOCATION_5014347300311961992_102140_0", "postingCode": "redeem-coupons", "validUntil": *************, "payload": {"postingCategories": [], "couponRedemptions": [{"couponID": "f7c3923f-d21b-446d-a6a4-eee16af6ca24", "value": 100, "curn": "102140", "valueTime": *************}]}}, "isForced": true, "remarks": "Credit allocation for ledger 5014347300311961992 and posting id 102140", "attributes": {"ruby.redeemer-posting-id": "102140", "ruby.parentPostingVoucherCode": "RECOVERY_POST_CHARGE_OFF", "creditAgainstBilledTransaction": "false", "ruby.parentPostingTransactionID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0", "creditAgainstMatchedTransaction": "false", "defaultTimeReference": "BOOK", "journal.voucherCode": "CREDIT_ALLOCATION", "journal.self": "false", "isP2PTransfer": "false", "isForced": "true"}, "transactionTime": *************, "bookTime": *************, "valueTime": *************, "statementTime": *************, "phase": "EOPI", "postingCodeURI": "postingCode://code/PSCINZZ0001", "headers": {"signatoryJID": "<EMAIL>", "signature": "AAHk6Z0wRAIgTCHAGJJrQYoYZqIkgZJVSP9Duv/PpHklJAti0CEMpbECIHDW81+AAFCydn4AZFF+NyzfDtzUVpwhKU0YQZTHNxup"}}, "version": 1, "headers": {}}, "isReversalPosting": false, "disputeIndicator": "", "disputeAttributes": [], "__searchKey__": "10214320250106102134107_1424_credit_allocation_5014347300311961992_102140_0credit_allocation0meta_detail_changeredeem-couponsnetcashadvancefee, net-cash-advance-feecredit allocation for ledger 5014347300311961992 and posting id 102140-"}, {"ledgerID": "5014347300311961992", "postingID": 102140, "postingCode": "-", "postingCodeVersion": 1, "transactionID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0.1", "txnID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0", "postingIndex": 1, "recordType": "DEBIT", "value": {"currency": "USD", "amount": 100}, "previousBalance": -100, "newBalance": 0, "timestamp": *************, "transactionTime": *************, "createdAt": *************, "remarks": "retailInterest - Recovery after Charge Off  {5014347300311961992, ****************}", "attributes": {"defaultTimeReference": "BOOK", "postingComponentType": "retailInterest", "journal.voucherCode": "RECOVERY_POST_CHARGE_OFF", "ruby.categoryType": "RETAILINTEREST", "journal.self": "false", "reasonCode": "SystemChargeOff", "relatedLedgerID": "5014347300311961600", "isP2PTransfer": "false", "isForced": "true"}, "categories": [{"categoryCode": "ExcessPayment", "value": 100, "recordType": "DEBIT"}, {"categoryCode": "InterestFreeUnbilled", "value": 100, "recordType": "DEBIT"}], "bookTime": *************, "valueTime": *************, "statementTime": *************, "requestPayload": {"signedDoc": {"ifiID": 600309, "coaID": "1441665598567441191", "ledgerID": "5014347300311961992", "transactionID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0.1", "txnID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0", "postingIndex": 1, "authorization": {"type": "app-generated-record", "towards": "retailInterest - Recovery after Charge Off  {5014347300311961992, ****************}", "authID": "rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0DEBIT", "isForced": true, "value": {"currency": "USD", "amount": 100}, "validUntil": **************, "postingCategories": [{"categoryCode": "ExcessPayment", "value": 100, "recordType": "DEBIT"}, {"categoryCode": "InterestFreeUnbilled", "value": 100, "recordType": "DEBIT"}], "ledgerID": "5014347300311961992", "headers": {"signature": "AAHk6Z0wRgIhAL6xA5ypzkfJetl4iRPj85Bxcxv8JlcRcWY4AIuD3Hj+AiEAuYh5VuhLvMW9skSNUZtSDf5V9dqHBWuEao7FeNhF8b4=", "signatoryJID": "<EMAIL>"}}, "isForced": true, "remarks": "retailInterest - Recovery after Charge Off  {5014347300311961992, ****************}", "attributes": {"defaultTimeReference": "BOOK", "postingComponentType": "retailInterest", "ruby.categoryType": "RETAILINTEREST", "journal.voucherCode": "RECOVERY_POST_CHARGE_OFF", "journal.self": "false", "reasonCode": "SystemChargeOff", "isP2PTransfer": "false", "relatedLedgerID": "5014347300311961600", "isForced": "true"}, "transactionTime": *************, "valueTime": *************, "bookTime": *************, "statementTime": *************, "phase": "EOPI", "postingCodeURI": "postingCode://code/PSCINZZ0001", "headers": {"signatoryJID": "<EMAIL>", "signature": "AAHk6Z0wRgIhAJr87YIFFDLWjFOfRZCldkcN192+T+MDbsf6h2t8DfMJAiEAh+x9teGDJton/m/sI86+X7KU5dVuOtZcQ/4nfXkilQ8="}}, "version": 1, "headers": {}}, "isReversalDebit": false, "isReversalPosting": false, "formattedAmount": 1, "disputeIndicator": "", "disputeAttributes": [], "formattedRecordType": "DEBIT", "formattedBalance": "", "categoryCodes": ["ExcessPayment", "InterestFreeUnbilled"], "voucherCode": "RECOVERY_POST_CHARGE_OFF", "netTransactionId": "-", "categoryString": "ExcessPayment, InterestFreeUnbilled", "cardNumber": "", "merchantName": "", "arn": "-"}, {"ledgerID": "5014347300311961992", "postingID": 102140, "postingCode": "-", "postingCodeVersion": 1, "transactionID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0.1", "txnID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0", "postingIndex": 1, "recordType": "DEBIT", "value": {"currency": "USD", "amount": 100}, "previousBalance": -100, "newBalance": 0, "timestamp": *************, "transactionTime": *************, "createdAt": *************, "remarks": "retailInterest - Recovery after Charge Off  {5014347300311961992, ****************}", "attributes": {"defaultTimeReference": "BOOK", "postingComponentType": "retailInterest", "journal.voucherCode": "RECOVERY_POST_CHARGE_OFF", "ruby.categoryType": "RETAILINTEREST", "journal.self": "false", "reasonCode": "SystemChargeOff", "relatedLedgerID": "5014347300311961600", "isP2PTransfer": "false", "isForced": "true"}, "categories": [{"categoryCode": "ExcessPayment", "value": 100, "recordType": "DEBIT"}, {"categoryCode": "InterestFreeUnbilled", "value": 100, "recordType": "DEBIT"}], "bookTime": *************, "valueTime": *************, "statementTime": *************, "requestPayload": {"signedDoc": {"ifiID": 600309, "coaID": "1441665598567441191", "ledgerID": "5014347300311961992", "transactionID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0.1", "txnID": "20250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0", "postingIndex": 1, "authorization": {"type": "app-generated-record", "towards": "retailInterest - Recovery after Charge Off  {5014347300311961992, ****************}", "authID": "rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_RECOVERY_POST_CHARGE_OFF_0DEBIT", "isForced": true, "value": {"currency": "USD", "amount": 100}, "validUntil": **************, "postingCategories": [{"categoryCode": "ExcessPayment", "value": 100, "recordType": "DEBIT"}, {"categoryCode": "InterestFreeUnbilled", "value": 100, "recordType": "DEBIT"}], "ledgerID": "5014347300311961992", "headers": {"signature": "AAHk6Z0wRgIhAL6xA5ypzkfJetl4iRPj85Bxcxv8JlcRcWY4AIuD3Hj+AiEAuYh5VuhLvMW9skSNUZtSDf5V9dqHBWuEao7FeNhF8b4=", "signatoryJID": "<EMAIL>"}}, "isForced": true, "remarks": "retailInterest - Recovery after Charge Off  {5014347300311961992, ****************}", "attributes": {"defaultTimeReference": "BOOK", "postingComponentType": "retailInterest", "ruby.categoryType": "RETAILINTEREST", "journal.voucherCode": "RECOVERY_POST_CHARGE_OFF", "journal.self": "false", "reasonCode": "SystemChargeOff", "isP2PTransfer": "false", "relatedLedgerID": "5014347300311961600", "isForced": "true"}, "transactionTime": *************, "valueTime": *************, "bookTime": *************, "statementTime": *************, "phase": "EOPI", "postingCodeURI": "postingCode://code/PSCINZZ0001", "headers": {"signatoryJID": "<EMAIL>", "signature": "AAHk6Z0wRgIhAJr87YIFFDLWjFOfRZCldkcN192+T+MDbsf6h2t8DfMJAiEAh+x9teGDJton/m/sI86+X7KU5dVuOtZcQ/4nfXkilQ8="}}, "version": 1, "headers": {}}, "isReversalPosting": false, "disputeIndicator": "", "disputeAttributes": [], "__searchKey__": "10214020250106102131690_1419_rubydelinquencyaccountingworker_job-bd31a5d9-64b4-45a9-bcfc-fef97bef44d1_5014347300311961992_8377906575248518103_recovery_post_charge_off_0.1recovery_post_charge_off1debit-excesspayment, interestfreeunbilledretailinterest - recovery after charge off  {5014347300311961992, ****************}-"}]}]}