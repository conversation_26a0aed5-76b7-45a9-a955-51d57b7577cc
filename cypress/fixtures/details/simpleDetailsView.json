{"dataConfig": [{"type": "PREFILL", "refId": "data1", "httpRequest": {"url": "<%= params.baseUrl%>/ledger-manager/tenants/<%= params.tenantId %>/coas/<%= params.coaId %>/ledgers/<%= params.ledgerId %>/statements?clockType=SYSTEM&cycleID=-1&periodStartSequenceNumber=1&periodEndSequenceNumber=1&includePostingRequestPayload=true", "method": "GET", "comment": "", "cookies": [], "headers": [{"name": "authorization", "value": "Bearer <%= authToken %>"}], "bodySize": -1, "headersSize": -1, "httpVersion": "HTTP/1.1", "queryString": []}, "inputParams": [], "paginationParams": {}, "responseTransformer": "const currencyMap = {\nINR: {\n    locale: 'en-IN',\n    decimal: 100,\n},\nBRL: {\n    locale: 'en-BR',\n    decimal: 100,\n},\nPHP: {\n    locale: 'en-PH',\n    decimal: 100,\n},\nVND: {\n    locale: 'en-VN',\n    decimal: 100,\n},\nEUR: {\n    locale: 'en-UK',\n    decimal: 100,\n},\nUSD: {\n    locale: 'en-US',\n    decimal: 100,\n},\nJPY: {\n    locale: 'en-JP',\n    decimal: 100,\n},\nCNY: {\n    locale: 'en-CN',\n    decimal: 100,\n},\nCAD: {\n    locale: 'en-CA',\n    decimal: 100,\n},\nSGD: {\n    locale: 'en-SG',\n    decimal: 100,\n},\nAED: {\n    locale: 'en-AE',\n    decimal: 100,\n},\nHKD: {\n    locale: 'en-HK',\n    decimal: 100,\n},\nAUD: {\n    locale: 'en-AU',\n    decimal: 100,\n},\nOMR: {\n    locale: 'en-OM',\n    decimal: 1000,\n},\n};\n\nconst postings = res?.summaries?.[0]?.postings;\nconst postingDetail =\npostings?.find((posting) => posting.postingID == params.postingId) || {};\n\n//if context.viewType is not provided by application then set the view type based on the posting detail\nconst HOLD_VOUCHER_CODE_LIST = context?.holdVoucherCodeList || [\"HOLD\", \"UNHOLD\", \"HOLD_EXPIRY\", \"HOLD_EXPIRY_CANCEL\", \"HOLD_EXPIRY_CANCELLED\"];\nif(postingDetail?.recordType === \"META_DETAIL_CHANGE\" && postingDetail?.attributes?.['journal.voucherCode'] ===  \"CREDIT_ALLOCATION\")\n  context.viewType = \"ALLOCATION\";\nelse if(HOLD_VOUCHER_CODE_LIST.includes(postingDetail?.attributes?.['journal.voucherCode']))\n  context.viewType = \"HOLD\";\nelse if(postingDetail?.requestPayload?.signedDoc?.authorization?.postingCode === \"add-ledger-tags\")\n  context.viewType = \"ADD_LEDGER_TAGS\";\nelse if(postingDetail?.requestPayload?.signedDoc?.authorization?.postingCode === \"update-ledger-tags\")\n  context.viewType = \"UPDATE_LEDGER_TAGS\";\nelse if(postingDetail?.requestPayload?.signedDoc?.authorization?.postingCode === \"replace-ledger-tags\")\n  context.viewType = \"REPLACE_LEDGER_TAGS\";\nelse\n  context.viewType = \"NORMAL\";\n\nconst currency = postingDetail?.value?.currency || context?.currency;\nconst locale = context?.locale || 'en-IN';\nconst timeZone = context?.timeZone || '';\n\nfunction addDecimalToAmount(amount, currency) {\n// If the currency is not available then pass the amount as it is\nconst divisionBy = currencyMap[currency]?.decimal || 1;\nreturn amount / divisionBy;\n}\nfunction localizeAmount(amount,currency) {\nif (isNaN(amount)) {\n    return '';\n} else if (currency) {\n    amount = addDecimalToAmount(amount, currency);\n    const currencyLocale = currencyMap[currency]?.locale || context?.locale || 'en-IN';\n    return amount.toLocaleString(currencyLocale, {\n    \t\t\t\tmaximumFractionDigits: 2,\n    \t\t\t\tstyle: 'currency',\n    \t\t\t\tcurrency: currency,\n\t\t\t\t\t});\n}\nreturn amount;\n}\n\nfunction getLocaleDateString(\ndate,\noptions = { year: 'numeric', month: 'short', day: 'numeric' }\n) {\nlet validDate = date;\nif (validDate && (typeof date === 'number' || typeof date === 'string')) {\n    validDate = new Date(date);\n}\nif (Object.prototype.toString.call(validDate) === '[object Date]') {\n    if (isNaN(validDate)) {\n    return '-';\n    }\n    const opt = { ...options, ...(timeZone ? { timeZone } : {}) };\n    validDate = validDate.toLocaleDateString(locale, opt);\n    return /-/.test(validDate) ? validDate.replace(/-/g, ' ') : validDate;\n}\nreturn '-';\n}\n\nfunction filterPostingCategory(postingCategories, categoryKey, totalKey) {\nconst { categories = [] } = postingDetail;\nif (!Array.isArray(categories)) {\n    return [];\n}\nconst filteredCategory = categories.filter((category) =>\n    postingCategories.includes(category.categoryCode)\n);\nlet total = 0;\nconst formattedCategory = filteredCategory.map((category) => {\n    total = total + (category.recordType === 'CREDIT' ? category.value : 0);\n    const value = localizeAmount(category?.value,currency) || '';\n    return {\n    category: category.categoryCode,\n    debit: category.recordType === 'DEBIT' ? value : '',\n    credit: category.recordType === 'CREDIT' ? value : '',\n    };\n});\n\nreturn {\n    [totalKey]: localizeAmount(total,currency),\n    [categoryKey]: formattedCategory,\n};\n}\n\nfunction getPseudoLedgersData() {\n    const { categories = [] } = postingDetail;\n    let pseudoLedgers = {\n      pseudoLedgerOne: [],\n      pseudoLedgerOneTotal: 0,\n      pseudoLedgerTwo: [],\n      pseudoLedgerTwoTotal: 0,\n      pseudoLedgersOthers: [],\n      pseudoLedgersOthersTotal: 0,\n    };\n    if (!Array.isArray(categories)) {\n      return pseudoLedgers;\n    }\n    pseudoLedgers = categories.reduce((pseudoLedgers, category) => {\n      const catValue = category.recordType === 'CREDIT' ? category.value : 0;\n      const value = localizeAmount(category?.value, currency) || '';\n      const formattedCat = {\n        category: category.categoryCode,\n        debit: category.recordType === 'DEBIT' ? value : '',\n        credit: category.recordType === 'CREDIT' ? value : '',\n      };\n      if (context.pseudoLedgerOneCode?.includes(category.categoryCode)) {\n        pseudoLedgers.pseudoLedgerOneTotal = pseudoLedgers.pseudoLedgerOneTotal + catValue;\n        pseudoLedgers.pseudoLedgerOne.push(formattedCat);\n      } else if (context.pseudoLedgerTwoCode?.includes(category.categoryCode)) {\n        pseudoLedgers.pseudoLedgerTwoTotal = pseudoLedgers.pseudoLedgerTwoTotal + catValue;\n        pseudoLedgers.pseudoLedgerTwo.push(formattedCat);\n      } else {\n        pseudoLedgers.pseudoLedgersOthersTotal = pseudoLedgers.pseudoLedgersOthersTotal + catValue;\n        pseudoLedgers.pseudoLedgersOthers.push(formattedCat);\n      }\n      return pseudoLedgers;\n    }, pseudoLedgers);\n\n    pseudoLedgers.pseudoLedgerOneTotal = localizeAmount(\n      pseudoLedgers.pseudoLedgerOneTotal,\n      currency\n    );\n    pseudoLedgers.pseudoLedgerTwoTotal = localizeAmount(\n      pseudoLedgers.pseudoLedgerTwoTotal,\n      currency\n    );\n    pseudoLedgers.pseudoLedgersOthersTotal = localizeAmount(\n      pseudoLedgers.pseudoLedgersOthersTotal,\n      currency\n    );\n    return pseudoLedgers;\n}\nfunction getRelatedHoldDetails() {\n  const { attributes ,postingID, transactionID ,bookTime,remarks,valueTime,timestamp} = postingDetail;\n  const relatedHoldPosting = {\n    postingID: postingID || '-',\n    voucherCode: attributes?.['journal.voucherCode'] || '-',\n    transactionID: transactionID || '-',\n    description: remarks || '-',\n    holdAmount : localizeAmount(attributes?.['hold.value'],currency) || '-',\n    bookDate: getLocaleDateString(bookTime),\n    valueDate: getLocaleDateString(valueTime),\n  };\n  return {relatedHoldPosting};\n}\nfunction getHoldDetails() {\nconst { attributes } = postingDetail;\nlet isDomestic = '-';\nif(attributes?.['super-card.isDomestic'] === true || attributes?.['super-card.isDomestic'] === \"true\"){\n  isDomestic = 'Yes';\n}else if(attributes?.['super-card.isDomestic'] === false || attributes?.['super-card.isDomestic'] === \"false\"){\n  isDomestic = 'No';\n}\nlet holdExpiryTime = '-'; \nif(attributes?.['hold.expiryTime']){\n  try{\n    const holdExpiryJsonObj = JSON.parse(attributes?.['hold.expiryTime']);\n    if(holdExpiryJsonObj?.timestamp){\n       holdExpiryTime = getLocaleDateString(holdExpiryJsonObj?.timestamp);\n    }\n  }catch(e){\n    //error while parsing json\n  }\n}\n\n\nconst holdDetails = {\n    holdId: attributes?.['hold.ids'] || '-',\n    purposeCode: attributes?.['hold.purposeCode'] || '-',\n    postingCode: attributes?.['metaPosting.postingCode'] || '-',\n    isDomestic: isDomestic,\n    merchantName: attributes?.['super-card.merchant-name'] || '-',\n    holdExpiryTime: holdExpiryTime,\n};\nreturn {holdDetails};\n}\nfunction getPostingSummary() {\nconst { postingID, transactionID, remarks, bookTime, valueTime, timestamp } =\n    postingDetail;\n    let label = 'Posting Summary';\n    if(context?.viewType === 'ALLOCATION'){\n      label = 'Allocation Meta Posting Summary';\n    }else if(context?.viewType === 'HOLD'){\n      label = 'Meta Posting Summary';\n    }\nreturn {\n    postingSummary: {\n    postingID: postingID || '-',\n    transactionID: transactionID || '-',\n    description: remarks || '-',\n    bookDate: getLocaleDateString(bookTime),\n    valueDate: getLocaleDateString(valueTime),\n    systemDate: getLocaleDateString(timestamp),\n    label: label,\n    },\n};\n}\n\nfunction getOriginalCreditPosting() {\nconst originalPosting =\n    postings?.find(\n    (posting) =>\n        posting.postingID ==\n        postingDetail?.attributes?.['ruby.redeemer-posting-id']\n    ) || {};\n\nconst {\n    postingID,\n    transactionID,\n    remarks,\n    bookTime,\n    valueTime,\n    value,\n    attributes = {},\n} = originalPosting || {};\nreturn {\n    originalCreditPosting: {\n    postingID: postingID || '-',\n    voucherCode: attributes?.['journal.voucherCode'] || '-',\n    transactionID: transactionID || '-',\n    amount: localizeAmount(value?.amount,value?.currency) || '-',\n    description: remarks || '-',\n    bookDate: getLocaleDateString(bookTime),\n    valueDate: getLocaleDateString(valueTime),\n    },\n};\n}\n\nfunction parseTag(tag, header) {\n  let [name, value, additionalParameters] = tag.split('//')[1].split('?')[0].split('/').concat(tag.split('?')[1]);\n  return { name, value, additionalParameters, header };\n}\n\nfunction getLedgerTags(){\n try {\n  if(postingDetail?.requestPayload?.signedDoc?.authorization?.postingCode === \"add-ledger-tags\"){\n    const tag = postingDetail.requestPayload.signedDoc.authorization.payload.tags[0];\n    return { tag: parseTag(tag, '')};\n  } \n  else if(postingDetail?.requestPayload?.signedDoc?.authorization?.postingCode === \"update-ledger-tags\"){\n    let tags = JSON.parse(postingDetail.attributes['ledger.changed']);\n    return {tag1: parseTag(tags.before.tags[0], 'Before'), tag2: parseTag(tags.after.tags[0], 'After')};\n  } \n  else if(postingDetail?.requestPayload?.signedDoc?.authorization?.postingCode === \"replace-ledger-tags\"){\n      let tag1 = postingDetail.requestPayload.signedDoc.authorization.payload.sourceTag;\n      let tag2 = postingDetail.requestPayload.signedDoc.authorization.payload.targetTag;\n      return {tag1: parseTag(tag1, 'Source'), tag2: parseTag(tag2, 'Target')};\n  } else {\n    return {};\n  }\n  }catch(error){\n    console.error('Error while fetching ledger tags',error);\n    return {};\n  }\n}\n\nfunction buildPostingDetailsBasedOnViewType() {\nlet processedViewData = {};\nif (context?.viewType === 'ALLOCATION') {\n    processedViewData = {\n    ...getPostingSummary(),\n    ...getOriginalCreditPosting(),\n    ...getPseudoLedgersData(),\n    postingList: postings,\n    currency,\n    locale,\n    postingHeader: 'Allocation Meta Posting Summary',\n    };\n} else if (context?.viewType === 'HOLD') {\n    processedViewData = {\n    ...getPostingSummary(),\n    ...getHoldDetails(),\n    ...getRelatedHoldDetails(),\n    ...getPseudoLedgersData(),\n    postingList: postings,\n    currency,\n    locale,\n    postingHeader: 'Meta Posting Summary',\n    };\n} else if (['ADD_LEDGER_TAGS', 'UPDATE_LEDGER_TAGS', 'REPLACE_LEDGER_TAGS'].includes(context?.viewType)){\n    processedViewData = {\n    ...getPostingSummary(),\n    ...getLedgerTags(),\n    postingList: postings,\n    currency,\n    locale,\n    postingHeader: 'Meta Posting Summary',\n    };\n} else {\n    processedViewData = {\n    ...getPostingSummary(),\n    ...getPseudoLedgersData(),\n    ...getLedgerTags(),\n    postingList: postings,\n    currency,\n    locale,\n    postingHeader: 'Posting Summary',\n    };\n}\nprocessedViewData.viewType = context?.viewType;\nreturn processedViewData;\n}\nconsole.log(buildPostingDetailsBasedOnViewType());\nreturn buildPostingDetailsBasedOnViewType();"}, {"type": "PREFILL", "refId": "coupons", "httpRequest": {"url": "<%= params.couponBaseUrl%>/ruby/tenants/<%= params.tenantId %>/accounts/<%= params.accountId %>/coupons?pageSize=1000&pageNo=1", "method": "GET", "comment": "", "cookies": [], "headers": [{"name": "x-zeta-authToken", "value": "<%= authToken %>"}], "bodySize": -1, "headersSize": -1, "httpVersion": "HTTP/1.1", "queryString": []}, "inputParams": [], "paginationParams": {}, "responseTransformer": "const couponList = res?.content || [];\nlet parsedCouponList = [];\nlet parsedPostingList = [];\nconst currencyMap = {\nINR: {\n    locale: 'en-IN',\n    decimal: 100,\n},\nBRL: {\n    locale: 'en-BR',\n    decimal: 100,\n},\nPHP: {\n    locale: 'en-PH',\n    decimal: 100,\n},\nVND: {\n    locale: 'en-VN',\n    decimal: 100,\n},\nEUR: {\n    locale: 'en-UK',\n    decimal: 100,\n},\nUSD: {\n    locale: 'en-US',\n    decimal: 100,\n},\nJPY: {\n    locale: 'en-JP',\n    decimal: 100,\n},\nCNY: {\n    locale: 'en-CN',\n    decimal: 100,\n},\nCAD: {\n    locale: 'en-CA',\n    decimal: 100,\n},\nSGD: {\n    locale: 'en-SG',\n    decimal: 100,\n},\nAED: {\n    locale: 'en-AE',\n    decimal: 100,\n},\nHKD: {\n    locale: 'en-HK',\n    decimal: 100,\n},\nAUD: {\n    locale: 'en-AU',\n    decimal: 100,\n},\nOMR: {\n    locale: 'en-OM',\n    decimal: 1000,\n},\n};\n\nconst currency = data1?.currency || context?.currency;\nconst locale = context?.locale || 'en-IN';\nconst timeZone = context?.timeZone || '';\n\nconst couponStatus = {\n  ACTIVATED: 'success',\n  ACTIVE: 'success',\n  REDEEMED: 'success',\n  EXPIRED: 'danger',\n  ABANDONED: 'danger',\n};\n\nconst couponState = {\n  active: \"Active\",\n  activated: \"Active\",\n  redeemed: \"Redeemed\",\n  redeemedFully: \"Fully Redeemed\",\n  redeemedPartially: \"Partially Redeemed\",\n  expired: \"Expired\",\n  reissued: \"Reissued\",\n  abandoned: \"Abandoned\"\n};\n\nfunction addDecimalToAmount(amount, currency) {\n// If the currency is not available then pass the amount as it is\nconst divisionBy = currencyMap[currency]?.decimal || 1;\nreturn amount / divisionBy;\n}\nfunction localizeAmount(amount,currency) {\nif (isNaN(amount)) {\n    return '';\n} else if (currency) {\n    amount = addDecimalToAmount(amount, currency);\n    const currencyLocale = currencyMap[currency]?.locale || context?.locale || 'en-IN';\n    return amount.toLocaleString(currencyLocale, {\n    \t\t\t\tmaximumFractionDigits: 2,\n    \t\t\t\tstyle: 'currency',\n    \t\t\t\tcurrency: currency,\n\t\t\t\t\t});\n}\nreturn amount;\n}\n\nfunction getLocaleDateString(\n  date,\n  options = { year: \"numeric\", month: \"short\", day: \"numeric\" }\n) {\n    let validDate = date;\n    if (\n      validDate &&\n      (typeof date === \"number\" || typeof date === \"string\")\n    ) {\n      validDate = new Date(date);\n    }\n    if (Object.prototype.toString.call(validDate) === \"[object Date]\") {\n      if (isNaN(validDate)) {\n        return \"-\";\n      }\n      const opt = { ...options, ...(timeZone ? { timeZone } : {}) };\n      validDate = validDate.toLocaleDateString(locale, opt);\n      return /-/.test(validDate) ? validDate.replace(/-/g, \" \") : validDate;\n    }\n    return \"-\";\n}\n\nfunction parseCoupon() {\n  parsedCouponList = couponList.reduce(\n    (couponAccumalator, coupon) => {\n      const couponData = {\n          ...couponAccumalator,\n      };\n      if (couponData.basedOnCurn[coupon.curn]) {\n          couponData.basedOnCurn[coupon.curn].push(coupon);\n      } else {\n          couponData.basedOnCurn[coupon.curn] = [coupon];\n      }\n      if (couponData.basedOnPosting[coupon.postingID]) {\n          couponData.basedOnPosting[coupon.postingID].push(coupon);\n      } else {\n          couponData.basedOnPosting[coupon.postingID] = [coupon];\n      }\n      couponData.basedOnCouponID[coupon.id] = coupon;\n      return couponData;\n    },\n    {\n      basedOnCurn: {},\n      basedOnPosting: {},\n      basedOnCouponID: {},\n    },\n  );\n}\n\nfunction parsePosting() {\n  parsedPostingList = data1.postingList.reduce(\n    (postingAccumulator, posting) => {\n      // Based on Posting ID\n      const tempPostingAccumulator = { ...postingAccumulator };\n      tempPostingAccumulator.basedOnPostingID[posting.postingID] = posting;\n      return tempPostingAccumulator;\n    },\n    {\n      basedOnPostingID: {},\n    }\n  );\n}\n\nfunction parseRedemptionData() {\n    let redemptionPostings = [];\n\n    if (!params.postingId) {\n        return {};\n    }\n\n    // In case of posting view we need to show only that posting redemption\n    redemptionPostings = [parsedPostingList.basedOnPostingID[params.postingId]];\n\n    const redemptionCouponData = redemptionPostings.map((posting) => {\n      \n      let redeemerPostingID = '';\n      if (posting.attributes) {\n        redeemerPostingID = posting.attributes[\n          \"ruby.redeemer-posting-id\"\n        ];\n      }\n\n      const creditPosting = parsedPostingList.basedOnPostingID[redeemerPostingID];\n      // New coupons are coupons belonging to the redemption posting and arranged on the basis of CURN\n      const newCoupons = parsedCouponList.basedOnPosting[posting.postingID]?.reduce(\n        (couponAccumalator, coupon) => {\n            couponAccumalator[coupon.curn] = coupon;\n            return couponAccumalator;\n        },\n        {},\n      );\n      let oldCoupons = [];\n      let postingAction = '';\n\n      if (posting?.requestPayload?.signedDoc?.authorization) {\n        const postingCode = posting?.requestPayload?.signedDoc?.authorization?.postingCode;\n        // For different posting code, we have different ways to get coupon IDs\n        let couponList = [];\n        switch (postingCode) {\n          case \"redeem-coupons\":\n            couponList =\n                posting.requestPayload.signedDoc.authorization?.payload?.couponRedemptions?.map(\n                    (coupon) => coupon.couponID,\n                );\n            postingAction = \"redeemCoupons\";\n            break;\n          case \"expire-coupons\":\n            postingAction = \"expireCoupons\";\n            couponList = posting.requestPayload.signedDoc.authorization?.payload?.matchFilters?.couponIDs;\n            break;\n          case \"change-coupon-terms\":\n            postingAction = \"changeCouponTerms\";\n            couponList = posting.requestPayload.signedDoc.authorization?.payload?.matchFilters?.couponIDs;\n            break;\n        }\n        // Based on Coupon list, get the values from parsedCoupon data\n        couponList.forEach((couponID) => {\n           if(parsedCouponList.basedOnCouponID[couponID]){\n            oldCoupons.push(parsedCouponList.basedOnCouponID[couponID]);\n           }\n        });\n\n      // Create the coupon table information\n      const tableInfo = [];\n      let total = 0;\n      for (let coupon of oldCoupons) {\n        let newCouponData = {\n            id: '',\n            newValue: 0,\n            newDate: '-',\n        };\n        // If the same curn id (old coupon) is available in new coupon or not\n        if (newCoupons && newCoupons[coupon.curn]) {\n            newCouponData.id = newCoupons[coupon.curn].id;\n            newCouponData.newValue = newCoupons[coupon.curn]?.value;\n            newCouponData.newDate = getLocaleDateString(newCoupons[coupon.curn].valueTime);\n        }\n        const creditAttributed = coupon.value - newCouponData.newValue;\n        const redemptionStatus = coupon?.redemptionInfo?.redemptionType;\n        let couponStateValue = '';\n        let state = 'warning';\n        if (redemptionStatus === 'PARTIAL' && coupon.state === 'REDEEMED') {\n          couponStateValue = couponState.redeemedPartially;\n          state = 'warning';\n        } else if(redemptionStatus === 'FULL' && coupon.state === 'REDEEMED'){\n          couponStateValue = couponState.redeemedFully;\n          state = 'success';\n        } else {\n          couponStateValue = coupon.state;\n          state = couponStatus[coupon.state] || 'warning';\n        }\n        const tableRow = {\n            curnID: coupon.curn,\n            oldCouponID: coupon.id,\n            oldValueDate: getLocaleDateString(coupon.valueTime),\n            oldValue: localizeAmount(coupon?.value,currency),\n            status: couponStateValue,\n            state: state || 'warning',\n            newCouponID: newCouponData.id,\n            newValueDate: getLocaleDateString(newCouponData.newDate),\n            newValue: localizeAmount(newCouponData.newValue,currency),\n            creditAttributed: localizeAmount(creditAttributed,currency),\n            isCouponLinkable: context.isCouponLinkable\n        };\n        total = total + (creditAttributed || 0);\n        tableInfo.push(tableRow);\n      }\n      return {\n          redemptionPosting: posting,\n          creditPosting,\n          tableInfo,\n          postingAction,\n          totalCreditAttributed: localizeAmount(total,currency),\n      };\n      }\n    });\n\n    return redemptionCouponData;\n}\n\nparseCoupon();\nparsePosting();\n\nconst parsedData = parseRedemptionData();\n\nconst couponData = { \n  tableInfo: parsedData?.[0]?.tableInfo || [],\n  totalCreditAttributed: parsedData?.[0]?.totalCreditAttributed\n};\nreturn couponData;"}], "viewConfig": {"css": ".z-data-grid {\n  padding: 0 !important;\n}\n.z-table {\n  max-height: 271px;\n  overflow-x: scroll;\n}\n.view.posting-coupons-view .z-table {\n  min-width: 1320px;\n}\n.debit { \n  color: #BE304A; \n}\n.credit { \n  color: #0D871D;\n}\n.credit-align {\n  padding-right: 3px;\n}\n.is-numeric { \n  display: flex;\n  justify-content: flex-end;\n  text-align: right;\n}\n.view.posting-original-credit-view,\n.view.posting-related-view{ \n  background: #F6F7FA;\n}\n.view.posting-original-credit-view .z-button,\n.view.posting-related-view .z-button {\n  background-color: #F6F7FA;\n}\n.content-value { \n  word-break: break-word;\n}\n.temp-btn .button span {\n  padding-left: 0 !important;\n}\ntd .column-container a,\ntd .column-container .link {\n  color: #2a4ff0;\n  font-weight: 500;\n  max-width: 83px;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\ntd .column-container a:hover {\n  color: #263eb3;\n}\n.z-button.button.link:hover span {\n  border-bottom: none !important;\n}\n.table-footer {\n  background-color: #F1F2F8;\n  min-height: 45px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  padding: 12px 20px;\n}\n.total-key {\n  color: #020D4D;\n}\n.creditAttributed {\n  font-weight: 500 !important;\n  padding-right: 3px;\n  color: #020D4D;\n}\n.z-component.z-default-tab .z-tabs nav ul li {\n  margin-right: 12px;\n}", "views": [{"class": "posting-summary-view", "header": {"title": {"value": "data1.postingHeader"}}, "sections": [{"key": "postingSummary", "fields": [{"field": "postingID", "label": "Posting ID"}, {"field": "transactionID", "label": "Transaction ID"}, {"field": "description", "label": "Description"}, {"field": "bookDate", "label": "Book Date"}, {"field": "valueDate", "label": "Value Date"}, {"field": "systemDate", "label": "System Date"}], "attributes": {"hasBottomDivider": true}}]}, {"class": "posting-hold-view", "header": {"title": "Hold Details"}, "sections": [{"key": "holdDetails", "fields": [{"field": "purposeCode", "label": "Purpose Code"}, {"field": "postingCode", "label": "Posting Code"}, {"field": "isDomestic", "label": "Is Domestic"}, {"field": "holdId", "label": "Hold Id"}, {"field": "merchantName", "label": "Merchant Name"}, {"field": "holdExpiryTime", "label": "Expiry date"}], "condition": {"visibleIf": {"===": [{"var": "prefillData.data1.viewType"}, "HOLD"]}}, "attributes": {"hasBottomDivider": true}}]}, {"class": "posting-original-credit-view", "sections": [{"key": "originalCreditPosting", "type": "accordion", "label": "Original Credit Posting", "fields": [{"type": "template", "field": "postingID", "label": "Posting ID", "value": "<div class=\"temp-btn\"><z-button variant=\"link\" @click=\"handleClick\">{{ data1.originalCreditPosting.postingID }}</z-button></div>"}, {"field": "voucherCode", "label": "Voucher Code"}, {"field": "transactionID", "label": "Transaction ID"}, {"field": "amount", "label": "Amount"}, {"field": "description", "label": "Description"}, {"field": "bookDate", "label": "Book Date"}, {"field": "valueDate", "label": "Value Date"}], "condition": {"visibleIf": {"===": [{"var": "prefillData.data1.viewType"}, "ALLOCATION"]}}, "attributes": {"hasBottomDivider": true}, "accordionAttributes": {"isOpen": true}}]}, {"class": "posting-related-view", "sections": [{"key": "relatedHoldPosting", "type": "accordion", "label": "Related Hold Posting Details", "fields": [{"type": "template", "field": "postingID", "label": "Posting ID", "value": "<div class=\"temp-btn\"><z-button variant=\"link\" @click=\"handleClick\">{{ data1.relatedHoldPosting.postingID }}</z-button></div>"}, {"field": "voucherCode", "label": "Voucher Code"}, {"field": "transactionID", "label": "Transaction ID"}, {"field": "holdAmount", "label": "Hold Amount"}, {"field": "description", "label": "Description"}, {"field": "bookDate", "label": "Book Date"}, {"field": "valueDate", "label": "Value Date"}], "condition": {"visibleIf": {"===": [{"var": "prefillData.data1.viewType"}, "HOLD-HIDDEN"]}}, "attributes": {"hasBottomDivider": true}, "accordionAttributes": {"isOpen": true}}]}, {"class": "tag-details-view", "header": {"title": {"value": "Tag Details"}}, "sections": [{"key": "tag", "fields": [{"field": "name", "label": "Tag type"}, {"field": "value", "label": "Tag value"}, {"field": "additionalParameters", "label": "Additional parameters"}], "attributes": {"hasBottomDivider": true}}], "condition": {"visibleIf": {"===": [{"var": "prefillData.data1.viewType"}, "ADD_LEDGER_TAGS"]}}}, {"class": "tag-details-view", "header": {"title": {"value": "Tag Details"}}, "sections": [{"key": "tag1", "label": "data1.tag1.header", "fields": [{"field": "name", "label": "Tag type"}, {"field": "value", "label": "Tag value"}, {"field": "additionalParameters", "label": "Additional parameters"}], "attributes": {"hasBottomDivider": true}}, {"key": "tag2", "label": "data1.tag2.header", "fields": [{"field": "name", "label": "Tag type"}, {"field": "value", "label": "Tag value"}, {"field": "additionalParameters", "label": "Additional parameters"}], "attributes": {"hasBottomDivider": true}}], "condition": {"visibleIf": {"or": [{"===": [{"var": "prefillData.data1.viewType"}, "UPDATE_LEDGER_TAGS"]}, {"===": [{"var": "prefillData.data1.viewType"}, "REPLACE_LEDGER_TAGS"]}]}}}], "version": "v2"}}