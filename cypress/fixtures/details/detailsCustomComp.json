{"dataConfig": [{"type": "PREFILL", "refId": "data1", "httpRequest": {"url": "http://localhost:4000/get-data", "method": "GET"}, "inputParams": [], "paginationParams": {}, "responseTransformer": "return { ...res }"}], "viewConfig": {"version": "v2", "views": [{"sections": [{"key": "first-custom-comp", "type": "custom", "config": {"name": "payment-details", "package": {"name": "payment-views", "version": "1.2.16"}, "props": {"ifi-id": {"value": "params.tenantId"}, "payment-id": {"value": "params.paymentId"}, "resource-id": {"value": "params.resourceId"}, "transaction-id": {"value": "params.transactionId"}, "service-base-url": {"value": "params.baseUrl"}, "payment-v2-service-url": {"value": "params.baseUrl"}, "atalanta-url": {"value": "params.baseUrl"}, "config": {"value": "context.compConfig"}}}}]}]}}