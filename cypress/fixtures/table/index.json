{"businessEntityId": "test-entity", "componentType": "data-table", "viewConfig": {"css": ".phase--failed{\n  color: #f04f6d;\n}\n.phase--successful{\n  color: #49cf55;\n}\n.phase--danger{\n  color: #f09948;\n}\na {\n  text-decoration: none;\n}", "version": "v2", "title": "All Workers", "description": "This is the worker list", "isFrontEndFilter": true, "tableConfig": {"sortIcon": "arrow-up", "columns": [{"field": "phase", "label": "PHASE", "sortable": false, "width": "80px", "pinned": "left", "component": {"template": "<span :class=\"`phase--${$attrs.data.row.status.toLowerCase()}`\">{{$attrs.data.row.phase}}</span>"}}, {"field": "worker<PERSON>ame", "label": "WORKER", "align": "left", "sortable": true, "width": "280px", "render": "return h('a',{ href:'#',onClick:(event)=>{ event.target.dispatchEvent(new CustomEvent('worker-row-clicked',{bubbles:true,composed:true,detail:row})) }},row.workerName)"}, {"field": "periodicity", "label": "PERIODICITY", "align": "right", "titleAlign": "center", "sortable": false, "width": "150px"}, {"field": "startTime", "label": "START TIME", "render": "const dateOptions={day:\"numeric\",month:\"short\",year:\"numeric\",hour:\"numeric\",minute:\"numeric\",second:\"numeric\",hour12:true};const date = new Date(row.startTime).toLocaleDateString(context.locale, dateOptions); return h('span',{},date)", "sortable": false, "width": "225px"}, {"field": "endTime", "label": "END TIME", "render": "const dateOptions={day:\"numeric\",month:\"short\",year:\"numeric\",hour:\"numeric\",minute:\"numeric\",second:\"numeric\",hour12:true};const date = new Date(row.updatedTime).toLocaleDateString(context.locale, dateOptions); return h('span',{},date)", "sortable": false, "width": "225px"}, {"field": "totalEntityCount", "label": "TOTAL LEDGERS", "sortable": false, "width": "200px", "numeric": true}, {"field": "successfulEntityCount", "label": "PASSED LEDGERS", "align": "left", "sortable": false, "width": "200px", "pinned": "left", "numeric": true}, {"field": "failedEntityCount", "label": "FAILED LEDGERS", "align": "left", "sortable": false, "width": "200px", "numeric": true}, {"label": "STATUS", "field": "status", "width": "150px", "pinned": "right", "render": "<ZTag :label=\"data.row.status\" indicator=\"dot\" :type=\"data.row.status==='Successful'?'success':data.row.status === 'Failed'? 'danger':data.row.status==='In Progress'?'warning':''\" emphasis=\"transparent\"/>"}], "detailed": false, "ShowDetailIcon": true, "hoverable": true, "striped": true, "checkable": false, "stickyColumnActions": false, "stickyFirstColumn": false}, "tableActions": [{"label": "Test Is the name", "value": "test", "icon": "AddFilled", "callback": "return console.log('<PERSON><PERSON>  was called on the config',tenantId, context);"}, {"label": "Test1", "value": "test1", "disabled": true}], "filterConfig": [{"field": "phase", "fixed": true, "selectors": {"by-value": {"multiple": true, "searchable": true, "show-indicator": true, "options": [{"label": "BOPI", "value": "BOPI"}, {"label": "BOFI", "value": "BOFI"}, {"label": "EOPI", "value": "EOPI"}, {"label": "EOFI", "value": "EOFI"}, {"label": "EOP", "value": "EOP"}]}}, "label": "Phase"}, {"field": "worker<PERSON>ame", "label": "Worker", "fixed": true, "multiple": true, "searchable": true, "selectors": {"by-value": {"multiple": true, "searchable": true, "actions": ["apply", "clear", "reset"]}}}], "paginationConfig": {"page": 1, "pageSize": 5, "showSizePicker": true, "pageSizes": [5, 10, 20], "onChange": "(page: number) => {paginationConfig.page = page;}", "onUpdatePageSize": "(pageSize: number) => {paginationConfig.pageSize = pageSize;paginationConfig.page = 1;}"}}, "dataConfig": {"refId": "eod-center-workers-list", "httpRequest": {"url": "<%= params.ATALANTA_BASE_URL %>/orchestra/api/v1/tenants/<%= params.tenantId %>/coas/<%= params.coaId %>/workers-list?startPeriodId=<%= params.startPeriodId %>&endPeriodId=<%= params.endPeriodId %>&includeWorkerRuns=true&orchestraUrl=<%= params.orchestraUrl %>", "method": "GET", "bodySize": -1, "headersSize": -1, "httpVersion": "HTTP/1.1", "headers": [{"name": "Authorization", "value": "Bearer "}]}, "inputParams": [], "apiAdopter": "financeCenterAPI", "cacheBurstTime": 0, "paginationParams": {}, "responseTransformer": "return (function (workerList) {\n  return workerList.map((worker)=>{\n    return {\n      ...worker,\n      phase: worker.phase === 'CLOSED'? 'EOP': worker.phase,\n    }\n  })\n})(res)"}}