{"viewConfig": {"version": "v2", "fields": {"searchPanel": {"type": "custom", "className": "custom-ach-search-container", "config": {"name": "search", "package": {"name": "ach-search", "version": "0.4.6"}, "props": {"ifi-id": {"value": "context.metadata.tenantId"}, "user-id": {"value": "formModel.name"}, "name": {"value": {"handler": "return formModel.name || formModel.remarks || 'No Name'"}}, "api-value": {"value": {"handler": "return prefillData.data1.serviceValue || 'No serviceValue'"}}, "service-base-url": {"value": "http://localhost:5000/mars"}, "config": {"value": "prefillData.data2.searchConfig"}, "aan-search-url": "http://localhost:5000/atalanta", "card-search-url": "http://localhost:5000/fusion-tomcat"}, "events": {"accountHolderSearch_linkClicked": "console.log(\"accountHolderSearch_linkClicked body executed\", eventData);\nreturn {selectedAch: {...eventData}};"}}}, "name": {"type": "text", "label": "Name", "direction": "horizontal"}, "valueTime": {"type": "text", "label": "Value Date", "direction": "horizontal"}, "remarks": {"type": "text", "label": "Description"}}, "form": {"sections": [{"fields": [{"key": "name"}, {"key": "remarks"}, {"key": "valueTime"}]}, {"title": "Account Holder Search", "subtitle": "Custom Business component to be rendered", "customField": "searchPanel"}], "buttons": [{"text": "Submit", "action": "submit"}, {"text": "Cancel", "action": "cancel"}]}}, "dataConfig": [{"httpRequest": {"method": "POST", "url": "http://localhost:5000/post-data", "postData": {"mimeType": "application/json", "params": [], "text": "<%= JSON.stringify(formModel) %>"}, "additional": {}}, "inputParams": [], "paginationParams": {}}, {"type": "PREFILL", "refId": "data1", "httpRequest": {"method": "GET", "url": "http://localhost:5000/get-data"}, "inputParams": [], "responseTransformer": "return { userId: 'uyughjbjhj9898hjhj' ,serviceValue: 'service Value'}"}, {"type": "PREFILL", "refId": "data2", "httpRequest": {"method": "GET", "url": "http://localhost:5000/get-data"}, "inputParams": [], "responseTransformer": "const searchConfig= JSON.stringify({\"real\":{\"types\":[\"emailId\",\"phone\",\"id\",\"name\",\"crn\",\"card\",\"aan\"]},\"featureFlags\":{\"aanApiVersion\":\"v2\"},\"useV2SearchExperience\":true});\nreturn { ...res, searchConfig, postingHeader: 'Posting Summary' };"}]}