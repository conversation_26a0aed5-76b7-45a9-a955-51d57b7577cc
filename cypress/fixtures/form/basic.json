{"viewConfig": {"version": "v2", "fields": {"valueTime": {"type": "text", "label": "Value Date", "direction": "horizontal"}, "remarks": {"type": "text", "label": "Description"}}, "form": {"sections": [{"fields": [{"key": "remarks"}, {"key": "valueTime"}]}], "buttons": [{"text": "Submit", "action": "submit"}, {"text": "Cancel", "action": "cancel"}, {"text": "Reset", "action": "reset"}]}}, "dataConfig": [{"refId": "data1", "httpRequest": {"method": "GET", "url": "http://localhost:5000/get-data"}, "inputParams": [], "responseTransformer": "return { ...res }"}]}