{"viewConfig": {"version": "v2", "fields": {"nooffset": {"label": "No Offset", "description": "This is a datetime field with no offset", "component": "AngelosDatePicker", "value": "2015-01-10T00:00:00.123+07:00"}, "datetime2": {"label": "DateTime SYSTEM", "description": "This is a datetime field with system timezone format", "component": "AngelosDatePicker", "value": "2015-01-10T00:00:00.123+07:00", "dateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "outputDateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "timezone": "SYSTEM"}, "datetime3": {"label": "DateTime PRESERVE", "description": "This is a datetime field with preserve timezone format", "component": "AngelosDatePicker", "value": "2015-01-10T00:00:00.123+07:00", "dateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "outputDateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "timezone": "PRESERVE"}, "datetime4": {"label": "DateTime UTC", "description": "This is a datetime field with utc timezone format", "component": "AngelosDatePicker", "value": "2015-01-10T00:00:00.123+07:00", "dateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "outputDateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "timezone": "UTC"}, "appDateTime": {"label": "DateTime APP", "description": "This is a datetime field with app timezone format", "component": "AngelosDatePicker", "value": "2015-01-10T00:00:00.123+07:00", "dateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "outputDateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "showOffsetPrefix": true}, "date2": {"label": "Date SYSTEM", "description": "This is a date field with system timezone format", "component": "AngelosDatePicker", "value": "2015-01-10", "dateFormat": "YYYY-MM-DD", "outputDateFormat": "YYYY-MM-DD", "timezone": "SYSTEM"}, "date3": {"label": "Date PRESERVE", "description": "This is a date field with preserve timezone format", "component": "AngelosDatePicker", "value": "2015-01-10", "dateFormat": "YYYY-MM-DD", "outputDateFormat": "YYYY-MM-DD", "timezone": "PRESERVE"}, "date4": {"label": "Date UTC", "description": "This is a date field with utc timezone format", "component": "AngelosDatePicker", "value": "2015-01-10", "dateFormat": "YYYY-MM-DD", "outputDateFormat": "YYYY-MM-DD", "timezone": "UTC"}, "appDate": {"label": "Date APP", "description": "This is a date field with app timezone format", "component": "AngelosDatePicker", "value": "2015-01-10", "dateFormat": "YYYY-MM-DD", "outputDateFormat": "YYYY-MM-DD", "showOffsetPrefix": true}}, "form": {"sections": [{"fields": [{"key": "nooffset"}, {"key": "datetime1"}, {"key": "datetime2"}, {"key": "datetime3"}, {"key": "datetime4"}, {"key": "appDateTime"}, {"key": "date1"}, {"key": "date2"}, {"key": "date3"}, {"key": "date4"}, {"key": "appDate"}]}]}}, "dataConfig": [{"refId": "data1", "httpRequest": {"method": "GET", "url": "http://localhost:5000/get-data"}, "inputParams": [], "responseTransformer": "return { ...res }"}, {"httpRequest": {"url": "http://localhost:5000/save-data", "method": "POST", "postData": {"mimeType": "application/json", "params": [], "text": "<%= JSON.stringify(formModel) %>"}}}]}