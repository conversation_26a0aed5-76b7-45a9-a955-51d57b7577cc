{"viewConfig": {"version": "v2", "fields": {"reasonCode": {"name": "Reason Code", "label": "Reason Code", "rules": {"required": {"value": true, "message": "Reason Code is required"}}, "placeholder": "Select Reason Code", "indicatorType": "mandatory"}, "code": {"name": "Code", "label": "Code", "rules": {"required": {"value": true, "message": "Code is required"}}, "placeholder": "Select Code", "indicatorType": "mandatory"}}, "form": {"sections": [{"direction": "horizontal", "title": "Section Title", "subtitle": "lorem ipsum dolor sit amet consectetue 2", "layoutType": "accordion", "headerAttributes": {"isBordered": false}, "sections": [{"accordionAttributes": {"title": "Accordion Title"}, "direction": "vertical", "title": "Section Title", "layoutType": "accordion", "subtitle": "lorem ipsum dolor sit amet consectetuer", "columns": 6, "sections": [{"accordionAttributes": {"title": "Accordion Title"}, "fields": [{"key": "reasonCode", "colspan": 12}]}]}, {"accordionAttributes": {"title": "Accordion Title"}, "direction": "vertical", "title": "Section Title", "layoutType": "accordion", "subtitle": "lorem ipsum dolor sit amet consectetuer", "columns": 6, "sections": [{"accordionAttributes": {"title": "Accordion Title"}, "fields": [{"key": "code", "colspan": 12}]}]}]}], "buttons": [{"action": "submit", "text": "Submit"}, {"action": "cancel", "text": "Cancel"}], "confirmationDialog": {"title": "Reversal confirmation", "template": "hello"}}}, "dataConfig": [{"type": "PREFILL", "refId": "data1", "httpRequest": {"url": "http://localhost:4000/get-data", "method": "GET"}, "inputParams": [], "paginationParams": {}, "responseTransformer": "return { ...res }"}, {"httpRequest": {"method": "POST", "url": "http://localhost:4000/save-data", "postData": {"mimeType": "application/json", "params": [], "text": "<%= JSON.stringify(formModel) %>"}, "additional": {}}, "inputParams": [], "paginationParams": {}}]}