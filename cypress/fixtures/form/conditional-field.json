{"viewConfig": {"version": "v2", "fields": {"name": {"label": "Name", "condition": {"visibleIf": {"===": [{"var": "formModel.fruits"}, "banana"]}}}, "fruits": {"label": "Fruits", "component": "AngelosRadioButton", "values": [{"label": "Apple", "value": "apple"}, {"label": "Banana", "value": "banana"}]}}, "form": {"sections": [{"title": "Test", "fields": ["name", "fruits"]}]}}, "dataConfig": [{"httpRequest": {"url": "http://localhost:5000/save-data", "method": "POST", "postData": {"mimeType": "application/json", "params": [], "text": "<%= JSON.stringify(formModel) %>"}}}]}