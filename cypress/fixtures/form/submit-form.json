{"viewConfig": {"version": "v2", "fields": {"fruit": {"label": "Fruit", "component": "AngelosRadioButton", "values": [{"label": "Mango", "value": "mango"}, {"label": "Orange", "value": "orange"}], "rules": {"required": true}}, "drink": {"label": "Drinks", "component": "AngelosCheckbox", "values": [{"label": "CocaCola", "value": "cocacola"}, {"label": "Pepsi", "value": "pepsi"}]}, "reason": {"label": "Reason", "name": "Reason", "component": "AngelosSelect", "values": [{"label": "Reason1", "value": "reason1"}, {"label": "Reason2", "value": "reason2"}], "rules": {"required": false}}, "inputRemarks": {"label": "Remarks", "name": "Remarks", "component": "AngelosTextArea", "maxlength": 140, "indicatorType": "optional", "placeholder": "Enter any additional remarks..."}, "bookTime": {"type": "text", "label": "Book Date", "component": "AngelosDatePicker", "value": "2023-07-19"}, "multipleInput": {"type": "text", "label": "Emails", "multiple": true}, "codeEditor": {"label": "Code editor", "component": "AngelosCodeEditor", "value": "\nconst a = 5;"}, "switch": {"label": "Enable", "component": "AngelosSwitch"}, "customDateField": {"label": "Date with custom timezone", "component": "AngelosDatePicker", "dateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "outputDateFormat": "YYYY-MM-DDTHH:mm:ss.SSSZ", "value": "2015-01-10T00:00:00.123+07:00", "timezone": "CUSTOM"}, "radioButtons": {"label": "Buttons", "component": "AngelosRadioButton", "type": "button", "values": [{"label": "Enable", "value": "enable"}, {"label": "Disable", "value": "disable"}]}}, "form": {"sections": [{"fields": [{"key": "reason"}, {"key": "fruit"}, {"key": "drink"}, {"key": "inputRemarks"}, {"key": "switch"}, {"key": "multipleInput"}, {"key": "bookTime"}, {"key": "customDateField"}, {"key": "codeEditor"}, {"key": "radioButtons"}]}], "buttons": [{"text": "Submit", "action": "submit"}, {"text": "Cancel", "action": "cancel"}], "confirmationDialog": {"title": "Confirmation", "template": "<div> Dynamic Value - {{formModel.drink}} {{tenantId}} {{entityId}} </div>", "buttons": [{"text": "Yes", "action": "proceed"}, {"text": "No", "action": "cancel"}], "isVisible": {"handler": "return formModel.fruit === 'orange';"}}}}, "dataConfig": [{"httpRequest": {"url": "http://localhost:5000/save-data", "method": "POST", "postData": {"mimeType": "application/json", "params": [], "text": "<%= JSON.stringify(formModel) %>"}}}]}