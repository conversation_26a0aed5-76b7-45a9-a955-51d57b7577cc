/// <reference types="cypress" />

const setup = () => {
    cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
        fixture: 'form/basic-custom-comp.json'
    }).as('get-component-type');
    cy.intercept('GET', 'http://localhost:5000/get-data', { fixture: 'response/data1.json' });
    cy.intercept(
        'POST',
        'http://localhost:5000/mars/tachyon/v4/ifi/*********/accountholders/search',
        { fixture: 'response/ach.data.json' }
    );
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
    cy.intercept('GET', '*/module.entry.js').as('get-module-entry');
    cy.visit('cypress/e2e/form/basic-custom-comp.html');
    cy.wait('@get-angelos');
    cy.on('uncaught:exception', () => {
        return false;
    });
};
const getShadowDom = () => cy.get('zwe-angelos-create-form-v3').shadow();
const getBusinessCompDom = () => cy.get('@zweSearch');
describe('Form with all field types', () => {
    beforeEach(setup);
    it('displays form', () => {
        getShadowDom()
            .find('.angelos-wrapper')
            .should('have.length', 1)
            .find('form')
            .should('have.length', 1)
            .find('input')
            .should('have.length', 3)
            .closest('form')
            .find('button')
            .should('have.length', 2);
        cy.compareSnapshot('form-basic');
    });

    it('displays form with business component', () => {
        getShadowDom()
            .find('.angelos-wrapper')
            .find('form')
            .find('.form-sections-container')
            .should('have.length', 1);

        getShadowDom()
            .find('.section-header__center')
            .find('h3')
            .should('have.text', 'Account Holder Search');

        getShadowDom().find('zwe-search').should('have.length', 1);
    });
    it('zwe-search component has attribute name with value "No Name"', () => {
        getShadowDom().find('zwe-search').should('have.attr', 'name', 'No Name');
    });
    it('zwe-search component has attribute api-value with value data1.test.serviceValue', () => {
        getShadowDom().find('zwe-search').should('have.attr', 'api-value', 'service Value');
    });
    it('changes input value and checks if it is reflected in zwe-search', () => {
        const name = 'John Doe';
        getShadowDom()
            .find('.angelos-wrapper')
            .find('form')
            .find('.form-sections-container')
            .find('input[placeholder="Please Input"]')
            .eq(0)
            .type(name);

        // Check if the input value is reflected
        getShadowDom().find('zwe-search').should('have.attr', 'name', name);
    });
    it('changes remarks value and checks if it is reflected in zwe-search', () => {
        const name = 'Remarks Updated';
        getShadowDom()
            .find('.angelos-wrapper')
            .find('form')
            .find('.form-sections-container')
            .find('input[placeholder="Please Input"]')
            .eq(1)
            .type(name);

        // Check if the input value is reflected
        getShadowDom().find('zwe-search').should('have.attr', 'name', name);
    });
    it('renders zwe-search component with correct elements', () => {
        getShadowDom().find('zwe-search').shadow().as('zweSearch');

        getBusinessCompDom().find('h2').should('have.text', 'Search New Profile');

        getBusinessCompDom().find('.search-form').should('have.length', 1);
        getBusinessCompDom()
            .find('.search-form')
            .find('input[placeholder="Email address"]')
            .should('have.length', 1);
    });

    it('renders zwe-search component with Full Name input and performs search', () => {
        getShadowDom().find('zwe-search').shadow().as('zweSearch');

        cy.get('@zweSearch')
            .find('input[placeholder="Full Name"]')
            .should('have.length', 1)
            .type('test');

        cy.get('@zweSearch').find('button').contains('Search').should('not.be.disabled').click();
        cy.compareSnapshot('search-clicked');
    });
});
