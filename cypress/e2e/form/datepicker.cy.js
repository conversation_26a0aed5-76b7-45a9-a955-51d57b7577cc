/// <reference types="cypress" />

const setup = () => {
    cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
        fixture: 'form/datepicker.form.json'
    }).as('get-component-type');
    cy.intercept('GET', 'http://localhost:5000/get-data', {
        fixture: 'response/datepicker.data.json'
    }).as('get-data');
    cy.intercept('POST', 'http://localhost:5000/save-data', { fixture: 'response/data1.json' }).as(
        'submit-post-call'
    );
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');

    cy.visit('cypress/e2e/form/index.html');
    cy.wait('@get-angelos');
};

describe('Datepicker without offset', () => {
    beforeEach(setup);

    it('displays date-picker field without offset', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#nooffset') // id for textarea field
            .get('.prepend-container')
            .should('not.exist');

        // cy.compareSnapshot('no-offset', Cypress.env('COMPARISON_THRESHOLD'));
    });
});

describe('DateTimePicker with static offset', () => {
    beforeEach(setup);

    it('displays correct offset for SYSTEM timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#datetime2') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });

    it('displays correct offset for PRESERVE timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#datetime3') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +07:00`);
    });

    it('displays correct offset for UTC timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#datetime4') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +00:00`);
    });

    it('displays correct offset for APP timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#appDateTime')
            .as('appDateTimeField') // id for appDateTme field
            .find('.prepend-container')
            .should('have.text', `(UTC) +01:00`);

        cy.get('@appDateTimeField').find('input').should('have.value', '2015-01-09 18:00:00'); // Value based on app timezone i.e Europe/Berlin(see index.html)
    });
});

describe('DatePicker with static offset', () => {
    beforeEach(setup);

    it('displays correct offset for SYSTEM timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#date2') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });

    it('displays correct offset for PRESERVE timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#date3') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });

    it('displays correct offset for UTC timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#date4') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +00:00`);
    });

    it('displays correct offset for APP timezone', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#appDate') // id for appDateTme field
            .find('.prepend-container')
            .should('have.text', `(UTC) +01:00`);
    });
});

describe.only('Use DatePicker and submit', () => {
    beforeEach(setup);
    /* eslint-disable cypress/unsafe-to-chain-command */

    function selectDateTime(dateId) {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find(dateId)
            .find('.z-date-picker')
            .find('input')
            .trigger('click')
            .as('openDatePicker');

        cy.get('@openDatePicker')
            .closest('.z-config-provider')
            .siblings('.v-binder-follower-container')
            .find('.z-date-panel-calendar')
            .find('.z-date-panel-dates')
            .find('.z-date-panel-date')
            .should('have.length', 42)
            .eq(4)
            .find('.z-date-panel-date__trigger')
            .click({ force: true })
            .as('selectedDate');
        cy.get('@selectedDate')
            .closest('.z-date-panel-calendar')
            .siblings('.z-date-panel-actions')
            .find('button')
            .first()
            .should('contain.text', 'Confirm')
            .click({ waitForAnimations: false });
    }

    function selectDate(dateId) {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find(dateId)
            .find('.z-date-picker')
            .find('input')
            .trigger('click', { force: true })
            .as('openDatePicker');

        cy.get('@openDatePicker')
            .closest('.z-config-provider')
            .siblings('.v-binder-follower-container')
            .find('.z-date-panel-calendar')
            .find('.z-date-panel-dates')
            .find('.z-date-panel-date')
            .eq(4)
            .click();
    }

    it('output correct values', () => {
        selectDateTime('#datetime2');

        selectDateTime('#datetime3');
        selectDateTime('#datetime4');
        selectDateTime('#appDateTime');
        selectDate('#nooffset');
        selectDate('#date2');
        selectDate('#date3');
        selectDate('#date4');
        selectDate('#appDate');

        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#angelos-v2-footer')
            .find('button')
            .first()
            .should('contain.text', 'Submit')
            .click();

        cy.wait('@submit-post-call').then((interception) => {
            const {
                request: { body }
            } = interception;
            const parsedBody = JSON.parse(body);
            expect(parsedBody.nooffset).to.equal('2015-01-01T00:00:00+01:00'); // Based on app timezone - Europe/Berlin
            expect(parsedBody.datetime2).to.equal('2015-01-01T22:30:00.000+05:30');
            expect(parsedBody.datetime3).to.equal('2015-01-01T00:00:00.000+07:00');
            expect(parsedBody.datetime4).to.equal('2015-01-01T17:00:00.000+00:00');
            expect(parsedBody.appDateTime).to.equal('2015-01-01T18:00:00.000+01:00');
            expect(parsedBody.date2).to.equal('2015-01-01');
            expect(parsedBody.date3).to.equal('2015-01-01');
            expect(parsedBody.date4).to.equal('2015-01-01');
            expect(parsedBody.date4).to.equal('2015-01-01');
        });
    });
});
