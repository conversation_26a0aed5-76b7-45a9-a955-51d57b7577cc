/// <reference types="cypress" />

const setup = () => {
    cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
        fixture: 'form/datepicker.form.json'
    }).as('get-component-type');
    cy.intercept('GET', 'http://localhost:5000/get-data', {
        fixture: 'response/datepicker.data.json'
    }).as('get-data');
    cy.intercept('POST', 'http://localhost:5000/save-data', { fixture: 'response/data1.json' }).as(
        'submit-post-call'
    );
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');

    cy.visit('cypress/e2e/form/no-app-timezone.html');
    cy.wait('@get-angelos');
};

const getShadowDom = () => cy.get('zwe-angelos-update-form-v3').shadow();

describe('Datepicker without offset when app timezone is not provided', () => {
    beforeEach(setup);

    it('displays date-picker field without offset', () => {
        getShadowDom()
            .find('#nooffset') // id for textarea field
            .find('.prepend-container')
            .should('not.exist');

        cy.compareSnapshot('no-offset', Cypress.env('COMPARISON_THRESHOLD'));
    });
});

describe('DateTimePicker with static offset when app timezone is not provided', () => {
    beforeEach(setup);

    it('displays correct offset for SYSTEM timezone', () => {
        getShadowDom()
            .find('#datetime2') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });

    it('displays correct offset for PRESERVE timezone', () => {
        getShadowDom()
            .find('#datetime3') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +07:00`);
    });

    it('displays correct offset for UTC timezone', () => {
        getShadowDom()
            .find('#datetime4') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +00:00`);
    });

    it('displays correct offset for APP timezone', () => {
        getShadowDom()
            .find('#appDateTime')
            .as('appDateTimeField') // id for appDateTme field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);

        cy.get('@appDateTimeField').find('input').should('have.value', '2015-01-09 22:30:00'); // Value based on system timezone only, since app-timezone is not provided
    });
});

describe('DatePicker with static offset when app timezone is not provided', () => {
    beforeEach(setup);

    it('displays correct offset for SYSTEM timezone', () => {
        getShadowDom()
            .find('#date2') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });

    it('displays correct offset for PRESERVE timezone', () => {
        getShadowDom()
            .find('#date3') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });

    it('displays correct offset for UTC timezone', () => {
        getShadowDom()
            .find('#date4') // id for textarea field
            .find('.prepend-container')
            .should('have.text', `(UTC) +00:00`);
    });

    it('displays correct offset for APP timezone', () => {
        getShadowDom()
            .find('#appDate') // id for appDateTme field
            .find('.prepend-container')
            .should('have.text', `(UTC) +05:30`);
    });
});

describe('Use DatePicker and submit when app timezone is not provided', () => {
    beforeEach(setup);
    /* eslint-disable cypress/unsafe-to-chain-command */

    function selectDateTime(dateId) {
        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find(dateId)
            .find('.z-date-picker')
            .find('input')
            .trigger('click')
            .as('openDatePicker');

        cy.get('@openDatePicker')
            .closest('.z-config-provider')
            .siblings('.v-binder-follower-container')
            .find('.z-date-panel-calendar')
            .find('.z-date-panel-dates')
            .find('.z-date-panel-date')
            .should('have.length', 42)
            .eq(4)
            .find('.z-date-panel-date__trigger')
            .click({ force: true })
            .as('selectedDate');
        cy.get('@selectedDate')
            .closest('.z-date-panel-calendar')
            .siblings('.z-date-panel-actions')
            .find('button')
            .first()
            .should('contain.text', 'Confirm')
            .click({ waitForAnimations: false });
    }

    function selectDate(dateId) {
        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find(dateId)
            .find('.z-date-picker')
            .find('input')
            .trigger('click')
            .as('openDatePicker');

        cy.get('@openDatePicker')
            .closest('.z-config-provider')
            .siblings('.v-binder-follower-container')
            .find('.z-date-panel-calendar')
            .find('.z-date-panel-dates')
            .find('.z-date-panel-date')
            .should('have.length', 42)
            .eq(4)
            .find('.z-date-panel-date__trigger')
            .click({ force: true });
    }

    it('output correct values', () => {
        selectDateTime('#datetime2');
        selectDateTime('#datetime3');
        selectDateTime('#datetime4');
        selectDateTime('#appDateTime');
        selectDate('#nooffset');
        selectDate('#date2');
        selectDate('#date3');
        selectDate('#date4');
        selectDate('#appDate');

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#angelos-v2-footer')
            .find('button')
            .first()
            .should('contain.text', 'Submit')
            .click();

        cy.wait('@submit-post-call').then((interception) => {
            const {
                request: { body }
            } = interception;
            const parsedBody = JSON.parse(body);
            expect(parsedBody.nooffset).to.equal('2015-01-01T00:00:00+05:30'); // Based on system timezone, since app timezone is not provided
            expect(parsedBody.datetime2).to.equal('2015-01-01T22:30:00.000+05:30');
            expect(parsedBody.datetime3).to.equal('2015-01-01T00:00:00.000+07:00');
            expect(parsedBody.datetime4).to.equal('2015-01-01T17:00:00.000+00:00');
            expect(parsedBody.appDateTime).to.equal('2015-01-01T22:30:00.000+05:30');
            expect(parsedBody.date2).to.equal('2015-01-01');
            expect(parsedBody.date3).to.equal('2015-01-01');
            expect(parsedBody.date4).to.equal('2015-01-01');
            expect(parsedBody.appDate).to.equal('2015-01-01');
        });
    });
});
