<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos Test</title>
        <script>
            __zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'http://localhost:5000',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            localStorage.setItem('@zeta::authToken', '');
            document.addEventListener('DOMContentLoaded', function () {
                const angelosEntityComponent = document.getElementById('angelosEntityComponent');
                angelosEntityComponent.addEventListener('before-submit', async function (event) {
                    console.log('before-submit', event.detail);
                    const beforSubmit = document.getElementById('beforSubmit');
                    beforSubmit.innerText = 'beforSubmit invoked';
                });
                angelosEntityComponent.addEventListener('cancel-success', async function (event) {
                    console.log('cancel-success', event.detail);
                    const cancelSuccess = document.getElementById('cancelSuccess');
                    cancelSuccess.innerText = 'cancelSuccess invoked';
                });

                angelosEntityComponent.addEventListener('reset-success', async function (event) {
                    console.log('reset-success', event.detail);
                    const resetSuccess = document.getElementById('resetSuccess');
                    resetSuccess.innerText = 'resetSuccess invoked';
                });

                angelosEntityComponent.addEventListener('before-submit', async function (event) {
                    console.log('before-submit', event.detail);
                    const beforeSubmit = document.getElementById('beforeSubmit');
                    beforeSubmit.innerText = 'beforeSubmit invoked';
                });

                angelosEntityComponent.addEventListener('submit-success', async function (event) {
                    console.log('submit-success', event.detail);
                    const submitSuccess = document.getElementById('submitSuccess');
                    submitSuccess.innerText = 'submitSuccess invoked';
                });

                angelosEntityComponent.addEventListener('submit-error', async function (event) {
                    console.log('submit-error', event.detail);
                    const submitError = document.getElementById('submitError');
                    submitError.innerText = 'submitError invoked';
                });

                angelosEntityComponent.addEventListener('after-submit', async function (event) {
                    console.log('after-submit', event.detail);
                    const afterSubmit = document.getElementById('afterSubmit');
                    afterSubmit.innerText = 'afterSubmit invoked';
                });

                angelosEntityComponent.addEventListener('prefill-success', async function (event) {
                    console.log('prefill-success', event.detail);
                    const prefillSuccess = document.getElementById('prefillSuccess');
                    prefillSuccess.innerText = 'prefillSuccess invoked';
                });

                angelosEntityComponent.addEventListener('prefill-error', async function (event) {
                    console.log('prefill-error', event.detail);
                    const prefillError = document.getElementById('prefillError');
                    prefillError.innerText = 'prefillError invoked';
                });
            });
        </script>

        <script type="module" src="../../../dist/angelos.min.js"></script>
    </head>
    <body>
        <zwe-angelos-create-form-v3
            id="angelosEntityComponent"
            entity-id="test-entity"
            tenant-id="0"
            context='{
                "list": [
                {
                    "label": "test1",
                    "value": "1"
                },
                {
                    "label": "test2",
                    "value": "2"
                },
                {
                    "label": "test3",
                    "value": "3"
                }
            ]
            }'
            params='{"testurl": "http://localhost:5000"}'
            app-timezone="Europe/Berlin"
        ></zwe-angelos-create-form-v3>
        <span id="beforSubmit">beforSubmit not invoked </span>
        <span id="submitSuccess">submitSuccess not invoked </span>
        <span id="submitError">submitError not invoked </span>
        <span id="afterSubmit">afterSubmit not invoked </span>
        <span id="cancelSuccess">cancelSuccess not invoked </span>
        <span id="resetSuccess">resetSuccess not invoked </span>
        <span id="beforeSubmit">beforeSubmit not invoked </span>
        <span id="prefillSuccess">prefillSuccess not invoked </span>
        <span id="prefillError">prefillError not invoked </span>
    </body>
</html>
