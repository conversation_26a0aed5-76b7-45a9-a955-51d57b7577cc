<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Angelos Test</title>
    <script>
        __zeta__ = {
            angelos: {
                SERVICE_BASE_URL: 'http://localhost:5000',
                OMS_SERVICE_BASE_URL: 'http://localhost:5000',
            },
        };
        localStorage.setItem(
            '@zeta::authToken',
            '',
        );
    </script>

    <script type="module" src="../../../dist/angelos.min.js"></script>

</head>
</head>

<body>
    <button id="externalSubmit">externalSubmit</button>
    <button id="externalCheckValidity">externalCheckValidity</button>
    <button id="externalReportValidity">externalReportValidity</button>
    <button id="externalReset">externalReset</button>
    <zwe-angelos-update-form-v3 entity-id="test-entity" tenant-id="0" context="{}"
        params='{"testurl": "http://localhost:5000"}'></zwe-angelos-update-form-v3>
    <script>
        // document.addEventListener('load', function () {
            window.updateform = document.getElementsByTagName('zwe-angelos-update-form-v3')[0];

            const submitButton = document.getElementById('externalSubmit');
            const checkValidityButton = document.getElementById('externalCheckValidity');
            const reportValidityButton = document.getElementById('externalReportValidity');
            const resetButton = document.getElementById('externalReset');

            submitButton.addEventListener('click', async function () {
                const data = await updateform.submit();
                console.log(data);
            })

            checkValidityButton.addEventListener('click', async function () {
                const data = await updateform.checkValidity();
                console.log(data);
            })

            reportValidityButton.addEventListener('click', async function () {
                const data = await updateform.reportValidity();
                console.log(data);
            })

            resetButton.addEventListener('click', function () {
                updateform.reset();
            })
        // })
    </script>
</body>

</html>
