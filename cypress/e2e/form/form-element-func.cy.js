describe('Update form exposed functions', () => {
    beforeEach(() => {
        cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
            fixture: 'form/submit-form.json'
        }).as('get-component-type');
        cy.intercept('POST', 'http://localhost:5000/save-data', {
            fixture: 'response/data1.json'
        }).as('submit-post-call');
        cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
        cy.visit('cypress/e2e/form/update-form-exposed-functions.html');
        cy.wait('@get-angelos');
        cy.wait('@get-component-type');

        Cypress.on('uncaught:exception', () => {
            // returning false here prevents <PERSON><PERSON> from
            // failing the test
            return false;
        });
    });

    it('should submit the form', () => {
        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit') //id of radio field
            .find('input[type="radio"]')
            .first() // first field value is mango
            .click(); // selects first radio

        cy.get('#externalSubmit').click();

        cy.wait('@submit-post-call').then((interception) => {
            const {
                request: { body }
            } = interception;
            const parsedBody = JSON.parse(body);
            expect(parsedBody.fruit).to.equal('mango');
        });
    });

    it('should check validate and show validation error', () => {
        // Ensures that the form is rendered
        cy.get('zwe-angelos-update-form-v3').shadow().find('#fruit');

        // In case it fails - it can be resolved by increasing the wait time
        cy.get('#externalCheckValidity').click();

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit')
            .find('.z-form-item-feedback--error')
            .should('exist');
    });

    it('should report validate and show validation error', () => {
        // Ensures that the form is rendered
        cy.get('zwe-angelos-update-form-v3').shadow().find('#fruit');

        // In case it fails - it can be resolved by increasing the wait time
        cy.get('#externalReportValidity').click();

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit')
            .find('.z-form-item-feedback--error')
            .should('exist');
    });

    it('should validate and not submit the form', () => {
        // Ensures that the form is rendered
        cy.get('zwe-angelos-update-form-v3').shadow().find('#fruit');

        // In case it fails - it can be resolved by increasing the wait time
        cy.get('#externalSubmit').click();

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit')
            .find('.z-form-item-feedback--error')
            .should('exist');
    });

    it('should reset the form', () => {
        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit') //id of radio field
            .find('input[type="radio"]')
            .first() // first field value is mango
            .click(); // selects first radio

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit') //id of radio field
            .find('input[type="radio"]')
            .first()
            .parent()
            .should('have.class', 'z-radio--checked');

        cy.get('#externalReset').click();

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit') //id of radio field
            .find('input[type="radio"]')
            .first()
            .should('not.have.class', 'z-radio--checked');
    });

    it('should return correct values with silent validation', () => {
        const validationObject = {
            silent: true
        };

        // Ensures that the form is rendered
        cy.get('zwe-angelos-update-form-v3').shadow().find('#fruit');

        cy.get('zwe-angelos-update-form-v3')
            .then(($form) => {
                return $form[0].submit(validationObject);
            })
            .should((obj) => {
                expect(obj).to.have.property('errors');
                expect(obj).to.have.property('formModel');
                expect(obj).to.not.have.property('submitResponse');
            });

        cy.get('zwe-angelos-update-form-v3')
            .then(($form) => {
                return $form[0].checkValidity(validationObject);
            })
            .should('be.false');

        cy.get('zwe-angelos-update-form-v3')
            .then(($form) => {
                return $form[0].reportValidity(validationObject);
            })
            .should((obj) => {
                expect(obj).to.have.property('errors');
                expect(obj).to.have.property('isValid');
            });

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#fruit')
            .find('.z-form-item-feedback--error')
            .should('not.exist');
    });

    it('should render ZRadioGroup with correct values', () => {
        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#radioButtons')
            .find('input[type="radio"]')
            .first()
            .should('have.value', 'enable');

        cy.get('zwe-angelos-update-form-v3')
            .shadow()
            .find('#radioButtons')
            .find('input[type="radio"]')
            .last()
            .should('have.value', 'disable');
    });
});
