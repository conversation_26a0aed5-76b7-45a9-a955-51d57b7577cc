<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos Test</title>
        <script>
            __zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'http://localhost:5000',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            localStorage.setItem('@zeta::authToken', '');
        </script>

        <script type="module" src="../../../dist/angelos.min.js"></script>
    </head>
    <body>
        <zwe-angelos-create-form-v3
            entity-id="array-element-from-select"
            tenant-id="0"
            context='{
            "metadata": {
              "id": "aether.feature.feature-flag-configs",
              "name": "featureFlagConfigs",
              "description": "Sample Feature",
              "requester": {
                "module": "aether",
                "moduleVersion": "0.0.1"
              },
              "tenantId": "600309",
              "tenantCode": "lsg"
            },
            "state": "ENABLED",
            "viewConfig": {
              "displayName": "feature-flags"
            }
          }'
        ></zwe-angelos-create-form-v3>
    </body>
</html>
