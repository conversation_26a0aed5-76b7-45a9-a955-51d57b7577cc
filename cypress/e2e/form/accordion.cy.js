/// <reference types="cypress" />

const setup = () => {
    cy.intercept(
        'GET',
        '/api/v1/tenants/0/business-entities/angelos-testing-entity/component-type/*',
        {
            fixture: 'form/accordion.json'
        }
    ).as('get-component-type');
    cy.intercept('GET', 'http://localhost:4000/get-data', {
        fixture: 'response/data1.json'
    }).as('get-data');
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
    cy.visit('cypress/e2e/form/accordion.html');
    cy.wait('@get-angelos');
    cy.on('uncaught:exception', () => {
        return false;
    });
};

const getShadowDom = () => cy.get('zwe-angelos-create-form-v3').shadow();

describe('Form with accordions', () => {
    beforeEach(setup);

    it('check if accordion title present in the form', () => {
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__header-main')
            .first()
            .should('have.text', 'Accordion Title');
    });

    it('whether the input box is visible after opening accordion', () => {
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .first('.z-accordion-item__header-main')
            .click({ waitForAnimations: true })
            .find('.z-accordion-item')
            .first('.z-accordion-item__header-main')
            .click({ waitForAnimations: true })
            .find('.z-input')
            .should('be.visible');
    });

    it('check whether the nested accordions open if the validations failed', () => {
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__header-main')
            .eq(2)
            .click({ waitForAnimations: true })
            .parent()
            .siblings('.z-accordion-item__content-wrapper')
            .find('.z-accordion-item')
            .first('.z-accordion-item__header-main')
            .click({ waitForAnimations: true });

        // close the accordions without filling mandatory form field
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__header-main')
            .eq(2)
            .click({ waitForAnimations: true });

        // click on submit button
        getShadowDom()
            .find('.z-button')
            .find('.z-button__content')
            .contains('span', 'Submit')
            .click();

        // accordion should be open
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__header-main')
            .eq(1)
            .parent()
            .should('have.class', 'z-accordion-item__header--active');

        // check error message is there or not
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__content-wrapper')
            .find('.z-form-item-feedback__line')
            .first()
            .should('have.text', 'Reason Code is required');
    });
});
