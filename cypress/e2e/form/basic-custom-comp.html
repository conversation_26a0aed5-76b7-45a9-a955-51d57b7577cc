<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos Test</title>
        <script>
            __zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'http://localhost:5000',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
            localStorage.setItem('@zeta::authToken', '');
        </script>

        <script type="module" src="../../../dist/angelos.min.js"></script>
    </head>
    <body>
        <zwe-angelos-create-form-v3
            entity-id="test-entity"
            tenant-id="0"
            context='{
                "metadata":{
                   "tenantId": "778787878" 
                },
                "list": [
                {
                    "label": "test1",
                    "value": "1"
                },
                {
                    "label": "test2",
                    "value": "2"
                },
                {
                    "label": "test3",
                    "value": "3"
                }
            ]
            }'
            params='{"testurl": "http://localhost:5000"}'
            app-timezone="Europe/Berlin"
        ></zwe-angelos-create-form-v3>
    </body>
</html>
