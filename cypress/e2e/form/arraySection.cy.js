/// <reference types="cypress" />

const setup = () => {
    cy.intercept(
        'GET',
        '/api/v1/tenants/0/business-entities/array-element-from-select/component-type/*',
        {
            fixture: 'form/arraySection.json'
        }
    ).as('get-component-type');
    cy.intercept('GET', 'http://localhost:4000/get-data', {
        fixture: 'response/data1.json'
    }).as('get-data');
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
    cy.visit('cypress/e2e/form/arraySection.html');
    cy.wait('@get-angelos');
    cy.on('uncaught:exception', () => {
        return false;
    });
};

const getShadowDom = () => cy.get('zwe-angelos-create-form-v3').shadow();

describe('Check accordion opening in array section when validation error in form', () => {
    beforeEach(setup);

    it('check if accordion title present in the form', () => {
        getShadowDom()
            .find('.angelos-form-container')
            .find('.section-header__right')
            .eq(2)
            .find('button')
            .click()
            .click();

        // click on submit button
        getShadowDom()
            .find('.z-button')
            .find('.z-button__content')
            .contains('span', 'Submit')
            .click();

        // accordion should be open
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__header-main')
            .eq(1)
            .parent()
            .should('have.class', 'z-accordion-item__header--active');

        // check error message is there or not
        getShadowDom()
            .find('.angelos-form-container')
            .find('.z-accordion-item')
            .find('.z-accordion-item__content-wrapper')
            .find('.z-form-item-feedback__line')
            .first()
            .should('have.text', 'ledgerDetails.0.glName is required');
    });
});
