/// <reference types="cypress" />

const submitForm = () => {
    cy.get('zwe-angelos-create-form-v3')
        .shadow()
        .find('#angelos-v2-footer')
        .find('button')
        .first()
        .click();
};

describe('Conditional field in a form', () => {
    beforeEach(() => {
        cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
            fixture: 'form/conditional-field.json'
        }).as('get-component-type');
        cy.intercept('POST', 'http://localhost:5000/save-data', {
            fixture: 'response/data1.json'
        }).as('submit-post-call');
        cy.intercept('GET', '*/angelos.min.js').as('get-angelos');

        cy.visit('cypress/e2e/form/index.html');
        cy.wait('@get-angelos');
    });

    it('Should not display field when condition is not fulfilled', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('.angelos-wrapper')
            .should('have.length', 1)
            .find('#angelos-form-section-container')
            .find('input[type="text"]')
            .should('have.length', 0); // Initially condition is not being fulfilled, so the field should not be displayed (check view config for condition)

        cy.compareSnapshot('hidden-field', Cypress.env('COMPARISON_THRESHOLD'));

        submitForm();

        cy.wait('@submit-post-call').then((interception) => {
            const {
                request: { body }
            } = interception;
            const parsedBody = JSON.parse(body);
            expect(parsedBody).to.not.have.property('name'); // name is the id of the field which is conditionally hidden
        });
    });

    it('Should display field when condition is fulfilled', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#angelos-form-section-container')
            .as('form-section-container')
            .find('#fruits') // id of the field
            .find('input[type="radio"]')
            .last()
            .click(); // Selects the 2nd radio button to fulfill condition

        cy.get('@form-section-container')
            .find('input[type="text"]')
            .as('conditional-field')
            .should('have.length', 1); // Now the field should be displayed

        cy.compareSnapshot('visible-field', Cypress.env('COMPARISON_THRESHOLD'));

        cy.get('@conditional-field').first().type('hello world', { force: true }); // types into the text field

        submitForm();

        cy.wait('@submit-post-call').then((interception) => {
            const {
                request: { body }
            } = interception;
            const parsedBody = JSON.parse(body);
            expect(parsedBody.name).to.equal('hello world');
        });
    });

    it('Should update baseline images', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('#angelos-form-section-container')
            .as('form-section-container')
            .find('#fruits')
            .find('input[type="radio"]')
            .last()
            .click();

        cy.get('@form-section-container')
            .find('input[type="text"]')
            .as('conditional-field')
            .should('have.length', 1);

        cy.compareSnapshot('visible-field', 0.0, { updateSnapshots: true });
    });
});
