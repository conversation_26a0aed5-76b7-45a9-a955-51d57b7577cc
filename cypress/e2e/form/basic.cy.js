/// <reference types="cypress" />

describe('Form with all field types', () => {
    beforeEach(() => {
        cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
            fixture: 'form/basic.json'
        }).as('get-component-type');
        cy.intercept('GET', 'http://localhost:5000/get-data', { fixture: 'response/data1.json' });
        cy.intercept('GET', '*/angelos.min.js').as('get-angelos');

        cy.visit('cypress/e2e/form/index.html');
        cy.wait('@get-angelos');
    });

    it('displays form', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('.angelos-wrapper')
            .should('have.length', 1)
            .find('form')
            .should('have.length', 1)
            .find('input')
            .should('have.length', 2)
            .closest('form')
            .find('button')
            .should('have.length', 3);

        cy.compareSnapshot('form-basic');
    });

    it('raises - cancel-success', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('.angelos-wrapper')
            .find('form')
            .find('button')
            .eq(1)
            .click();
        cy.get('span[id=cancelSuccess]').should('have.text', 'cancelSuccess invoked');
    });

    it('raises - reset-success', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('.angelos-wrapper')
            .find('form')
            .find('button')
            .eq(2)
            .click();
        cy.get('span[id=resetSuccess]').should('have.text', 'resetSuccess invoked');
    });

    it('raises - before-submit and submit flow events', () => {
        cy.get('zwe-angelos-create-form-v3')
            .shadow()
            .find('.angelos-wrapper')
            .find('form')
            .find('button')
            .eq(0)
            .click();
        cy.get('span[id=beforeSubmit]').should('have.text', 'beforeSubmit invoked');
        cy.get('span[id=submitSuccess]').should('have.text', 'submitSuccess invoked');
        cy.get('span[id=afterSubmit]').should('have.text', 'afterSubmit invoked');
    });

    it('raises - prefill events', () => {
        cy.get('zwe-angelos-create-form-v3').trigger('prefill');
        cy.get('span[id=prefillSuccess]').should('have.text', 'prefillSuccess invoked');
        // For error case you'd need to set up an error condition first
        // cy.get('span[id=prefillError]').should('have.text', 'prefillError invoked');
    });
});
