import componentConfig from '../../fixtures/table';

const setup = () => {
    cy.intercept('GET', '/api/v1/tenants/0/business-entities/test-entity/component-type/*', {
        fixture: 'table/index.json'
    }).as('get-component-type');

    cy.intercept(
        'GET',
        /https:\/\/0-0-hercules\.mum1-pp\.zetaapps\.in\/atalanta\/\/orchestra\/api\/v1\/tenants\/\d+\/coas\/\d+\/workers-list.*/,
        {
            fixture: 'table/worker-list.json'
        }
    ).as('workerList');

    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
    cy.visit('cypress/e2e/table/index.html');
    cy.wait('@get-angelos');
    cy.on('uncaught:exception', () => {
        return false;
    });
};

const getShadowDom = () => cy.get('zwe-angelos-data-table-v3').shadow();

describe('Table initial render', () => {
    beforeEach(setup);

    it('should display the data table', () => {
        cy.get('zwe-angelos-data-table-v3').should('be.visible');
    });
    it('should render the title and description of the table', () => {
        getShadowDom().find('.header__title').should('have.text', 'All Workers');

        getShadowDom().find('.header__description').should('have.text', 'This is the worker list');
    });

    it('should render the grid action Button', () => {
        getShadowDom()
            .find('.actions')
            .find('button')
            .find('.z-button__content')
            .should('be.visible')
            .contains('Actions');
    });

    it('should render the grid options when action is clicked', () => {
        // Click the button with the class 'action'
        getShadowDom().find('.actions').click({ waitForAnimations: true });

        getShadowDom().find('.z-base-select-menu').should('be.visible');

        getShadowDom()
            .find('.z-base-select-option--disabled')
            .should('be.visible')
            .contains('Test1');
    });

    it('should render the table component', () => {
        // table component is present

        getShadowDom().find('.z-data-table-table').should('be.visible');
    });
    it('should render the correct number of rows and columns', () => {
        const tableConfig = componentConfig.viewConfig.tableConfig;
        const paginationConfig = componentConfig.viewConfig.paginationConfig;
        // number of columns  in config
        const numberOfColumns = tableConfig.columns?.length;
        // check number of columns
        getShadowDom()
            .find('.z-data-table-tr')
            .first()
            .find('.z-data-table-th')
            .should('have.length', numberOfColumns);

        /** check number of rows
         * for this we will check tbody > tr
         * To check number of rows, check the pagination default size. If pagination is not there in config, then assume row to be 5 (angelos table default config )
         */
        const pageSize = paginationConfig?.pageSize || 5;
        getShadowDom().find('tbody').find('.z-data-table-tr').should('have.length', pageSize);
    });

    it('should pin the columns which has pinned property', () => {
        // get all the columns having pinned attribute, check if the correct class(left or right ) are set for those columns based on the pinned value
        const columns = componentConfig.viewConfig.tableConfig.columns;
        const columnsWithPinnedProperty = columns.filter((column) => column.pinned);
        columnsWithPinnedProperty.forEach((column) => {
            getShadowDom()
                .find('tbody')
                .find('.z-data-table-tr')
                .first()
                .find(`[data-col-key="${column.field}"]`)
                .should('have.class', `z-data-table-td--fixed-${column.pinned}`);
        });
    });

    it('it should align the header and column data based on the property ', () => {
        // get all the columns and headers having align attribute, check if the correct class(left ,center or right ) are set for those columns based on the pinned value
        const columns = componentConfig.viewConfig.tableConfig.columns;
        const columnsWithAlignProperty = columns.filter((column) => column.align);
        columnsWithAlignProperty.forEach((column) => {
            getShadowDom()
                .find('tbody')
                .find('.z-data-table-tr')
                .first()
                .find(`[data-col-key="${column.field}"]`)
                .should('have.class', `z-data-table-td--${column.align}-align`);
        });
        // check for all the header and their alignment
        const flexMap = {
            left: 'justify-content: flex-start;',
            right: 'justify-content: flex-end;',
            center: 'justify-content: center;'
        };
        columns.forEach((column) => {
            getShadowDom()
                .find('thead')
                .find('.z-data-table-tr')
                .find(`[data-col-key="${column.field}"]`)
                .find('.z-data-table-th__title-wrapper')
                .should('have.attr', 'style', flexMap[column.titleAlign ?? 'left']);
        });
    });
});

describe('Frontend Pagination', () => {
    beforeEach(setup);
    it('should render the pagination component', () => {
        getShadowDom().find('.z-data-table__pagination').should('be.visible');
    });

    it('should render the same number of rows as per pageSize mentioned', () => {
        // check number of row is equal to the page size mentioned in the config
        const paginationConfig = componentConfig.viewConfig.paginationConfig;
        const pageSize = paginationConfig.pageSize;
        getShadowDom().find('tbody').find('.z-data-table-tr').should('have.length', pageSize);
    });
    it('should show the page size picker based on the config', () => {
        // check if show picker is present when config has true
        const paginationConfig = componentConfig.viewConfig.paginationConfig;
        const pageSize = paginationConfig.pageSize;
        if (paginationConfig.showSizePicker) {
            getShadowDom()
                .find('.z-pagination-size-picker__button-content')
                .should('be.visible')
                .find('span')
                .should('be.visible')
                .contains(pageSize);
        } else {
            getShadowDom()
                .find('.z-pagination-size-picker__button-content')
                .should('not.be.visible');
        }
    });
    it('should load next set of data when next arrow is clicked', () => {
        /**
         * We will check if index 0 of a column data matches the index (pageSize) data
         */

        let dataForFirstPage = '';
        getShadowDom()
            .find('.z-data-table-tbody')
            .find('.z-data-table-tr')
            .find('[data-col-key="workerName"]')
            .first()
            .then(($element) => {
                dataForFirstPage = $element.text();
            });
        /**
         * Monitor change in data after the button is clicked
         */
        getShadowDom()
            .find('.z-pagination-right-section')
            .find('.z-pagination-item--button')
            .eq(2)
            .click({ waitForAnimations: true });
        getShadowDom()
            .find('.z-data-table-tbody')
            .find('.z-data-table-tr')
            .find('[data-col-key="workerName"]')
            .first()
            .should('not.equal', dataForFirstPage);
    });

    it('the previous button for first page should be disabled and enabled for other pages', () => {
        let pageIndex = '';
        getShadowDom()
            .find('.z-pagination-quick-jumper')
            .find('.z-base-selection-label__render-label')
            .then(($element) => {
                $element.text();
                pageIndex = $element.text();
            });
        if (pageIndex === 1) {
            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .first()
                .should('have.class', 'z-pagination-item--disabled');

            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .eq(2)
                .should('have.class', 'z-pagination-item--disabled');
        }
        // click

        getShadowDom()
            .find('.z-pagination-right-section')
            .find('.z-pagination-item--button')
            .eq(2)
            .click({ waitForAnimations: true });

        // check the previous buttons for the page again
        if (pageIndex === 2) {
            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .first()
                .should('not.have.class', 'z-pagination-item--disabled');

            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .eq(2)
                .should('not.have.class', 'z-pagination-item--disabled');
        }
    });

    it('the last button for last page should be disabled', () => {
        let pageIndex = '';
        getShadowDom()
            .find('.z-pagination-quick-jumper')
            .find('.z-base-selection-label__render-label')
            .then(($element) => {
                $element.text();
                pageIndex = $element.text();
            });

        if (pageIndex === 1) {
            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .eq(3)
                .should('not.have.class', 'z-pagination-item--disabled');

            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .eq(4)
                .should('not.have.class', 'z-pagination-item--disabled');
        }
        // click

        getShadowDom()
            .find('.z-pagination-right-section')
            .find('.z-pagination-item--button')
            .eq(3)
            .click({ waitForAnimations: true });

        if (pageIndex === 1) {
            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .eq(3)
                .should('have.class', 'z-pagination-item--disabled');

            getShadowDom()
                .find('.z-pagination-right-section')
                .find('.z-button')
                .eq(4)
                .should('have.class', 'z-pagination-item--disabled');
        }
    });
});

describe('Sorting', () => {
    beforeEach(setup);
    it('Should have the sorting icon for which sort is set to true', () => {
        // check for all the columsn having sorting set as true to have the icon
        const columns = componentConfig.viewConfig.tableConfig.columns;
        const columnsWithSorting = columns.filter((column) => column.sortable);
        columnsWithSorting.forEach((column) => {
            getShadowDom()
                .find('thead')
                .find('.z-data-table-tr')
                .find(`[data-col-key="${column.field}"]`)
                .should('have.class', `z-data-table-th--sortable`) // check if this class is present in the header
                .find('.z-data-table-sorter') // check if the sort icon is visible
                .should('be.visible');
        });
    });
    it('Should change the order of data when header is clicked for sort enabled row', () => {
        // Collect the last data from api response
        let lastData;

        cy.wait('@workerList')
            .its('response')
            .then((response) => {
                // Access full response
                const data = response.body;
                const sortedData = data.sort((a, b) => {
                    return a.workerName.localeCompare(b.workerName);
                });

                lastData = sortedData[data.length - 1];
                //Click header
                getShadowDom()
                    .find('thead')
                    .find('[data-col-key="workerName"]')
                    .click({ waitForAnimations: true });

                // Verify last data is now on first
                getShadowDom()
                    .find('tbody')
                    .find('.z-data-table-tr')
                    .first()
                    .find('[data-col-key="workerName"]')
                    .should('have.text', lastData.workerName);
            });
    });
    it('Shouldnot change the order of data when header is clicked for row that doesnot have sort enabled', () => {
        // collect the data of a column and see that the data remains same after header click( Chose date column for uniqueness)

        let startTime = '';
        // Verify last data is now on first
        getShadowDom()
            .find('tbody')
            .find('.z-data-table-tr')
            .first()
            .find('[data-col-key="startTime"]')
            .then(($element) => {
                startTime = $element.text();
                // Click header
                getShadowDom()
                    .find('thead')
                    .find('[data-col-key="startTime"]')
                    .click({ waitForAnimations: true });

                getShadowDom()
                    .find('tbody')
                    .find('.z-data-table-tr')
                    .first()
                    .find('[data-col-key="startTime"]')
                    .contains(startTime);
            });
    });
});
describe('Filter', () => {
    beforeEach(setup);
    it('Should render all the filter chips if filterconfig is present', () => {
        const filterConfig = componentConfig.viewConfig.filterConfig;
        const filterConfigLength = filterConfig ? filterConfig.length : 0;
        if (filterConfigLength) {
            getShadowDom().find('.z-filter-item').should('have.length', filterConfigLength);
        }
    });
    it('Single select - Should filter data when values are selected for a chip', () => {
        // click first filter item

        getShadowDom().find('.z-filter-item').first().click({ waitForAnimations: true });

        let dataAtTheFilter = '';
        // click a value
        getShadowDom()
            .find('.z-popover__content')
            .find('.z-list-item')
            .eq(1) // as 0 will be the select all button so we choose 1
            .then(($element) => {
                dataAtTheFilter = $element.text().trim();
            })
            .click({ waitForAnimations: true });

        // Apply
        getShadowDom().find('.z-popover__footer').find('.z-button--primary-type').click();

        // Now check all the data in this column that should be equal to dataAtTheFilter

        getShadowDom()
            .find('tbody')
            .find('.z-data-table-tr')
            .each(($row) => {
                cy.wrap($row).find('[data-col-key="phase"]').contains(dataAtTheFilter);
            });
    });
    it('Multiple select - Should filter data when values are selected for a chip', () => {
        // click first filter item

        getShadowDom().find('.z-filter-item').first().click({ waitForAnimations: true });

        let dataAtTheFilter = [];
        // click the first value
        getShadowDom()
            .find('.z-popover__content')
            .find('.z-list-item')
            .eq(1) // as 0 will be the select all button so we choose 1
            .then(($element) => {
                dataAtTheFilter.push($element.text().trim());
            })
            .click({ waitForAnimations: true });

        // click the second value
        getShadowDom()
            .find('.z-popover__content')
            .find('.z-list-item')
            .eq(2) //
            .then(($element) => {
                dataAtTheFilter.push($element.text().trim());
            })
            .click({ waitForAnimations: true });

        // Apply
        getShadowDom().find('.z-popover__footer').find('.z-button--primary-type').click();

        // Now check all the data in this column that should be equal to dataAtTheFilter

        getShadowDom()
            .find('tbody')
            .find('.z-data-table-tr')
            .each(($row) => {
                cy.wrap($row)
                    .find('[data-col-key="phase"]')
                    .then(($element) => {
                        expect($element.text().trim()).to.be.oneOf(dataAtTheFilter);
                    });
            });
    });
    it('Should show the reset button only when filter values are added', () => {
        // Check that the reset button doesnot exist
        getShadowDom().find('.z-action-reset').should('not.exist');
        // Apply a filter then see if the rest button shows

        getShadowDom().find('.z-filter-item').first().click({ waitForAnimations: true });
        getShadowDom()
            .find('.z-popover__content')
            .find('.z-list-item')
            .eq(1) // as 0 will be the select all button so we choose 1
            .click({ waitForAnimations: true });

        getShadowDom()
            .find('.z-popover__footer')
            .find('.z-button--primary-type')
            .click({ waitForAnimations: true });
        // check the rest button exist
        getShadowDom().find('.z-action-reset').should('be.visible');
    });
    it('Should reset the filter state when reset button is clicked', () => {
        // the label in filter would have the data selected, checking if it is not there tells that the reset has worked

        getShadowDom().find('.z-filter-item').first().click({ waitForAnimations: true });

        // click a value
        getShadowDom()
            .find('.z-popover__content')
            .find('.z-list-item')
            .eq(1) // as 0 will be the select all button so we choose 1
            .click({ waitForAnimations: true });

        // Apply
        getShadowDom().find('.z-popover__footer').find('.z-button--primary-type').click();

        // Check the label value is present or not

        getShadowDom().find('.z-filter-item').first().find('.z-filter__tag-value').should('exist');

        // Click reset
        getShadowDom().find('.z-action-reset').click({ waitForAnimations: true });

        getShadowDom()
            .find('.z-filter-item')
            .first()
            .find('.z-filter__tag-value')
            .should('not.exist'); // as the rest has taken place, this should not be prese
    });

    it('Should reset the table data when reset button is clicked', () => {
        // Check the number of rows before filter
        let numberOfRows = 0;
        getShadowDom()
            .find('tbody')
            .find('.z-data-table-tr')
            .its('length')
            .then((length) => {
                numberOfRows = length;
                restOfTheAction();
            });

        function restOfTheAction() {
            // setFilter
            getShadowDom().find('.z-filter-item').first().click({ waitForAnimations: true });

            // click a value
            getShadowDom()
                .find('.z-popover__content')
                .find('.z-list-item')
                .eq(1) // as 0 will be the select all button so we choose 1
                .click({ waitForAnimations: true });

            // Apply
            getShadowDom().find('.z-popover__footer').find('.z-button--primary-type').click();

            // Check the label value is present or not

            getShadowDom()
                .find('.z-filter-item')
                .first()
                .find('.z-filter__tag-value')
                .should('exist');

            // Click reset
            getShadowDom().find('.z-action-reset').click({ waitForAnimations: true });

            // Check the number of rows afer reset
            getShadowDom()
                .find('tbody')
                .find('.z-data-table-tr')
                .should('have.length', numberOfRows);
        }
    });
});

describe('Custom Template Rendering', () => {
    beforeEach(setup);
    it('Should enable custom template in column when render function is provided', () => {
        // For the worker column check if an anchor tag is present(as it was provided in  render function)
        getShadowDom()
            .find('tbody')
            .find('.z-data-table-tr')
            .first()
            .find(`[data-col-key="workerName"]`)
            .find('a')
            .should('exist');

        // Also, periodicity should not have any children (as it doesnot have a render function)

        getShadowDom()
            .find('tbody')
            .find('.z-data-table-tr')
            .first()
            .find(`[data-col-key="phase"]`)
            .children()
            .should('not.exist');
    });
});

describe('Error cases', () => {
    beforeEach(setup);
    it('It should show unauthorized error when api returns 401', () => {
        cy.intercept(
            'GET',
            /https:\/\/0-0-hercules\.mum1-pp\.zetaapps\.in\/atalanta\/\/orchestra\/api\/v1\/tenants\/\d+\/coas\/\d+\/workers-list.*/,
            {
                statusCode: 401,
                body: {
                    error: 'Unauthorized'
                }
            }
        ).as('workerList');
        cy.wait('@workerList').its('response.statusCode').should('eq', 401);
        getShadowDom()
            .find('.z-data-table-error')
            .find('.z-result__title')
            .should('be.visible')
            .contains('Unauthorized');
    });
    it('It should show Internal server error when api returns 500', () => {
        cy.intercept(
            'GET',
            /https:\/\/0-0-hercules\.mum1-pp\.zetaapps\.in\/atalanta\/\/orchestra\/api\/v1\/tenants\/\d+\/coas\/\d+\/workers-list.*/,
            {
                statusCode: 500,
                body: {
                    error: 'Error'
                }
            }
        ).as('workerList');

        cy.wait('@workerList').its('response.statusCode').should('eq', 500);
        getShadowDom()
            .find('.z-data-table-error')
            .find('.z-result__title')
            .should('be.visible')
            .contains('Internal Server Error');
    });
});
