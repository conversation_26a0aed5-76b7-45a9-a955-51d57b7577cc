/// <reference types="cypress" />

const setup = () => {
    cy.intercept(
        'GET',
        '/api/v1/tenants/0/business-entities/angelos-testing-entity/component-type/*',
        {
            fixture: 'details/detailsCustomComp.json'
        }
    ).as('get-component-type');
    cy.intercept('GET', 'http://localhost:4000/get-data', {
        fixture: 'response/data1.json'
    }).as('get-data');
    cy.intercept(
        'GET',
        'http://localhost:4000/atalanta/api/v1/supportcenter/tenants/600309/transactionErrorCodes',
        {
            fixture: 'response/transactionErrorCodes.data.json'
        }
    ).as('get-transactionErrorCodes-data');
    cy.intercept('POST', 'http://localhost:4000/v2/tenants/600309/payments/list', {
        fixture: 'response/payments.data.json'
    }).as('get-payments-data');
    cy.intercept(
        'POST',
        'http://localhost:4000/atalanta/api/v1/supportcenter/tenants/0/auditLogs',
        {
            fixture: 'response/auditLogs.data.json'
        }
    ).as('get-auditLogs-data');
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
    cy.visit('cypress/e2e/details/details-custom-comp.html');
    cy.wait('@get-angelos');
    cy.on('uncaught:exception', () => {
        return false;
    });
};

const getShadowDom = () => cy.get('zwe-angelos-details-view-v3').shadow();
const getBusinessCompDom = () => cy.get('@zwePaymentDetails');

describe('Test Details view with business component', () => {
    beforeEach(setup);

    it('check if Details view rendered', () => {
        getShadowDom()
            .find('.details-view-wrapper')
            .find('.view-container')
            .should('have.length', 1);
    });
    it('displays view with business component', () => {
        getShadowDom()
            .find('.details-view-wrapper')
            .find('.view-container')
            .find('zwe-payment-details')
            .should('have.length', 1);
    });
    it('renders zwe-payment-details component with simplified-view', () => {
        getShadowDom().find('zwe-payment-details').shadow().as('zwePaymentDetails');
        getBusinessCompDom().find('.segment-view-btns').find('.z-button').should('have.length', 3);
        getBusinessCompDom()
            .find('.payment-detail__title')
            .eq(0)
            .should('have.text', 'Transaction Summary ');
        getBusinessCompDom().find('.timeline-steps').should('have.length', 1);
    });

    it('renders zwe-payment-details component with  ISO-Message', () => {
        getShadowDom().find('zwe-payment-details').shadow().as('zwePaymentDetails');
        getBusinessCompDom()
            .find('.segment-view-btns')
            .find('.z-button')
            .should('have.length', 3)
            .eq(1)
            .should('have.text', ' ISO Message')
            .click({ waitForAnimations: true });
        getBusinessCompDom()
            .find('.details-card-title')
            .should('have.text', 'Authorisation ISO Message');
    });

    it('renders zwe-payment-details component with  JSON-view', () => {
        getShadowDom().find('zwe-payment-details').shadow().as('zwePaymentDetails');
        getBusinessCompDom()
            .find('.segment-view-btns')
            .find('.z-button')
            .should('have.length', 3)
            .eq(2)
            .should('have.text', ' JSON view')
            .click({ waitForAnimations: true });
        getBusinessCompDom().find('.z-json-viewer__wrapper').should('have.length', 1);
    });
});
