<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos Test</title>
        <script>
            __zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'http://localhost:5000',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            localStorage.setItem('@zeta::authToken', '');
        </script>

        <script type="module" src="../../../dist/angelos.min.js"></script>
    </head>
    <body>
        <zwe-angelos-details-view-v3
            entity-id="angelos-testing-entity"
            tenant-id="0"
            context='{
                "pseudoLedgerOneCode": [
                  "RetailPrincipalBilled",
                  "RetailPrincipalUnbilled",
                  "RetailInterestBilled",
                  "RetailInterestUnbilled",
                  "AnnualFeeBilled",
                  "AnnualFeeUnbilled",
                  "LateFeeUnbilled",
                  "LateFeeBilled",
                  "OverlimitFeeBilled",
                  "OverlimitFeeUnbilled",
                  "InsufficientFundFeeBilled",
                  "InsufficientFundFeeUnbilled",
                  "RetailServiceFeeBilled",
                  "RetailServiceFeeUnbilled",
                  "CollectionFeeBilled",
                  "CollectionFeeUnbilled",
                  "RetailMiscFeeBilled",
                  "RetailMiscFeeUnbilled",
                  "gstBilled",
                  "gstUnbilled",
                  "CashPrincipalBilled",
                  "CashPrincipalUnbilled",
                  "CashInterestUnbilled",
                  "CashInterestBilled",
                  "CashAdvanceFeeBilled",
                  "CashAdvanceFeeUnbilled",
                  "EMIPrincipalBilled",
                  "EMIPrincipalUnbilled",
                  "EMIInterestBilled",
                  "EMIInterestUnbilled",
                  "ExcessPayment",
                  "PendingAllocation",
                  "ExcessPaymentUnallocatable",
                  "CreditForExternalParty",
                  "AccountSetupFeeUnbilled",
                  "AccountSetupFeeBilled",
                  "AddOnUserAnnualFeeUnbilled",
                  "AddOnUserAnnualFeeBilled"
                ],
                "pseudoLedgerTwoCode": [
                  "RetailUnbilled",
                  "RetailBilled",
                  "CashUnbilled",
                  "CashBilled",
                  "EMIUnbilled",
                  "EMIBilled",
                  "InterestFreeUnbilled",
                  "InterestFreeBilled"
                ],
                "holdVoucherCodeList": [
                  "CARD_PURCHASE_PLACE_HOLD",
                  "CARD_CASH_PLACE_HOLD",
                  "CARD_PURCHASE_UPDATE_HOLD",
                  "CARD_CASH_UPDATE_HOLD",
                  "CARD_RELEASE_HOLD",
                  "PLACE_HOLD",
                  "RELEASE_HOLD"
                ],
                "viewType": "ALLOCATION",
                "showReversalMetaAsDebit": true,
                "isCouponLinkable": true,
                "timeZone": "America/Los_Angeles"
              }
              '
            params='{
                "baseUrl": "http://localhost:4000",
                "couponBaseUrl": "http://localhost:4000",
                "tenantId": "600309",
                "accountId": "7ba88512-4f31-4740-8f87-f6eca98d2b77",
                "coaId": "1441665598567441191",
                "ledgerId": "5014347300311961992",
                "postingId": 102143
              }'
            app-timezone="Europe/Berlin"
        ></zwe-angelos-details-view-v3>
    </body>
</html>
