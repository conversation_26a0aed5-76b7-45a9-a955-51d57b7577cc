/// <reference types="cypress" />

const setup = () => {
    cy.intercept(
        'GET',
        '/api/v1/tenants/0/business-entities/angelos-testing-entity/component-type/*',
        {
            fixture: 'details/simpleDetailsView.json'
        }
    ).as('get-component-type');
    cy.intercept(
        'GET',
        'http://localhost:4000/ledger-manager/tenants/600309/coas/1441665598567441191/ledgers/5014347300311961992/statements?clockType=SYSTEM&cycleID=-1&periodStartSequenceNumber=1&periodEndSequenceNumber=1&includePostingRequestPayload=true',
        {
            fixture: 'response/postings.data.json'
        }
    ).as('get-postings-data');
    cy.intercept(
        'GET',
        'http://localhost:4000/ruby/tenants/600309/accounts/7ba88512-4f31-4740-8f87-f6eca98d2b77/coupons?pageSize=1000&pageNo=1',
        {
            fixture: 'response/coupons.data.json'
        }
    ).as('get-coupons-data');
    cy.intercept('GET', '*/angelos.min.js').as('get-angelos');
    cy.visit('cypress/e2e/details/index.html');
    cy.wait('@get-angelos');
    cy.on('uncaught:exception', () => {
        return false;
    });
};

const getShadowDom = () => cy.get('zwe-angelos-details-view-v3').shadow();

describe('Test Details view', () => {
    beforeEach(setup);

    it('check if Allocation Meta Posting Summary title present in the view', () => {
        getShadowDom()
            .find('.details-view-wrapper')
            .find('.view.posting-summary-view')
            .find('h3')
            .first()
            .should('have.text', 'Allocation Meta Posting Summary');

        // close the accordions without filling mandatory form field
        getShadowDom()
            .find('.details-view-wrapper')
            .find('.view.posting-original-credit-view')
            .find('.z-accordion-item__header-main')
            .first()
            .should('have.text', 'Original Credit Posting')
            .click({ waitForAnimations: true });
    });
});
