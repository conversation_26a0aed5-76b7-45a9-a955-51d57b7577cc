<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Angelos Test</title>
        <script>
            __zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'http://localhost:5000',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
            localStorage.setItem('@zeta::authToken', '');
        </script>

        <script type="module" src="../../../dist/angelos.min.js"></script>
    </head>
    <body>
        <zwe-angelos-details-view-v3
            entity-id="angelos-testing-entity"
            tenant-id="0"
            context='{
                "compConfig":"{\"fieldConfig\":{\"actions\":{\"show\":false},\"afterBalance\":{\"show\":false},\"beforeBalance\":{\"show\":false},\"formFactorTags\":{\"show\":false}},\"featureFlags\":{\"showJSONView\":true,\"showScFilter\":true,\"sortByStatus\":true,\"showAdviceType\":true,\"passDateInEpoch\":true,\"usePaymentV2Api\":true,\"showIssuerTimezone\":true,\"showUserErrorDetails\":true,\"showACSAuthentication\":true,\"showTransactionsListV3\":true,\"showNewCreditMappingData\":true,\"showErrorReasonDetailView\":true,\"showFormFactorsByListCardFilters\":true,\"showDeclinedTransactionReasonCode\":true,\"enableUpiViewForChannelCodes\":[],\"showTransactionTimeline\":true,\"showQuickStatusHeader\":true},\"creditTimeLine\":true,\"formFactorConfig\":[{\"key\":\"card\",\"label\":\"cards\",\"types\":[\"card\",\"walletCard\",\"proxyCard\",\"mdesToken\"],\"status\":[]}],\"customPagePerCount\":25,\"disputeClaimConfig\":{\"lockerUrlType\":\"ext\",\"workbenchIdMap\":{\"600309\":\"61365\"},\"showClaimNewView\":true,\"enabledForTenants\":[\"600309\"],\"isAuthDisputeAllowed\":true,\"isFeesDisputeAllowed\":true,\"isInterestDisputeAllowed\":true},\"showAsTransactions\":true,\"showTransactionsNewView\":true}"
                  
              }
              '
            params='{
                "baseUrl": "http://localhost:4000",
                "tenantId": "600309",
                "paymentId": "360638000290",
                "transactionId": "360638000290",
                "resourceId": "fcf73c00-281e-4e55-aeb9-ac5f001422a9"
              }'
            app-timezone="Europe/Berlin"
        ></zwe-angelos-details-view-v3>
    </body>
</html>
