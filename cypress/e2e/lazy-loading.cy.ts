/**
 * E2E Tests for Lazy Loading Functionality
 * Tests the complete lazy loading system in a real browser environment
 */

describe('Angelos SDK Lazy Loading', () => {
    beforeEach(() => {
        // Visit the test page
        cy.visit('/');
        
        // Wait for SDK to initialize
        cy.window().should('have.property', 'AngelosSDK');
    });

    describe('SDK Initialization', () => {
        it('should initialize SDK successfully', () => {
            cy.window().then((win) => {
                expect(win.AngelosSDK).to.exist;
            });
            
            // Check console for initialization messages
            cy.window().then((win) => {
                cy.get('@consoleLog').should('have.been.calledWith', 
                    '🚀 Angelos SDK starting with optimized loading...'
                );
            });
        });

        it('should load main bundle only initially', () => {
            // Check network requests
            cy.intercept('GET', '**/angelos.min.js').as('mainBundle');
            
            cy.visit('/');
            cy.wait('@mainBundle');
            
            // Verify main bundle size is reasonable
            cy.get('@mainBundle').then((interception) => {
                expect(interception.response?.body).to.exist;
                // Main bundle should be around 5-6MB
                const contentLength = interception.response?.headers['content-length'];
                if (contentLength) {
                    expect(parseInt(contentLength)).to.be.lessThan(7000000); // 7MB
                    expect(parseInt(contentLength)).to.be.greaterThan(4000000); // 4MB
                }
            });
        });
    });

    describe('Component Registration', () => {
        it('should register all custom elements', () => {
            const componentNames = [
                'zwe-angelos-create-form-v3',
                'zwe-angelos-update-form-v3',
                'zwe-angelos-data-table-v3',
                'zwe-angelos-details-view-v3',
                'zwe-angelos-dynamic-view-v3'
            ];

            componentNames.forEach(name => {
                cy.window().then((win) => {
                    expect(win.customElements.get(name)).to.exist;
                });
            });
        });

        it('should render components when added to DOM', () => {
            // Add create form component
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                win.document.body.appendChild(element);
                
                // Wait for component to render
                cy.wait(1000);
                
                // Check if component has content
                cy.get('zwe-angelos-create-form-v3').should('exist');
                cy.get('zwe-angelos-create-form-v3').should('not.be.empty');
            });
        });

        it('should render data table component', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-data-table-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                win.document.body.appendChild(element);
                
                cy.wait(1000);
                
                cy.get('zwe-angelos-data-table-v3').should('exist');
                cy.get('zwe-angelos-data-table-v3').should('not.be.empty');
            });
        });
    });

    describe('Monaco Editor Lazy Loading', () => {
        it('should not load Monaco initially', () => {
            // Monaco should not be loaded on page load
            cy.window().then((win) => {
                expect(win.monaco).to.be.undefined;
            });
        });

        it('should load Monaco when code editor is needed', () => {
            // Intercept Monaco loading
            cy.intercept('GET', '**/editor.main-*.js').as('monacoBundle');
            
            // Create a code editor
            cy.window().then((win) => {
                if (win.MonacoLazyLoader) {
                    const container = win.document.createElement('div');
                    container.id = 'monaco-container';
                    win.document.body.appendChild(container);
                    
                    win.MonacoLazyLoader.createEditor(container, {
                        value: 'console.log("test");',
                        language: 'javascript'
                    });
                }
            });
            
            // Wait for Monaco to load
            cy.wait('@monacoBundle', { timeout: 10000 });
            
            // Verify Monaco is now available
            cy.window().then((win) => {
                expect(win.monaco).to.exist;
            });
        });

        it('should track Monaco loading stats', () => {
            cy.window().then((win) => {
                if (win.MonacoLazyLoader) {
                    const initialStats = win.MonacoLazyLoader.getStats();
                    expect(initialStats.coreLoaded).to.be.false;
                    expect(initialStats.editorsCreated).to.equal(0);
                    
                    const container = win.document.createElement('div');
                    win.document.body.appendChild(container);
                    
                    win.MonacoLazyLoader.createEditor(container).then(() => {
                        const updatedStats = win.MonacoLazyLoader.getStats();
                        expect(updatedStats.coreLoaded).to.be.true;
                        expect(updatedStats.editorsCreated).to.equal(1);
                    });
                }
            });
        });
    });

    describe('Icon Lazy Loading', () => {
        it('should preload common icons', () => {
            cy.window().then((win) => {
                if (win.IconLazyLoader) {
                    const stats = win.IconLazyLoader.getStats();
                    expect(stats.preloaded).to.be.greaterThan(0);
                }
            });
        });

        it('should load icons on demand', () => {
            cy.window().then((win) => {
                if (win.IconLazyLoader) {
                    const initialStats = win.IconLazyLoader.getStats();
                    
                    // Mock Vue app for testing
                    const mockApp = { component: cy.stub() };
                    
                    win.IconLazyLoader.registerIconsOnDemand(mockApp, ['Add', 'Edit', 'Delete']).then(() => {
                        const updatedStats = win.IconLazyLoader.getStats();
                        expect(updatedStats.onDemandLoaded).to.be.greaterThan(initialStats.onDemandLoaded);
                    });
                }
            });
        });
    });

    describe('Performance Metrics', () => {
        it('should have reasonable initial load time', () => {
            const startTime = Date.now();
            
            cy.visit('/');
            cy.window().should('have.property', 'AngelosSDK');
            
            cy.then(() => {
                const loadTime = Date.now() - startTime;
                expect(loadTime).to.be.lessThan(5000); // Should load within 5 seconds
            });
        });

        it('should have good bundle compression', () => {
            cy.request('/dist/angelos.min.js').then((response) => {
                const contentEncoding = response.headers['content-encoding'];
                if (contentEncoding === 'gzip') {
                    // Gzipped size should be significantly smaller
                    const gzipSize = response.body.length;
                    expect(gzipSize).to.be.lessThan(2000000); // Less than 2MB gzipped
                }
            });
        });
    });

    describe('Error Handling', () => {
        it('should handle component errors gracefully', () => {
            cy.window().then((win) => {
                // Create component with invalid attributes
                const element = win.document.createElement('zwe-angelos-create-form-v3');
                // Don't set required attributes
                win.document.body.appendChild(element);
                
                // Should not crash the page
                cy.get('body').should('exist');
            });
        });

        it('should handle Monaco loading failures', () => {
            // Mock Monaco loading failure
            cy.window().then((win) => {
                if (win.MonacoLazyLoader) {
                    // This should handle the error gracefully
                    const container = win.document.createElement('div');
                    win.document.body.appendChild(container);
                    
                    // Even if Monaco fails to load, page should remain functional
                    cy.get('body').should('exist');
                }
            });
        });
    });

    describe('Memory Management', () => {
        it('should not have memory leaks with multiple components', () => {
            cy.window().then((win) => {
                const initialMemory = (win.performance as any).memory?.usedJSHeapSize || 0;
                
                // Create and destroy multiple components
                for (let i = 0; i < 10; i++) {
                    const element = win.document.createElement('zwe-angelos-create-form-v3');
                    element.setAttribute('entity-id', `test-${i}`);
                    win.document.body.appendChild(element);
                    
                    // Remove after a short delay
                    setTimeout(() => {
                        win.document.body.removeChild(element);
                    }, 100);
                }
                
                cy.wait(2000);
                
                // Memory usage should not increase dramatically
                cy.then(() => {
                    const finalMemory = (win.performance as any).memory?.usedJSHeapSize || 0;
                    if (initialMemory > 0 && finalMemory > 0) {
                        const memoryIncrease = finalMemory - initialMemory;
                        expect(memoryIncrease).to.be.lessThan(50000000); // Less than 50MB increase
                    }
                });
            });
        });
    });
});
