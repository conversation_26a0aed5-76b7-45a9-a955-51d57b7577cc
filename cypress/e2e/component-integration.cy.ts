/**
 * E2E Tests for Component Integration
 * Tests the integration between components and the lazy loading system
 */

describe('Component Integration with Lazy Loading', () => {
    beforeEach(() => {
        cy.visit('/');
        cy.window().should('have.property', 'AngelosSDK');
    });

    describe('Create Form Component', () => {
        it('should render create form with proper structure', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.setAttribute('show-form-actions', 'true');
                element.id = 'test-create-form';
                win.document.body.appendChild(element);
            });

            cy.wait(2000); // Wait for component to fully render

            cy.get('#test-create-form').should('exist');
            cy.get('#test-create-form').should('not.be.empty');
            
            // Check for form elements (these may vary based on actual implementation)
            cy.get('#test-create-form').within(() => {
                // Should contain some form of input or form structure
                cy.get('*').should('have.length.greaterThan', 0);
            });
        });

        it('should handle form events properly', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'event-test-form';
                
                // Add event listeners
                let submitCalled = false;
                element.addEventListener('submit', () => {
                    submitCalled = true;
                });
                
                win.document.body.appendChild(element);
                
                // Store reference for later verification
                (win as any).testFormSubmitCalled = () => submitCalled;
            });

            cy.wait(2000);
            
            // Verify event handling capability exists
            cy.window().then((win) => {
                expect((win as any).testFormSubmitCalled).to.be.a('function');
            });
        });
    });

    describe('Data Table Component', () => {
        it('should render data table with proper structure', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-data-table-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'test-data-table';
                win.document.body.appendChild(element);
            });

            cy.wait(2000);

            cy.get('#test-data-table').should('exist');
            cy.get('#test-data-table').should('not.be.empty');
            
            // Check for table-like structure
            cy.get('#test-data-table').within(() => {
                cy.get('*').should('have.length.greaterThan', 0);
            });
        });

        it('should handle table events properly', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-data-table-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'event-test-table';
                
                let rowClickCalled = false;
                element.addEventListener('row-click', () => {
                    rowClickCalled = true;
                });
                
                win.document.body.appendChild(element);
                (win as any).testTableRowClickCalled = () => rowClickCalled;
            });

            cy.wait(2000);
            
            cy.window().then((win) => {
                expect((win as any).testTableRowClickCalled).to.be.a('function');
            });
        });
    });

    describe('Details View Component', () => {
        it('should render details view component', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-details-view-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'test-details-view';
                win.document.body.appendChild(element);
            });

            cy.wait(2000);

            cy.get('#test-details-view').should('exist');
            cy.get('#test-details-view').should('not.be.empty');
        });
    });

    describe('Dynamic View Component', () => {
        it('should render dynamic view component', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-dynamic-view-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'test-dynamic-view';
                win.document.body.appendChild(element);
            });

            cy.wait(2000);

            cy.get('#test-dynamic-view').should('exist');
            cy.get('#test-dynamic-view').should('not.be.empty');
        });
    });

    describe('Update Form Component', () => {
        it('should render update form component', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-update-form-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'test-update-form';
                win.document.body.appendChild(element);
            });

            cy.wait(2000);

            cy.get('#test-update-form').should('exist');
            cy.get('#test-update-form').should('not.be.empty');
        });
    });

    describe('Multiple Components', () => {
        it('should handle multiple components simultaneously', () => {
            cy.window().then((win) => {
                // Create multiple different components
                const components = [
                    { tag: 'zwe-angelos-create-form-v3', id: 'multi-create' },
                    { tag: 'zwe-angelos-data-table-v3', id: 'multi-table' },
                    { tag: 'zwe-angelos-details-view-v3', id: 'multi-details' }
                ];

                components.forEach(({ tag, id }) => {
                    const element = win.document.createElement(tag);
                    element.setAttribute('entity-id', 'test-entity');
                    element.setAttribute('tenant-id', 'test-tenant');
                    element.id = id;
                    win.document.body.appendChild(element);
                });
            });

            cy.wait(3000);

            // All components should render
            cy.get('#multi-create').should('exist').should('not.be.empty');
            cy.get('#multi-table').should('exist').should('not.be.empty');
            cy.get('#multi-details').should('exist').should('not.be.empty');
        });

        it('should not interfere with each other', () => {
            cy.window().then((win) => {
                // Create two instances of the same component
                const element1 = win.document.createElement('zwe-angelos-create-form-v3');
                element1.setAttribute('entity-id', 'entity-1');
                element1.setAttribute('tenant-id', 'tenant-1');
                element1.id = 'form-1';

                const element2 = win.document.createElement('zwe-angelos-create-form-v3');
                element2.setAttribute('entity-id', 'entity-2');
                element2.setAttribute('tenant-id', 'tenant-2');
                element2.id = 'form-2';

                win.document.body.appendChild(element1);
                win.document.body.appendChild(element2);
            });

            cy.wait(2000);

            // Both should render independently
            cy.get('#form-1').should('exist').should('not.be.empty');
            cy.get('#form-2').should('exist').should('not.be.empty');
            
            // They should be separate elements
            cy.get('#form-1').should('not.equal', cy.get('#form-2'));
        });
    });

    describe('Component Lifecycle', () => {
        it('should handle component removal gracefully', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', 'test-tenant');
                element.id = 'lifecycle-test';
                win.document.body.appendChild(element);
                
                // Store reference for removal
                (win as any).lifecycleTestElement = element;
            });

            cy.wait(1000);
            cy.get('#lifecycle-test').should('exist');

            // Remove the component
            cy.window().then((win) => {
                const element = (win as any).lifecycleTestElement;
                if (element && element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            });

            cy.get('#lifecycle-test').should('not.exist');
        });

        it('should handle attribute changes', () => {
            cy.window().then((win) => {
                const element = win.document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-id', 'initial-entity');
                element.setAttribute('tenant-id', 'initial-tenant');
                element.id = 'attribute-test';
                win.document.body.appendChild(element);
                
                (win as any).attributeTestElement = element;
            });

            cy.wait(1000);
            cy.get('#attribute-test').should('exist');

            // Change attributes
            cy.window().then((win) => {
                const element = (win as any).attributeTestElement;
                element.setAttribute('entity-id', 'updated-entity');
                element.setAttribute('tenant-id', 'updated-tenant');
            });

            // Component should still exist and function
            cy.get('#attribute-test').should('exist').should('not.be.empty');
        });
    });
});
