/* eslint-disable @typescript-eslint/no-var-requires */
const yaml = require('js-yaml');
const fs = require('fs-extra');

function getJsonFromYAML(componentType, businessEntityId, type) {
    const yml = yaml.load(
        fs.readFileSync(
            `./mocks/entities/${businessEntityId}/${componentType}/${type}.yaml`,
            'utf-8'
        )
    );
    return yml;
}

module.exports = {
    getJsonFromYAML
};
