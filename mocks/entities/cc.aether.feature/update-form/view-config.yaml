version: v2
fields:
  state:
    label: State
    tooltip: State
    component: AngelosRadioButton
    values:
      - label: DISABLED
        value: DISABLED
      - label: ENABLED
        value: ENABLED
    rules:
      required: true
  configParams:
    type: array
    fields:
      key:
        label: Key
        tooltip: Key
        type: text
        rules: 
          required: true
      value:
        label: Value
        tooltip: Value
        type: text
      isEncrypted:
        label: Is Encrypted
        tooltip: Is Encrypted
        component: AngelosSwitch
      useVault:
        label: Use Vault
        tooltip: Use Vault
        component: AngelosSwitch
  metadata.tenantId:
    label: Tenant Id
    tooltip: Tenant Id
    type: text
  metadata.tenantCode:
    label: Tenant Code
    tooltip: Tenant Code
    type: text
  metadata.id:
    label: Id
    tooltip: Id
    type: text
  metadata.key:
    label: Key
    tooltip: Key
    type: text
  metadata.name:
    label: Name
    tooltip: Name
    type: text
  metadata.description:
    label: Description
    tooltip: Description
    component: AngelosTextArea
  metadata.requester.module:
    label: Module
    tooltip: Module
    type: text
  metadata.requester.moduleVersion:
    label: Module Version
    tooltip: Module Version
    type: text
  metadata.requester.app:
    label: App
    tooltip: App
    type: text
  metadata.requester.appVersion:
    label: App Version
    tooltip: App Version
    type: text
  metadata.isOptional:
    label: Is Optional
    tooltip: Is Optional
    component: AngelosSwitch
  metadata.scope:
    label: Scope
    tooltip: Scope
    component: AngelosRadioButton
    values:
      - label: God
        value: God
      - label: Tenant
        value: Tenant
  metadata.tags:
    label: Tags
    tooltip: Tags
    type: text
    multiple: true
  metadata.labels:
    label: Labels
    tooltip: Labels
    component: AngelosCodeEditor
    language: json
form:
  sections:
    - title: Overview
      direction: horizontal
      fields:
        - field_key: state
          colspan: 12
    - title: Metadata
      direction: horizontal
      fields:
        - field_key: metadata.tenantId
          colspan: 6
        - field_key: metadata.tenantCode
          colspan: 6
        - field_key: metadata.id
          colspan: 6
        - field_key: metadata.key
          colspan: 6
        - field_key: metadata.name
          colspan: 6
        - field_key: metadata.description
          colspan: 12
        - field_key: metadata.isOptional
          colspan: 6
        - field_key: metadata.scope
          colspan: 12
        - field_key: metadata.tags
          colspan: 6
        - field_key: metadata.labels
          colspan: 12
    - title: Requester
      direction: horizontal
      fields:
        - field_key: metadata.requester.module
          colspan: 6
        - field_key: metadata.requester.moduleVersion
          colspan: 6
        - field_key: metadata.requester.app
          colspan: 6
        - field_key: metadata.requester.appVersion
          colspan: 6
    - title: Config Params
      direction: horizontal
      arrayField: configParams
      sections:
        - title: ''
          direction: horizontal
          fields:
            - field_key: key
              colspan: 6
            - field_key: value
              colspan: 6
            - field_key: isEncrypted
              colspan: 6
            - field_key: useVault
              colspan: 6
