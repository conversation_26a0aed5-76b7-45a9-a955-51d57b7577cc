- dataEndPoint: test
  refId: data1
  httpRequest:
      method: 'GET'
      url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: |-
      const listConfig= JSON.stringify([{"type":"PREPAID","order":1,"iconPath":"https://hercules-assets.mum1.zetaapps.in/common-assets/3.0.300/clusters/ruby/icons/prepaid.svg","attributes":{"otc":"OTC Benifit","rewards":"Rewards Benifit","discount":"Discount Benifit","benefitTypeKey":"corpben.benefitAccountType"},"subtitleType":"Date","serviceParams":{"ifiId":"301117","individualId":"5537ef85-2c69-4f96-8564-b1fd7d05b2e2","accountUrl":"https://sb1-god-aries.mum1.zetaapps.in","locale":"en-IN","achType":"real"},"serviceClassName":"PearlAccountListService"}]);
      return { ...res, ledgerId: 2580404821564676105, postingHeader: 'Posting Summary' ,listConfig};
- dataEndPoint: test
  refId: data2
  httpRequest:
      method: 'GET'
      url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: "return { tenantId: '301117',first: { code: '4567', second: { third: { state: 'Karnataka', country: 'India' }} }, showTable: true ,ledger:{id:'1088644771886435437',tenantId:'600309',coaId:'1441665598567441191',serviceUrl:'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon'}}"
