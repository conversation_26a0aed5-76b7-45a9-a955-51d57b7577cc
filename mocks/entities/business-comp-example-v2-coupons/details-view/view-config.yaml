version: v2
css: |-
    .custom-ach-search-container{background-color: #f5f5f5;padding: 20px;}
views:
    - sections:
          - attributes:
                hasBottomDivider: true
            key: postingSummary
            fields:
                - field: ID
                  label: ID
                  tooltip: ID
                  value: context.aetherData.id
                  visibleIf:
                      '==':
                          - var: context.enableRefund\.test
                          - male
                - field: VBO ID
                  label: VBO ID
                  tooltip: VBO ID
                  value: context.aetherData.vboID
      header:
          title:
              value: data1.postingHeader
      class: posting-summary-view
    - sections:
          - key: first-custom-comp
            type: custom
            className: custom-account-list
            config:
                name: account-list
                package:
                    name: account-list
                    version: '1.1.28-optum.1.7'
                props:
                    acct-config-json:
                        value: data1.listConfig
