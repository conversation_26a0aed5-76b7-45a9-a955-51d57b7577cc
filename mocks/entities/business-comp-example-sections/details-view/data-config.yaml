- dataEndPoint: test
  refId: data1
  httpRequest:
    method: 'GET'
    url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: "return { ...res }"
- dataEndPoint: test
  refId: data2
  httpRequest:
    method: 'GET'
    url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: "return { first: { code: '4567', second: { third: { state: 'Karnataka', country: 'India' }} }, showTable: true }"
