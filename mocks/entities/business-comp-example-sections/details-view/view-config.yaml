
version: v2
header:
  title: Bundle Definition
  type: colorless
  paddingless: true
  size: small
  isBordered: false
sections:
  - key: first-custom-comp
    type: custom
    onfig:
      name: zwe-account-overview
      package:
        name: '@zeta-business/accounts-overview'
        version: '1.0.0'
      props:
        - key: 'accountID'
          value: context.account.id
        - key: 'accountName'
          value: context.account.name
        - key: 'rubyBaseUrl'
          value: context.rubyBaseUrl
  - key: second-key-value
    label: Overview
    attributes:
      hasBottomDivider: true
    tooltip: Information about sections
    fields:
      - field: ID
        label: ID
        tooltip: ID
        value: context.aetherData.id
        visibleIf:
            "==":
            - var: context.enableRefund\.test
            - male
      - field: VBO ID
        label: VBO ID
        tooltip: VBO ID
        value: context.aetherData.vboID
      - field: Code
        label: Code
        tooltip: Code
        value: context.aetherData.shortCode
      - field: Name
        label: Name
        value: context.aetherData.name
      - field: Description
        label: Description
        value: context.aetherData.description
      - field: Status
        label: Status
        tooltip: Status
        value: context.aetherData.status
      - field: Created
        label: Created
        value: context.aetherData.createdAt
      - field: Modified
        label: Modified
        value: context.aetherData.updatedAt
      - section: 
          label: Overview
          key: second
          fields: 
            - field: Modified
              label: Modified
              value: context.aetherData.updatedAt
            - section: 
                label: 'Test' 
                key: second
                isJson: true
                type: accordion
                data: context.free
                attributes: 
                  hasBottomDivider: true