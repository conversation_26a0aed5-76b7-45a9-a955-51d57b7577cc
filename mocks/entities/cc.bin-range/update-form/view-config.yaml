version: v2
css: ".config-accordian-wrap .z-accordion{padding: 0 16px;}"
fields:
  # Section 1 - Overview
  cardType:
    label: Card Type
    tooltip: Type of Card ex. Physical,Virtual
    component: AngelosRadioButton
    values:
      - label: Physical
        value: PHYSICAL
      - label: Virtual
        value: VIRTUAL
    rules:
      required: true
  bin: # should convert into string on emit
    label: BIN
    tooltip: Bank Identifier Number that is the first 6 of 8 digits of the card number.
    type: number
    rules:
      required: true
  range: # should convert into string on emit
    label: Bin Range
    tooltip: Value of the BIN range
    type: number
    placeholder: 23
    rules:
      required: true
  features:
    label: Features
    component: AngelosCheckbox
    values:
      - label: EMV
        value: EMV
      - label: MagStripe
        value: MAG_STRIPE
      - label: NFC
        value: NFC
    rules:
      required: true
  
  ### Section 2 - Card Configuration
  
  # Section 2.1 - Base Configuration
  cardConfig.maskedPanFormat: #need optional indicator
    label: Masked PAN Format (RegEx)
    tooltip: Indicates the format of masked pan to be shown in the embossing file
    placeholder: "[0-9]{6}xxxxxx[0-9]{4}"
  cardConfig.disallowedPatternsForPAN:
    label: Disallowed PAN Patterns (RegEx)
    tooltip: For new PAN generation with this the BIN Range, these patterns are avoided if present
    multiple: true
    placeholder: "\\d{8}9\\d*"
    rules:
      required: true
  cardConfig.dispatch\.allowed: 
    label: Enable Dispatch
    tooltip: Enable dispatch for this bin range or not
    value: false
    component: AngelosSwitch
  cardConfig.association\.fullCardDetails\.allowed: 
    label: Association of Card
    tooltip: Associate card number with CRN to activate card using CRN
    value: false
    component: AngelosSwitch
  cardConfig.offlinePin\.verification\.enabled: 
    label: PIN Verification (Offline Txn)
    tooltip: Offline PIN verification is supported by EMV card. If enabled then we need SMI, SMC and PAN Crypto Keys must be present BIN Range.
    value: false
    component: AngelosSwitch
  cardConfig.backdate\.transaction\.allowed: 
    label: Backdate Transactions
    tooltip: Enable backdated transactions on this BIN Range
    value: false
    component: AngelosSwitch
  
  # Section 2.2 - ATC Configuration
  cardConfig.atcConfig.threshold: 
    label: ATC Threshold
    tooltip: Threshold above which the Application Transaction Counter will come into effect
    type: number
    rules:
      required: true
      min_value: 0
  cardConfig.atcConfig.thresholdBreachAction:
    label: Action on Threshold Breach 
    tooltip: The action on the card when the treshold is breached 
    component: AngelosSelect
    values:
      - label: None
        value: NONE
      - label: Temporary block card
        value: TEMPORARY_BLOCK_CARD
      - label: Permanent block card
        value: PERMANENT_BLOCK_CARD
    rules:
      required: true
  cardConfig.atcConfig.isEnabledForOnlineTxns:
    label: Enable ATC (Online Txn)
    tooltip: Whether ATC will be validated for online transactions 
    value: false
    component: AngelosSwitch
  cardConfig.atcConfig.isEnabledForOfflineTxns:
    label: Enable ATC (Offline Txn)
    tooltip: Whether ATC will be validated for offline transactions 
    value: false
    component: AngelosSwitch
  cardConfig.atcConfig.actionResetPeriod: 
    label: Action Reset Period
    tooltip: The time frame taken for the ATC to reset automatically. End Of Day - The ATC will reset at the calendar end-of-day (at 12 am). Hours - The hours after threshold breach the ATC will reset
    component: AngelosRadioButton
    values:
      - label: End of Day
        value: END_OF_DAY
      - label: Hours
        value: HOURS
    rules:
      required: true
  cardConfig.atcConfig.actionResetFixedDelayHours: # should convert into string on emit
    label: Threshold Reset Time
    tooltip: The hours after which the ATC will reset automatically
    type: number
    condition:
      visibleIf:
        "===":
          - var: formModel.cardConfig.atcConfig.actionResetPeriod
          - HOURS
    rules:
      required: true
      min_value: 1
      max_value: 24
  
  # Section 2.3 - Incorrect PIN Configuration
  cardConfig.incorrectPinConfig.threshold: 
    label: Failed Attempts Threshold
    tooltip: The number of incorrect PIN attempts permitted
    type: number
    rules:
      required: true
      min_value: 0
  cardConfig.incorrectPinConfig.thresholdBreachAction:
    label: Action on Threshold Breach 
    tooltip: The action on the card when the treshold is breached 
    component: AngelosSelect
    values:
      - label: None
        value: NONE
      - label: Temporary block card
        value: TEMPORARY_BLOCK_CARD
    rules:
      required: true
  cardConfig.incorrectPinConfig.transactionsAllowedPostThresholdBreach.domestic:
    label: Allowed Txn on Breach (Domestic)
    tooltip: Allow domestic transactions after threshold breach, enabled only when action on treshold breach is NONE (mutiple selection)
    component: AngelosSelect
    multiSelect: true
    values:
      - label: Card present with PIN
        value: CARD_PRESENT_WITH_PIN
      - label: Card present without PIN
        value: CARD_PRESENT_WITHOUT_PIN
      - label: Card not present
        value: CARD_NOT_PRESENT
    rules:
      required: true
  cardConfig.incorrectPinConfig.transactionsAllowedPostThresholdBreach.international:
    label: Allowed Txn on Breach (International)
    tooltip: Allow international transactions after threshold breach, enabled only when action on treshold breach is NONE (mutiple selection)
    component: AngelosSelect
    multiSelect: true
    values:
      - label: Card present with PIN
        value: CARD_PRESENT_WITH_PIN
      - label: Card present without PIN
        value: CARD_PRESENT_WITHOUT_PIN
      - label: Card not present
        value: CARD_NOT_PRESENT
    rules:
      required: true
  cardConfig.incorrectPinConfig.actionResetPeriod: 
    label: Action Reset Period
    tooltip: The time frame taken for the ATC to reset automatically. End Of Day - The ATC will reset at the calendar end-of-day (at 12 am). Hours - The hours after threshold breach the ATC will reset
    component: AngelosRadioButton
    values:
      - label: End of Day
        value: END_OF_DAY
      - label: Hours
        value: HOURS
    rules:
      required: true
  cardConfig.incorrectPinConfig.resetCounterOnActionReset:
    label: Reset Counter on Card Unblock
    tooltip: If the card is unblocked via API then should the counters be reset. If True - when the card status is changed to active, then the PIN counter will reset. If False - when the card status is changed to active, then the PIN count will not reset and the transactions enabled will be as per the Allowed Transaction configuration
    value: false
    component: AngelosSwitch
  cardConfig.incorrectPinConfig.actionResetFixedDelayHours: # should convert into string on emit
    label: Threshold Reset Time
    tooltip: Number of hours after which the PIN Counter will reset automatically  
    type: number
    condition:
      visibleIf:
        "===":
          - var: formModel.cardConfig.incorrectPinConfig.actionResetPeriod
          - HOURS
    rules:
      required: true
      min_value: 1
      max_value: 24
    
  # Section 2.4 - Tokenisation
  cardConfig.tokenization\.enabled: 
    label: Enable Tokenization
    tooltip: Tokens enabled for specified BIN Range
    value: false
    component: AngelosSwitch
  cardConfig.stepUp\.required: 
    label: Enable 2FA
    tooltip: 2 factor authentication (2FA) is required to approve provisioning of token
    value: false
    component: AngelosSwitch

  # Section 2.5 - Contactless Transactions
  cardConfig.maximum\.allowed\.contactless\.amount: # should convert into string on emit
    label: Max. Txn Amount Allowed
    tooltip: Maximum allowed amount for a contactless transaction, above which PIN validation will be required
    type: number
    rules:
      required: true
  
  ### Section 3 - Key Configuration
  
  # Section 3.1 CVV
  keyConfig.cvv\.generation\.mechanism:
    label: CVV Generation Mechanism
    tooltip: CVV generation should happen inside Hardwares Security module (HSM) or inside the code
    component: AngelosSelect
    enums:
      USING_HSM: Using HSM
      INSIDE_HSM: Inside HSM
    rules:
      required: true
  keyConfig.cvv1\.service\.code: 
    label: CVV 1 Service Code
    tooltip: Service code for CVV generation needed along with cvv1 and cvv12 keys. It determines the Bank's rules on the card such as domestic/international transaction use limitation, online/offline auth, etc. 
    placeholder: 620
    rules:
      required: true
  keyConfig.cvv1\.keyId: 
    label: CVV 1 Key ID
    tooltip: Generation and matching for CVV1
    placeholder: KEYIN00148
    rules:
      required: true
  keyConfig.cvv2\.keyId: 
    label: CVV 2 Key ID
    tooltip: Generation and matching for CVV2
    placeholder: KEYIN03049
  keyConfig.cvv12\.keyId:
    label: CVV12 Key ID
    tooltip: Generation and matching for CVV12
    placeholder: KEYIN03046
    rules:
      required: true
  keyConfig.cvv1\.slotId: # should convert into string on emit
    label: CVV 1 Slot ID
    tooltip: The slot id in the Hardwares Security module (HSM) which should be used for CVV1 generation
    type: number
    placeholder: 1
    rules: 
      required: true
  keyConfig.cvv2\.slotId: # should convert into string on emit
    label: CVV 2 Slot ID
    tooltip: The slot id in the Hardwares Security module (HSM)  which should be used for CVV2 generation
    type: number
    placeholder: 1
  keyConfig.cvv12\.slotId: # should convert into string on emit
    label: CVV12 Slot ID
    tooltip: The slot id in the Hardwares Security module (HSM)  which should be used for CVV12 generation
    type: number
    placeholder: 1
    rules:
      required: true
  keyConfig.cvv1\.keyAlgorithm:
    label: CVV 1 Key Algorithm
    tooltip: CVV1 Key algorithms are encryption operations via DES (Data Encryption Standard) or DES3 (Triple - Data Encryption Standard) used to authorize and authenticate transactions from CVV1 
    component: AngelosRadioButton
    values:
      - label: DES
        value: DES
      - label: DES3
        value: DES3
    rules: 
      required: true
  keyConfig.cvv12\.keyAlgorithm:
    label: CVV 12 Key Algorithm
    tooltip: CVV12 Key algorithms are encryption operations via DES (Data Encryption Standard) or DES3 (Triple - Data Encryption Standard) used to authorize and authenticate transactions from CVV12
    component: AngelosRadioButton
    values:
      - label: DES
        value: DES
      - label: DES3
        value: DES3
    rules: 
      required: true
  
  # Section 3.2 CAVV
  keyConfig.cavv\.generation\.mechanism:
    label: CAVV Generation Mechanism
    tooltip: CAVV generation should happen inside Hardwares Security module (HSM) or inside the code 
    component: AngelosSelect
    enums:
      USING_HSM: Using HSM
      INSIDE_HSM: Inside HSM
    rules: 
      required: true
  keyConfig.cavv1\.keyId:
    label: CAVV 1 Key ID
    tooltip: Validates the CAVV1 data (only Visa)
    placeholder: KEYIN66933
    rules:
      required: true
  keyConfig.cavv2\.keyId:
    label: CAVV 2 Key ID
    tooltip: Validates the CAVV2 data (only Visa)
    placeholder: KEYIN66992
    rules:
      required: true
  keyConfig.cavv12\.keyId:
    label: CAVV12 Key ID
    tooltip: Validates the CAVV12 data (only Visa)
  keyConfig.cavv\.slotId: # should convert into string on emit
    label: CAVV Slot ID
    tooltip: The slot id in the Hardwares Security module (HSM)  which should be used for CAVV generation
    type: number
    placeholder: 1
    rules:
      required: true
  keyConfig.cavv1\.keyAlgorithm:
    label: CAVV 1 Key Algorithm
    tooltip: CAVV1 Key algorithms are encryption operations via DES (Data Encryption Standard) or DES3 (Triple - Data Encryption Standard) used to authorize and authenticate transactions using CAVV1 
    component: AngelosRadioButton
    values:
      - label: DES
        value: DES
      - label: DES3
        value: DES3
    rules:
      required: true
  keyConfig.cavv2\.keyAlgorithm:
    label: CAVV 12 Key Algorithm
    tooltip: CAVV2 Key algorithms are encryption operations via DES (Data Encryption Standard) or DES3 (Triple - Data Encryption Standard) used to authorize and authenticate transactions using CAVV2
    component: AngelosRadioButton
    values:
      - label: DES
        value: DES
      - label: DES3
        value: DES3
    rules:
      required: true
  # Section 3.3 Crypto
  keyConfig.offline\.pin\.crypto\.keyId:
    label: Offline PIN Key ID
    tooltip: Offline PIN crypto ID allows the cardholder to create a unique PIN code that is stored securely on the card's chip.
    placeholder: KEYIN106011
    rules:
      required: true

  ### Section 4 - Clearing Configuration
  
  # Section 4.1 - Refund/Return Configuration
  clearing.refund\.isEnabled: # should convert into string on emit
    label: Enable Refund
    tooltip: FALSE indicates that the refund recieved in the clearing file will not be posted to the customer's account without the operation agent's manual intervention 
    value: false
    component: AngelosSwitch
  clearing.refund\.processor\.v2\.enabled: 
    label: Enable New Refund Processor
    tooltip: If channel code present and this flag set to true/not present then we apply the refund processor v2 else refund processor v1 will be applied
    value: false
    component: AngelosSwitch
  clearing.refund\.partial\.isEnabled: # should convert into string on emit
    label: Enable Partial Refund
    tooltip: FALSE indicates that the partial refund recieved in the clearing file will not be posted to the customer's account without manual intervention 
    value: false
    component: AngelosSwitch
  clearing.credit\.forUnmatchedRefund\.isEnabled: 
    label: Post Unmatched Refund
    tooltip: Enable posting of credit for refunds where the corresponding first presentment cannot be identifed
    value: false
    component: AngelosSwitch
  clearing.merchandiseReturn\.capture\.isEnabled: 
    label: Post Online Refund
    tooltip: Indicates if online refunds need to be honored 
    value: false
    component: AngelosSwitch
  clearing.credit\.forUnmatchedMerchandiseReturn\.isEnabled: 
    label: Post Unmatched Merchandise Return
    tooltip: For unmatched Merchandise Return transactions, amount is refunded directly to the user's account. 
    value: false
    component: AngelosSwitch

  # Section 4.2 - Presentments
  clearing.debit\.adjustment\.forUnmatchedAuth\.isEnabled: 
    label: Post Unmatched Presentments
    tooltip: Indicates if a clearing records needs to be posted if a corresponding authorization is not found 
    value: false
    component: AngelosSwitch
  clearing.recurring\.debit\.adjustment\.forUnmatchedAuth\.isEnabled: 
    label: Post Unmatched Presentments for Recurring Authorizations
    tooltip: Indicates if a clearing records needs to be posted if a corresponding authorization is not found for recurring transactions 
    value: false
    component: AngelosSwitch
  clearing.credit\.forUnmatchedOCT\.isEnabled: 
    label: Enable Credit for Unmatched OCT
    tooltip: For unmatched OCT (Original Credit Transactions) the amount is refunded directly to the user's account. 
    value: false
    component: AngelosSwitch
  clearing.late\.presentment\.debit\.isEnabled: 
    label: Post Late Presentment Debit
    tooltip: True indicates that the system will post the late presentment debit i.e a presentment recieved after the authorization hold has expired. False indicates that such late presentments will be sent to the operations queue for manual assessment
    value: false
    component: AngelosSwitch
  clearing.late\.presentment\.credit\.isEnabled: 
    label: Post Late Presentment Credit
    tooltip: Support handling of late presentment for credit transactions.
    value: false
    component: AngelosSwitch
  clearing.duplicate\.debit\.isEnabled: 
    label: Post Duplicate Debit
    tooltip: Allow/disallow posting of a duplicate debit Presentment to the customer. If false duplicate presentments will be sent to the operations queue for manual assessment
    value: false
    component: AngelosSwitch
  clearing.duplicate\.credit\.isEnabled: 
    label: Post Duplicate Credit
    tooltip: Allow/disallow posting of a duplicate credit Presentment to the customer. If false duplicate presentments will be sent to the operations queue for manual assessment
    value: false
    component: AngelosSwitch
  clearing.failed\.debit\.isEnabled: 
    label: Enable Failed Debit
    tooltip: Support handling of failed presentment for debit transactions.
    value: false
    component: AngelosSwitch
  clearing.failed\.credit\.isEnabled: 
    label: Enable Failed Credit
    tooltip: Support handling of failed presentment for credit transactions.
    value: false
    component: AngelosSwitch
  clearing.forced\.debit\.isEnabled: 
    label: Enable Forced Debit
    tooltip: Support handling of failed presentment for credit transactions.
    value: false
    component: AngelosSwitch
  clearing.payment\.capture\.withUpdatedAmount\.isEnabled: 
    label: Enable Changing Clearing Amt.
    tooltip: Allow capture with an amount different than authorization 
    value: false
    component: AngelosSwitch
  clearing.tat\.harmonisation\.days:
    label: Authorization Drop Days
    tooltip: Used to indicate the number of days after which the authorization hold will be dropped or reversed if the clearing file has not been recieved 
    type: number
    placeholder: 5
    rules:
      required: true
  clearing.opsCentre\.processDefinitionKey:
    label: Ops Center Queue Name
    tooltip: Name of the Operations Center Queue on which clearance exceptions will be posted
    placeholder: PZClearingException_Maker
    rules:
      required: true

  ### Section 5 - ACS Configuration
  
  # Section 5.1 - Configuration
  acs.is\.enabled: 
    label: Enable ACS
    tooltip: Enable Zeta ACS from Cipher (only for MasterCard)
    value: false
    component: AngelosSwitch
  acs.name: 
    label: ACS Name
    tooltip: Name of this ACS configuration
    placeholder: acs-manager url
    rules:
      required: true
  acs.avv1\.keyid: 
    label: AAV 1 Key ID
    tooltip: Needed to authorize CAVV1 validation for ecomm transactions 
    placeholder: KEYIN222915
    rules:
      required: true
  acs.avv12\.keyid:
    label: AAV 12 Key ID
    tooltip: Needed to authorize CAVV12 validation for ecomm transactions 
    placeholder: KEYIN222915
    rules:
      required: true

   ### Section 6 - ISO Configuration
  
  # Section 6.1 - All Networks
  iso.cardholder\.billing\.currency:
    label: Cardholder Billing Currency
    tooltip: Cardholder Billing Currency is the currency that the issuer uses to bill a cardholder for their transactions
    rules: 
      required: true
  iso.allowed\.transaction\.currency\.list:
    label: Allowed Transaction Currencies
    tooltip: The supported currency codes allowed for transactions 
    rules: 
      required: true
  iso.ac\.mk\.keyId:
    label: AC MK Key ID
    tooltip: Master key for cryptographic operation for EMV chip card.
    rules: 
      required: true
  
  # Section 6.2 - VISA
  iso.visa.pos\.zpk\.keyId: 
    label: POS ZPK Key ID
    tooltip: Zone PIN Key (ZPK) used in PIN encryption/decryption for POS transactions 
    rules: 
      required: true
  iso.visa.pos\.zpk\.slotId: # should convert into string on emit
    label: POS ZPK Slot ID
    tooltip: The slot id used for PIN decryption (Only if we are now using default slot - 1)
    type: number
    rules: 
      required: true
  iso.visa.ac\.mk\.slotId: # should convert into string on emit
    label: AC MK Slot ID
    tooltip: The slot id used for cryptogram generation in a chip card. (Only if we are not using default slot - 1)
    type: number
    rules: 
      required: true

   # Section 6.3 - Mastercard
  iso.mastercard.mastercard\.memberID:
    label: Mastercard Member ID
    tooltip: Zone PIN Key (ZPK) used in PIN encryption/decryption for POS transactions 
  iso.mastercard.mastercard\.pek\.setup:
    label: Mastercard PEK Setup
    tooltip: The slot id used for PIN decryption (Only if we are now using default slot - 1)
    component: AngelosRadioButton
    values:
      - label: Static
        value: STATIC
      - label: Dynamic
        value: DYNAMIC
    rules: 
      required: true
  iso.mastercard.smc\.mk\.keyId:
    label: SMC MK Key ID
    tooltip: The slot id used for cryptogram generation in a chip card. (Only if we are not using default slot - 1)
    isVisible: 
      handler: return formModel.cardConfig['offlinePin.verification.enabled']
    rules: 
      required: true
  iso.mastercard.smi\.mk\.keyId:
    label: SMI MK Key ID
    tooltip: The slot id used for cryptogram generation in a chip card. (Only if we are not using default slot - 1)
    isVisible: 
      handler: return formModel.cardConfig['offlinePin.verification.enabled']
    rules: 
      required: true
  iso.mastercard.pos\.zpk\.keyid:
    label: POS ZPK Key ID
    tooltip: The slot id used for cryptogram generation in a chip card. (Only if we are not using default slot - 1)
    rules: 
      required: true

form:
  sections:
    - title: Overview
      direction: horizontal
      fields: 
        - field_key: cardType
          colspan: 6
        - field_key: bin
          colspan: 6
        - field_key: range
          colspan: 6
        - field_key: features
          colspan: 12
    - title: Card Configuration
      subtitle: Usable functionalities of the card such as tokenization, outcome of entering invalid PIN, Application Transaction Counter, etc.
      layoutType: accordion
      class: config-accordian-wrap
      accordion-attributes:
        variant: boxed
        keepExpanded: multiple
      sections: 
        - accordion-attributes:
            title: Base Configuration
            expanded: false
          direction: horizontal
          fields: 
            - field_key: cardConfig.maskedPanFormat
              colspan: 6
            - field_key: cardConfig.disallowedPatternsForPAN
              colspan: 6
            - field_key: cardConfig.dispatch\.allowed
              colspan: 6
            - field_key: cardConfig.association\.fullCardDetails\.allowed
              colspan: 6
            - field_key: cardConfig.offlinePin\.verification\.enabled
              colspan: 6
            - field_key: cardConfig.backdate\.transaction\.allowed
              colspan: 6
        - accordion-attributes:
            title: Application Transaction Counter (ATC)
            expanded: false
          subtitle: ATC is maintained by the chip card application, that provides a sequential reference to each transaction. A duplicate ATC, a decrease in ATC or a large jump in ATC values may indicate data copying or other fraud to the issuer.
          direction: horizontal
          fields: 
            - field_key: cardConfig.atcConfig.threshold
              colspan: 6
            - field_key: cardConfig.atcConfig.thresholdBreachAction
              colspan: 6
            - field_key: cardConfig.atcConfig.isEnabledForOnlineTxns
              colspan: 6
            - field_key: cardConfig.atcConfig.isEnabledForOfflineTxns
              colspan: 6
            - field_key: cardConfig.atcConfig.actionResetPeriod
              colspan: 6
            - field_key: cardConfig.atcConfig.actionResetFixedDelayHours
              colspan: 6
        - accordion-attributes:
            title: Incorrect PIN Counter
            expanded: false
          subtitle: Maximum number of incorrect PIN tries and the action to be taken on the card for breaching the maximum limit.
          direction: horizontal
          fields: 
            - field_key: cardConfig.incorrectPinConfig.threshold
              colspan: 6
            - field_key: cardConfig.incorrectPinConfig.thresholdBreachAction
              colspan: 6
            - field_key: cardConfig.incorrectPinConfig.transactionsAllowedPostThresholdBreach.domestic
              colspan: 6
            - field_key: cardConfig.incorrectPinConfig.transactionsAllowedPostThresholdBreach.international
              colspan: 6
            - field_key: cardConfig.incorrectPinConfig.actionResetPeriod
              colspan: 6
            - field_key: cardConfig.incorrectPinConfig.resetCounterOnActionReset
              colspan: 6
            - field_key: cardConfig.incorrectPinConfig.actionResetFixedDelayHours
              colspan: 6
        - accordion-attributes:
            title: Tokenization
            expanded: false
          subtitle: Tokenization creates a card token that replaces the customer's senstive card information with a digital identifier, that can be stored by a merchant and used for recurring transactions.
          direction: horizontal
          fields: 
            - field_key: cardConfig.tokenization\.enabled
              colspan: 6
            - field_key: cardConfig.stepUp\.required
              colspan: 6
        - accordion-attributes:
            title: Contactless Transactions
            expanded: false
          subtitle: Card transactions made without physically touching a payment terminal.
          direction: horizontal
          fields: 
            - field_key: cardConfig.maximum\.allowed\.contactless\.amount
              colspan: 6
    - title: Key Configuration
      subtitle: Authenticate the card and ensure that it can only be accessed by authorized parties.
      layoutType: accordion
      class: config-accordian-wrap
      accordion-attributes:
        variant: boxed
        keepExpanded: multiple
      sections: 
        - accordion-attributes:
            title: CVV
            expanded: false
          subtitle: CVV is 3 digit number used to verify if the card is in the physical possession of the person making the purchase.
          direction: horizontal
          fields: 
            - field_key: keyConfig.cvv\.generation\.mechanism
              colspan: 12
            - field_key: keyConfig.cvv1\.service\.code
              colspan: 12
            - field_key: keyConfig.cvv1\.keyId
              colspan: 4
            - field_key: keyConfig.cvv2\.keyId
              colspan: 4
            - field_key: keyConfig.cvv12\.keyId
              colspan: 4
            - field_key: keyConfig.cvv1\.slotId
              colspan: 4
            - field_key: keyConfig.cvv2\.slotId
              colspan: 4
            - field_key: keyConfig.cvv12\.slotId
              colspan: 4
            - field_key: keyConfig.cvv1\.keyAlgorithm
              colspan: 4
            - field_key: keyConfig.cvv12\.keyAlgorithm
              colspan: 4
        - accordion-attributes:
            title: CAVV
            expanded: false
          subtitle: CAVV is used to authenticate the identity of the cardholder during an online transaction.
          direction: horizontal
          fields: 
            - field_key: keyConfig.cavv\.generation\.mechanism
              colspan: 12
            - field_key: keyConfig.cavv1\.keyId
              colspan: 4
            - field_key: keyConfig.cavv2\.keyId
              colspan: 4
            - field_key: keyConfig.cavv12\.keyId
              colspan: 4
            - field_key: keyConfig.cavv\.slotId
              colspan: 4
            - field_key: keyConfig.cavv1\.keyAlgorithm
              colspan: 4
            - field_key: keyConfig.cavv2\.keyAlgorithm
              colspan: 4
        - accordion-attributes:
            title: Crypto
            expanded: false
          subtitle: A security measure measure embedded in the card's chip that helps protect against fraud.
          direction: horizontal
          fields: 
            - field_key: keyConfig.offline\.pin\.crypto\.keyId
              colspan: 4
    - title: Clearing Configuration
      subtitle: Clearing of transactions for this card such as refund, postings, etc.
      layoutType: accordion
      class: config-accordian-wrap
      accordion-attributes:
        variant: boxed
        keepExpanded: multiple
      sections: 
        - accordion-attributes:
            title: Refund/Return
            expanded: false
          direction: horizontal
          fields: 
            - field_key: clearing.refund\.isEnabled
              colspan: 6
            - field_key: clearing.refund\.processor\.v2\.enabled
              colspan: 6
            - field_key: clearing.refund\.partial\.isEnabled
              colspan: 6
            - field_key: clearing.credit\.forUnmatchedRefund\.isEnabled
              colspan: 6
            - field_key: clearing.merchandiseReturn\.capture\.isEnabled
              colspan: 6
            - field_key: clearing.credit\.forUnmatchedMerchandiseReturn\.isEnabled
              colspan: 6  
        - accordion-attributes:
            title: Presentments
            expanded: false
          direction: horizontal
          fields: 
            - field_key: clearing.debit\.adjustment\.forUnmatchedAuth\.isEnabled
              colspan: 6
            - field_key: clearing.recurring\.debit\.adjustment\.forUnmatchedAuth\.isEnabled
              colspan: 6
            - field_key: clearing.credit\.forUnmatchedOCT\.isEnabled
              colspan: 12
            - field_key: clearing.late\.presentment\.debit\.isEnabled
              colspan: 6
            - field_key: clearing.late\.presentment\.credit\.isEnabled
              colspan: 6
            - field_key: clearing.duplicate\.debit\.isEnabled
              colspan: 6   
            - field_key: clearing.duplicate\.credit\.isEnabled
              colspan: 6  
            - field_key: clearing.failed\.debit\.isEnabled
              colspan: 6  
            - field_key: clearing.failed\.credit\.isEnabled
              colspan: 6  
            - field_key: clearing.forced\.debit\.isEnabled
              colspan: 6
            - field_key: clearing.payment\.capture\.withUpdatedAmount\.isEnabled
              colspan: 6
            - field_key: clearing.tat\.harmonisation\.days
              colspan: 6  
            - field_key: clearing.opsCentre\.processDefinitionKey
              colspan: 6  
    - title: ACS Configuration
      subtitle: Authorize and authenticate cardholders during transactions (only for MasterCard)
      layoutType: accordion
      class: config-accordian-wrap
      accordion-attributes:
        variant: boxed
        keepExpanded: multiple
      sections: 
        - accordion-attributes:
            title: Configuration
            expanded: false
          direction: horizontal
          fields: 
            - field_key: acs.is\.enabled
              colspan: 6
            - field_key: acs.name
              colspan: 6
            - field_key: acs.avv1\.keyid
              colspan: 6
            - field_key: acs.avv12\.keyid
              colspan: 6
    - title: ISO Configuration
      subtitle: Cryptographic keys used for securing network communication by encrypting and decrypting data and authenticating users.
      layoutType: accordion
      class: config-accordian-wrap
      accordion-attributes:
        variant: boxed
        keepExpanded: multiple
      sections: 
        - accordion-attributes:
            title: All Networks
            expanded: false
          direction: horizontal
          fields: 
            - field_key: iso.cardholder\.billing\.currency
              colspan: 6
            - field_key: iso.allowed\.transaction\.currency\.list
              colspan: 6
            - field_key: iso.ac\.mk\.keyId
              colspan: 6
        - accordion-attributes:
            title: Visa
            expanded: false
          direction: horizontal
          fields: 
            - field_key: iso.visa.pos\.zpk\.keyId
              colspan: 6
            - field_key: iso.visa.pos\.zpk\.slotId
              colspan: 6
            - field_key: iso.visa.ac\.mk\.slotId
              colspan: 6
        - accordion-attributes:
            title: Mastercard
            expanded: false
          direction: horizontal
          fields: 
            - field_key: iso.mastercard.mastercard\.memberID
              colspan: 6
            - field_key: iso.mastercard.mastercard\.pek\.setup
              colspan: 6
            - field_key: iso.mastercard.smc\.mk\.keyId
              colspan: 6
            - field_key: iso.mastercard.smi\.mk\.keyId
              colspan: 6
            - field_key: iso.mastercard.pos\.zpk\.keyid
              colspan: 6