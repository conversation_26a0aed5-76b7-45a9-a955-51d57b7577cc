version: v2
fields:
    name:
        label: Name
        placeholder: Enter Name
        maxlength: 50
    description:
        label: Description (Optional)
        component: AngelosTextArea
        maxlength: 140
        value:
            handler: return formModel.name
        rules:
            required: false
    multi:
        label: Description (Optional)
        component: AngelosSelect
        maxlength: 140
        filterable: true
        values:
            - value: hello
              label: hello
              description: hello world
            - value: hello2
              label: hello2
              description: hello2 world
            - value: hello3
              label: hello3
              description: hello3 world
        rules:
            required: false
    ledgerDetails:
        type: array
        fields:
            glName:
                label: GL Name
                placeholder: Enter Text
                rules:
                    required: true
            glAccount:
                label: GL Account
                placeholder: Enter Text
            accountingType:
                label: Accounting Type
                placeholder: Enter Text
            tags:
                label: Tags
                component: AngelosSelect
                placeholder: Enter Tag
                chipIcon: cancel
                disabled: false
                tags: name
                field: name
                keepOpen: true
                allowNew: true
                autocomplete: true
                allowDuplicates: false
                values:
                    handler: return context.list
    workbenches:
        type: array
        fields:
            name:
                label: Name
                tooltip: Workbench Name
                type: text
            code:
                label: Code
                tooltip: Workbench Code
                type: text
            description:
                label: Description
                tooltip: Workbench Description
                type: textarea
            status:
                label: Status
                tooltip: Workbench Status
                component: AngelosRadioButton
                values:
                    - label: Enabled
                      value: ENABLED
                    - label: Disabled
                      value: DISABLED
            consoles:
                label: Consoles
                tooltip: Workbench Consoles
                component: AngelosCheckbox
                values:
                    - label: Accounts
                      value: Accounts
                    - label: Rewards
                      value: Rewards
                    - label: Dispute
                      value: Dispute
form:
    sections:
        - title: Basic Details
          sections:
              - direction: vertical
                fields:
                    - key: name
                    - key: description
                    - key: multi
              - title: Ledger Details
                attributes:
                    bordered: false
                direction: horizontal
                sections:
                    - columns: 12
                      arrayField: ledgerDetails
                      direction: horizontal
                      attributes:
                          bordered: false
                      fields:
                          - key: glName
                            colspan: 3
                          - key: glAccount
                            colspan: 3
                          - key: accountingType
                            colspan: 3
                          - key: tags
                            colspan: 3
        - title: Workbench
          arrayField: workbenches
          subtitle: Expand to edit and modify the configurations
          attributes:
              variant: boxed
              keepExpanded: multiple
              sectionTitleTransformer: console.log("hello"); return data.name + ' - ' + data.code
              addSectionSelection:
                  component: AngelosSelect
                  uniqueKey: name
                  defaultSelectedValue: Add Workbench
                  values:
                      handler: 'return [{label: "Accounts Workbench", value: {name: "Accounts
                          Label", code: "WB008"}}, {label: "Rewards Workbench", value: {name:
                          "Rewards Label", code: "WB008"}}]'
          accordionItemAttributes:
              handler: 'console.log("hello"); return (index ? {disabled: true}: {})'
          sections:
              - fields:
                    - key: name
                      colspan: 6
                    - key: description
                      colspan: 6
                direction: horizontal
              - fields:
                    - key: consoles
                      colspan: 6
                    - key: status
                      colspan: 6
                direction: horizontal
