---
- type: prefill
  refId: data2
  dataEndpointId: test
  httpRequest:
      url: "<%= params.testurl %>/get-data"
      method: GET
      bodySize: -1
      headersSize: -1
      httpVersion: HTTP/1.1
      queryString: []
  inputParams: []
  paginationParams: {}
  responseTransformer: const obj = res; obj.sample = 'Hello World'; return obj;
- type: prefill
  refId: data3
  dataEndpointId: test
  httpRequest:
      url: 'http://localhost:4000/get-data-3'
      method: GET
      bodySize: -1
      headersSize: -1
      httpVersion: HTTP/1.1
      queryString: []
  inputParams: []
  paginationParams: {}
  ignoreFailure: true
  responseTransformer: const obj = res; obj.sample = 'Hello World'; return obj;

- httpRequest:
      url: 'http://localhost:4000/post-data'
      method: POST
      postData:
          mimeType: application/json
          params: []
          text: <%= JSON.stringify(formModel) %>
