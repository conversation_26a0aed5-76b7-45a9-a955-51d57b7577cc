# version: v2
# fields:
#   text: 
#     label: Text
#     component: AngelosInput
#     value:
#       handler: return 'yeahh'
#   test:
#     label: test
#     component: AngelosSwitch
#   pseudoLedger: 
#     component: AngelosSelect
#     test1:
#       handler: return console.log('test select')
#     values: 
#       handler: return context.pseudoLedgerList
#     condition:
#       visibleIf: 
#         "===": 
#           - var: formModel.test
#           - true

#   postingCategories: 
#     type: array
#     # test2: 
#     #   handler: return console.log('test array')
#     value: 
#       handler: return context.someKey.find(item => item.pseudoLedger === formModel.pseudoLedger)?.postingCategories || []
#       dependencies: 
#         - pseudoLedger
#     condition:
#       visibleIf: 
#         "!==": 
#           - var: formModel.pseudoLedger
#           - PseudoLedger 1
#     fields: 
#       balanceType:
#         label: Balance Type
#         component: AngelosRadioButton
#         values: 
#           - label: Net Balance
#             value: NET_BALANCE
#           - label: Split Balance
#             value: SPLIT_BALANCE
#       debit.glName:
#         label: Debit GL Name
#         component: AngelosSelect
#         values:
#           handler: return context.glNameList
#       debit.attributes:
#         label: Debit Attributes
#         component: AngelosCodeEditor
#       credit.glName:
#         label: Credit GL Name
#         component: AngelosSelect
#         values:
#           handler: return context.glNameList
#         # test:
#         #   handler: return console.log('data', data)
#         isVisible:
#           handler: return data.balanceType === 'SPLIT_BALANCE'
#           # handler: return formModel.test === true
#   #       # condition:
#   #       #   visibleIf: 
#   #       #     "===": 
#   #       #       - var: data.balanceType
#   #       #       - SPLIT_BALANCE
#       credit.attributes:
#         label: Credit Attributes
#         component: AngelosCodeEditor
#         # condition:
#         #   visibleIf: 
#         #     "===": 
#         #       - var: data.balanceType
#         #       - SPLIT_BALANCE
# form:
#   sections: 
#     - title: XYZ
#       sections: 
#         - title: pseudoLedger
#           direction: horizontal
#           fields: 
#             - key: text
#               colspan: 4
#             - key: test
#               colspan: 4
#             - key: pseudoLedger
#               colspan: 4
#         - title: Posting Categories
#           arrayField: postingCategories
#           fields: 
#             - key: balanceType
#               colspan: 12
#             - key: debit.glName
#               colspan: 6
#             - key: debit.attributes
#               colspan: 6
#             - key: credit.glName
#               colspan: 6
#             - key: credit.attributes
#               colspan: 6
#           # condition:
#           #   visibleIf:
#           #     "!!": 
#           #       - var: formModel.pseudoLedger



version: v2
fields:
  field1: 
    label: field 1
    component: AngelosInput
    rules:
      required: true
  field2: 
    label: field 2
    component: AngelosCheckbox
    values: 
      - label: Test
        value: test
      - label: Test2
        value: test2
    rules:
      required: true
  field3: 
    label: field 3
    component: AngelosSelect
    values: 
      - label: Test
        value: test
      - label: Test2
        value: test2
    rules:
      required: true
  field4: 
    label: field 4
    component: AngelosRadioButton
    values: 
      - label: Test
        value: test
      - label: Test2
        value: test2
    rules:
      required: true
  field5: 
    label: field 5
    component: AngelosComboBox
    values: 
      - label: Test
        value: test
      - label: Test2
        value: test2
    rules:
      required: true
  field6: 
    label: field 6
    component: AngelosCodeEditor
    rules:
      required: true
  field7: 
    label: field 7
    component: AngelosDatePicker
    rules:
      required: true
  field8: 
    label: field 8
    component: AngelosFileUpload
    rules:
      required: true
  field9: 
    label: field 9
    component: AngelosSwitch
    rules:
      required: true
  field10: 
    label: field 10
    component: AngelosTagInput
    rules:
      required: true
  field11: 
    label: field 11
    component: AngelosTextArea
    rules:
      required: true
form:
  sections: 
    - title: XYZ
      sections: 
        - title: pseudoLedger
          direction: horizontal
          fields: 
            - key: field1
              colspan: 4
            - key: field2
              colspan: 4
            - key: field3
              colspan: 4
            - key: field4
              colspan: 4
            - key: field5
              colspan: 4
            - key: field6
              colspan: 4
            - key: field7
              colspan: 4
            - key: field8
              colspan: 4
            - key: field9
              colspan: 4
            - key: field10
              colspan: 4
            - key: field11
              colspan: 4
