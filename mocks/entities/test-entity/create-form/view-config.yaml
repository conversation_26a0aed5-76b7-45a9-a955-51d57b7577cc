# ---
# version: v2
# fields:
#     select: 
#       label: Select Field
#       component: AngelosSelect
#       disabled: false
#       tooltip: 'This is a tooltip'
#       indicatorType: mandatory
#       description: 'This is a description'
#       size: 'small'
#       maxTagCount: responsive
#       multiSelect: false
#       value: reasonCode2
#       placement: 'top' 
#       values: 
#         - label: 'Reason Code 1'
#           value: 'reasonCode1'
#           class: "custom-class"
#         - label: 'Reason Code  This is not the description for Reason Code 2'
#           value: 'reasonCode2'
#           description: 'This is the description for Reason Code 2'
#         - label: 'Reason Code 3'
#           value: 'reasonCode3'
#         - label: 'Reason Code 4'
#           value: 'reasonCode4'
#         - label: 'Reason Code 5'
#           value: 'reasonCode5'
#       rules:
#         required: 
#           value: true
#           message: 'Please select a value'
#     code:
#         name: Code
#         label: Code editor
#         component: AngelosCodeEditor
#         theme: vs-dark
#         value: |-
#             function test(){ 
#               console.log('hello world')
#             }
#     gender:
#         name: Gender
#         component: AngelosRadioButton
#         label: Gender
#         values:
#             - label: Male
#               value: Male
#             - label: Female
#               value: Female
#     fruits:
#         name: Fruits
#         component: AngelosCheckbox
#         label: Fruits
#         values:
#             - label: Orange
#               value: Orange
#             - label: Mango
#               value: Mango
#     remarks:
#         name: Remarks
#         type: text
#         label: Add Remarks
#         rules:
#             required: true
#         maxlength: 140
#         placeholder: Add Remarks
#         indicatorType: mandatory
#     reasonCode:
#         name: Reason Code
#         label: Reason Code
#         rules:
#             required: true
#         placeholder: Select Reason Code
#         indicatorType: mandatory
#     array:
#       type: array
#       fields: 
#         test: 
#           label: Test
# form:
#     sections:
#         - arrayField: array
#           direction: vertical
#           title: Parameters
#           subtitle: Array Field description
#           fields:
#               - field_key: test
#         - direction: vertical
#           fields:
#               # - field_key: reasonCode
#               #   colspan: 6
#               - field_key: remarks
#               # - field_key: code
#               # - key: gender
#               - key: select
#               - key: fruits

#     buttons: 
#         - action: submit
#           text: Submit
#         - action: cancel
#           text: Cancel



version: v2
fields:
  boolean: 
    component: AngelosSwitch
  users:
    type: array
    fields: 
      firstName:
        label: First Name
        # type: number
        rules:
          required: true
      middleName:
        label: Middle Name
      # lastName: 
      #   label: Last Name
      #   rules:
      #     required: true
      # age:
      #   label: Age
      #   type: number
      #   rules:
      #     required: true
      sex: 
        label: Gender
        component: AngelosRadioButton
        values: 
          - label: Male
            value: male
          - label: Female
            value: female
          - label: Other
            value: other
        rules:
          required: true
      # emailIds:
      #   label: Emails
      #   multiple: true
      #   type: email
      # phoneNumber:
      #   label: Phone Number
      #   type: number
      #   rules:
      #     required: true
      # phoneNumber2:
      #   label: Alternate Phone Number
      #   type: number
      contactInfo: 
        type: array
        fields: 
          contactType: 
            label: Type
            component: AngelosSelect
            indicatorType: mandatory
            values:
              - label: LinkedIn
                value: linkedin
              - label: GitHub
                value: github
              - label: Portfolio Link
                value: portfolio
            rules:
              required: true
          contactLink: 
            label: Link
            rules:
              required: true
form:
    sections:
        - title: User Details
          direction: vertical
          fields:
            - key: boolean
        - title: Users
          arrayField: users
          sections: 
            - title: Name
              direction: horizontal
              condition:
                visibleIf: 
                  "===":
                    - var: formModel.boolean
                    - true
              fields: 
                - key: firstName
                  colspan: 4
                - key: middleName
                  colspan: 4
            #     - key: lastName
            #       colspan: 4
            - direction: horizontal
              fields: 
                # - key: age
                #   colspan: 4
                - key: sex
                  colspan: 4
            - title: ContactInfo
              attributes:
                bordered: true
              direction: horizontal
              sections:
            #     - columns: 4
            #       title: EmailIds
            #       attributes:
            #         bordered: true
            #       fields: 
            #         - key: phoneNumber
            #         - key: phoneNumber2
            #         - key: emailIds
                - columns: 8
                  title: Additional Contact Info
                  arrayField: contactInfo
                  direction: horizontal
                  attributes:
                    bordered: true
                  fields: 
                    - key: contactType
                      colspan: 4
                    - key: contactLink
                      colspan: 8


# version: v2
# fields:
#   text: 
#     label: Text
#     component: AngelosInput
#   why:
#     label: Why
#     component: AngelosInput
#     value: 
#       handler: return formModel.text
#   test:
#     label: test
#     component: AngelosSwitch
#     value: true
#   pseudoLedger: 
#     label: Test Label
#     component: AngelosSelect
#     values: 
#       handler: return context.pseudoLedgerList
#     rules:
#       required: true
#   postingCategories: 
#     type: array
#     value: 
#       handler: return context.someKey.find(item => item.pseudoLedger === formModel.pseudoLedger)?.postingCategories || []
#       dependencies: 
#         - pseudoLedger
#     fields: 
#       balanceType:
#         label: Balance Type
#         component: AngelosRadioButton
#         values: 
#           - label: Net Balance
#             value: NET_BALANCE
#           - label: Split Balance
#             value: SPLIT_BALANCE
#       debit.glName:
#         label: Debit GL Name
#         component: AngelosSelect
#         values:
#           handler: return context.glNameList
#       debit.attributes:
#         label: Debit Attributes
#         component: AngelosCodeEditor
#       credit.glName:
#         label: Credit GL Name
#         component: AngelosSelect
#         values:
#           handler: return context.glNameList
#       credit.attributes:
#         label: Credit Attributes
#         component: AngelosCodeEditor
# form:
#   sections: 
#     - title: XYZ
#       sections: 
#         - title: pseudoLedger
#           direction: horizontal
#           fields: 
#             - key: text
#               colspan: 4
#             - key: why
#               colspan: 4
#             - key: test
#               colspan: 4
#             - key: pseudoLedger
#               colspan: 4
#         - title: Posting Categories
#           arrayField: postingCategories
#           sections: 
#             - fields:
#               - key: balanceType
#                 colspan: 12
#             - direction: horizontal
#               sections:
#               - title: Debit
#                 columns: 6
#                 fields:
#                   - key: debit.glName
#                     colspan: 12
#                   - key: debit.attributes
#                     colspan: 12
#                 condition:
#                   visibleIf: 
#                     "===": 
#                       - var: arrayItemData.balanceType
#                       - NET_BALANCE
#               - columns: 6
#                 title: Credit
#                 fields:
#                   - key: credit.glName
#                     colspan: 12
#                   - key: credit.attributes
#                     colspan: 12
#                 condition:
#                   visibleIf:
#                     "===": 
#                       - var: formModel.test
#                       - true
