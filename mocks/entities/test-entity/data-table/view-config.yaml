uiDataControl: false
paginationConfig:
  page: 1
  pageSize: 10
  showSizePicker: true
  showQuickJumper: false
  pageSizes:
    - 10
    - 20
    - 50
    - 100
tableConfig:
  columns:
    - field: selection
      type: selection
      multiple: false
      disabledHandler: |-
          console.log('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> called with:', { row, context, params });
          if(row.entity_id === '10002' || row.entity_id === '1011') {
              return true;
          }
          return false;
      
      selectionHandler: |-
        // IMPORTANT: Use stable row properties (not UUID) since UUID changes on page reload
        // Examples of stable properties: id, product_code, email, etc.
        // Examples of unstable properties: _uuid, timestamp, etc.

        // Debug logging
        // console.log('<PERSON><PERSON><PERSON><PERSON> called with:', { row, context, params });

        // Handle multiple preselected product codes from context
        // Check if preselectedProductCodes array exists and contains the current row's product_code
        const preselectedCodes = ['10'];
        const shouldSelect = preselectedCodes &&
                           Array.isArray(preselectedCodes) &&
                           preselectedCodes.includes(row.entity_id);

        // console.log(`Row ${row.product_code} should be selected:`, shouldSelect);
        // console.log('Context preselectedProductCodes:', preselectedCodes);

        return shouldSelect;

        // Alternative examples:
        // return row.status === 'active' || row.apl_inclusion === 'I';  // Multiple conditions
        // return row.id === 'specific-id';  // Using stable ID
        // return params.preselectedIds && params.preselectedIds.includes(row.id);  // Using params
      condition:
        visibleIf:
          "===":
            - var: params.featureFlags.selection
            - true
      width: 50px
    - field: entity_id
      label: ID
      width: 100px
    - field: name
      label: Name
      width: 200px
    - field: description
      label: Description
      width: 300px
      render: |-
        return h('span', {
          class: 'description-column',
        }, row.description);
    - field: entity_type
      label: Type
      width: 200px
    - field: status
      label: Status
      width: 150px
      render: |-
        console.log('PArams', params);
        const status = row.status?.toLowerCase();
        return h(ZTag, {
          bordered: false,
          class: `status-tag status-tag--${status}`,
        }, [
          h('span', {
            class: `status-dot status-dot--${status}`,
          }),
          h(ZText, {
            transform: 'capitalize',
            class: `status-text--${status}`,
          }, row.status?.toLowerCase()),
        ]);

filterConfig:
  - field: entity_id
    fixed: true
    label: ID
    selectors:
      "by-input":
        placeholder: Enter text
        searchable: true
        multiple: false
  # - field: entity_type
  #   fixed: true
  #   label: Type
  #   selectors:
  #     'by-input':
  #       placeholder: Enter text
  #       searchable: true
  #       multiple: false
  - field: status
    label: Status
    fixed: true
    selectors:
      "by-value":
        multiple: false
        searchable: false
        show-indicator: true
        options:
          - label: Active
            value: ACTIVE
          - label: Inactive
            value: INACTIVE
css: |-
  .z-data-table {
    font-size: 14px;
  }
  .status-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
  }
  .status-tag--inactive {
    background-color:#FFF1E0;
  }
  .status-tag--active {
    background-color:#E4F7E7;
  }
  .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }
  .status-dot--active {
    background-color: #5FD174;
  }
  .status-text--active {
    color: #07841D;
  }
  .status-text--inactive {
    color : #9F3F00;
  }
  .status-dot--inactive {
    background-color: #FF8B3D;
  }
  .topbar {
    gap: 0 !important;
  }
  .z-data-table .z-data-table-th  {
    background-color: #fff;
  }
  .description-column {
    display:inline-block;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
