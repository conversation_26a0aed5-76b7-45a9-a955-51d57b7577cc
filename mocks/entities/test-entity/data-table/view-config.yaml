version: v2
isFrontEndFilter: false
uiDataControl: false
paginationConfig:
  page: 1
  pageSize: 4
  showSizePicker: true
  showQuickJumper: false
  pageSizes:
    - 2
    - 3
    - 4
    - 10
tableConfig:
  columns:
    - field: selection
      label: Select
      type: selection
      multiple: true
      width: 50px
      selectionHandler: |-
        const preselectedCodes = context.preselectedProductCodes;
        const shouldSelect = preselectedCodes && 
                           Array.isArray(preselectedCodes) && 
                           preselectedCodes.includes(row.product_code);
        return shouldSelect;
      condition:
        visibleIf:
          "===":
            - var: params.featureFlags.selection
            - true
    - field: product_code
      label: UPC/PLU
      width: 200px
      pinned: left
    
    - field: product_name
      label: Name
      width: 200px
    - field: product_tag_code
      label: Optum Category Code
      width: 200px
    - field: apl_id
      label: APL ID
      width: 200px
      condition:
        visibleIf:
          # Example: Only show if 'params.featureFlags.showAplId' is true
          # 'params' here refers to the 'inputParams.params' object
          "===":
            - var: params.featureFlags.showAplId
            - true
    - field: retailer_id
      label: Merchant ID
      width: 200px
    - field: apl_inclusion
      label: APL Inclusion
      width: 200px
    - field: status
      label: Status
      width: 150px
      pinned: right
      render: |-
        const status = row.status?.toLowerCase();
        return h(ZTag, {
          bordered: false,
          class: `status-tag status-tag--${status}`,
        }, [
          h('span', {
            class: `status-dot status-dot--${status}`,
          }),
          h(ZText, {
            transform: 'capitalize',
            class: `status-text--${status}`,
          }, row.status?.toLowerCase()),
        ]);

filterConfig:
  - field: product_code
    fixed: true
    label: UPC/PLU
    selectors:
      'by-input':
        placeholder: Enter text
        searchable: true
        multiple: false
  - field: apl_id
    fixed: true
    label: APL ID
    selectors:
      'by-input':
        placeholder: Enter text
        searchable: true
        multiple: false
  - field: retailer_id
    fixed: true
    label: Merchant ID
    selectors:
      'by-input':
        placeholder: Enter text
        searchable: true
        multiple: false
  - field: apl_inclusion
    label: APL Inclusion
    fixed: true
    selectors:
      'by-value':
        multiple: true
        searchable: true
        show-indicator: true
        options:
          - label: Included
            value: I
          - label: Excluded
            value: E
css: |-
    .z-data-table {
      font-size: 14px;
    }
    .status-tag {
      display: inline-flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 4px;
    }
    .status-tag--inactive {
      background-color:#FFF1E0;
    }
    .status-tag--active {
      background-color:#E4F7E7;
    }
    .status-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }
    .status-dot--active {
      background-color: #5FD174;
    }
    .status-text--active {
      color: #07841D;
    }
    .status-text--inactive {
      color : #9F3F00;
    }
    .status-dot--inactive {
      background-color: #FF8B3D;
    }
    
# version: v2
# title: 'All Workers'
# description: 'This is the worker list'
# # isTopbar: true
# isFrontEndFilter: true
# # singleSelect: false
# # multiSelect: false
# # stickyHeader: true
# tableConfig:
#     sortIcon: arrow-up
#     columns:
#         - field: phase
#           label: PHASE¯
#           centered: false
#           sortable: false
#           width: 80px
#           component:
#               template: <span :class="`phase--${$attrs.data.row.status.toLowerCase()}`">{{$attrs.data.row.phase}}</span>
#         - field: workerName
#           label: WORKER
#           centered: false
#           sortable: false
#           width: 280px
#           render: return h('a',{ href:'#',onClick:(event)=>{ event.target.dispatchEvent(new CustomEvent('worker-row-clicked',{bubbles:true,composed:true,detail:row})) }},row.workerName)
#         - field: periodicity
#           label: PERIODICITY
#           centered: false
#           sortable: false
#           width: 150px
#         - field: startTime
#           label: START TIME
#           render: const dateOptions={day:"numeric",month:"short",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:true};const date = new Date(row.startTime).toLocaleDateString(context.locale, dateOptions); return h('span',{},date)
#           centered: false
#           sortable: true
#           width: 225px
#         - field: endTime
#           label: END TIME
#           render: const dateOptions={day:"numeric",month:"short",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:true};const date = new Date(row.updatedTime).toLocaleDateString(context.locale, dateOptions); return h('span',{},date)
#           centered: false
#           sortable: false
#           width: 225px
#         - field: totalEntityCount
#           label: TOTAL LEDGERS
#           sortable: false
#           width: 200px
#           numeric: true
#         - field: successfulEntityCount
#           label: PASSED LEDGERS
#           centered: false
#           sortable: false
#           width: 200px
#           numeric: true
#         - field: failedEntityCount
#           label: FAILED LEDGERS
#           centered: false
#           sortable: false
#           width: 200px
#           numeric: true
#         - label: STATUS
#           field: status
#           width: 150px
#           pinned: right
#           render: return h(ZTag,{bordered:false},()=>[h(ZIcon,{size:10},()=>h(icons.ChangeCircle)),h('span',null,row.status)])
#     detailed: false
#     ShowDetailIcon: true
#     hoverable: true
#     striped: true
#     checkable: false
#     stickyColumnActions: false
#     stickyFirstColumn: false
# paginationConfig:
#     page: 1
#     pageSize: 5
#     showSizePicker: true
#     pageSizes: [5, 10, 20]
# tableActions:
#     - label: Test Is the name
#       value: test
#       icon: AddFilled
#       callback: return console.log('Handler  was called on the config',tenantId, context);
#     - label: Test1
#       value: test1
#       disabled: true
# filterConfig:
#     - field: phase
#       fixed: true
#       selectors:
#           'by-value':
#               multiple: true
#               searchable: true
#               show-indicator: true
#               options:
#                   - label: BOPI
#                     value: BOPI
#                   - label: BOFI
#                     value: BOFI
#                   - label: EOPI
#                     value: EOPI
#                   - label: EOFI
#                     value: EOFI
#                   - label: EOP
#                     value: EOP
#       label: Phase
#     - field: workerName
#       label: Worker
#       fixed: true
#       multiple: true
#       searchable: true
#       selectors:
#           'by-value':
#               multiple: true
#               searchable: true
#               actions:
#                   - 'apply'
#                   - 'clear'
#                   - 'reset'
