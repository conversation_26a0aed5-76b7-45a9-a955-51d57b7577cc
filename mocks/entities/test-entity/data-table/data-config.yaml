refId: data1
httpRequest:
  url: https://atlas-showroom.internal.mum1-pp.zetaapps.in/intendant/v1/tenants/163030/productCatalogueConfigs/categories
  method: POST
  bodySize: -1
  headersSize: -1
  httpVersion: HTTP/1.1
requestTransformer: 
  headers: |-
    return [
      { name: 'Authorization', value: `Bearer ${authToken}` },
    ];
  body: |-
    // we need to add pass filter and cursor information here
    const body = {
      filters: [],
      cursor: ""
    };
    // Filter
    if(filter && Object.keys(filter)) {
      const keys = Object.keys(filter);
      const filterConfig = [];
      if(keys.length) {
        keys.forEach((key) => {
          if(Array.isArray(filter[key].value)) {
            filter[key].value.forEach((value)=>{
                filterConfig.push({
                  key: key,
                  value: value,
                });
            })
          }else {
            filterConfig.push({
              key: key,
              value: filter[key].value,
            });
          }
        });
        console.log(filterConfig);
      }
      body.filters = filterConfig;
    }
    // cursor
    if(data) {
       const length = Object.keys(data).length;
       const lastPage = data[length]
       if(lastPage) body['cursor'] = lastPage.cursor 
    }
    return body;
  query: |-
    if(pagination){
      return {
        pageSize: pagination.pageSize,
      }
    }
    return {
      pageSize: 10,
    }

responseTransformer: |-
  const response = {
    ...res
  }
  if(Array.isArray(response.data) && response.data.length)  {
    response.data = response.data.map((item) => {
      return {
        ...item,
        apl_inclusion: item.apl_inclusion === 'I' ? 'Included' : item.apl_inclusion === 'E' ? 'Excluded' : '-',
      }
    });
  }
  if(response.cursor) {
    response.hasNext = true;
  }else {
    response.hasNext = false;
  }
  return response;
