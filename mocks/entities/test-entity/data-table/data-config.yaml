refId: data1
httpRequest:
  url:  'https://atlas-showroom.internal.mum1-pp.zetaapps.in/intendant/v1/tenants/163030/productCatalogueConfigs/products'
  method: POST
  bodySize: -1
  headersSize: -1
  httpVersion: HTTP/1.1
requestTransformer: 
  headers: |-
    return [
      { name: 'Authorization', value: `Bearer ${authToken}` },
    ];
  body: |-
    // we need to add pass filter and cursor information here
    const body = {
      filters: [],
      cursor: ""
    };
    // Filter
    if(filter && Object.keys(filter)) {
      const keys = Object.keys(filter);
      const filterConfig = [];
      if(keys.length) {
        keys.forEach((key) => {
          if(Array.isArray(filter[key].value)) {
            filter[key].value.forEach((value)=>{
                filterConfig.push({
                  key: key,
                  value: value,
                });
            })
          }else {
            filterConfig.push({
              key: key,
              value: filter[key].value,
            });
          }
        });
        console.log(filterConfig);
      }
      body.filters = filterConfig;
    }
    // cursor
    if(data) {
       const length = Object.keys(data).length;
       const lastPage = data[length]
       if(lastPage) body['cursor'] = lastPage.cursor 
    }
    return body;
  query: |-
    if(pagination){
      return {
        pageSize: pagination.pageSize,
      }
    }
    return {
      pageSize: 10,
    }

responseTransformer: |-
  const response = {
    ...res
  }
  if(Array.isArray(response.data) && response.data.length)  {
    response.data = response.data.map((item) => {
      return {
        ...item,
        aplInclusion: item.aplInclusion === 'I' ? 'Included' : item.aplInclusion === 'E' ? 'Excluded' : '-',
      }
    });
  }
  if(response.cursor) {
    response.hasNext = true;
  }else {
    response.hasNext = false;
  }
  return response;
# responseTransformer: |-
#   const statusMap = {
#     "ENABLED": {
#       value: "Active",
#       color: "success"
#     },
#     "DISABLED": {
#       value: "Inactive",
#       color: "warning"
#     },
#     "DELETED": {
#       value: "Terminated",
#       color: "error"
#     }
#   }
#   const data = [];
#   try {
#     res.hits.forEach(hit => {
#       const merchantID = hit.issuerBusinesses[0].vectors.find(vector => vector.type === 'affiliateID')?.value;
#       const businessGroupId = hit.issuerBusinesses[0].entityAttributes.find(attribute => attribute.key === 'groupId')?.primitiveValue;
#       const status = statusMap[hit.issuerBusinesses[0]?.state] || '-';
#     data.push({
#       businessID: hit.businessID,
#       businessName: hit.businessName,
#       businessGroupId: businessGroupId || '-',
#       merchantAggregator: hit.issuerBusinesses[0].entityAttributes[0]?.merchantAggregator || '-',
#       merchantID: merchantID || '-',
#       status: status,
#       });
#     });
#   } catch (error) {
#     console.log(error);
#   }
#   console.log(data);
#   return {data, total: res.totalCount};
cacheBurstTime: 0
# refId: eod-center-workers-list
# httpRequest:
#     url: '<%= params.ATALANTA_BASE_URL %>/orchestra/api/v1/tenants/<%= params.tenantId %>/coas/<%= params.coaId %>/workers-list?startPeriodId=<%= params.startPeriodId %>&endPeriodId=<%= params.endPeriodId %>&includeWorkerRuns=true&orchestraUrl=<%= params.orchestraUrl %>'
#     method: GET
#     bodySize: -1
#     headersSize: -1
#     httpVersion: HTTP/1.1
#     headers:
#         - name: 'Authorization'
#           value: 'Bearer '
# inputParams: []
# apiAdopter: financeCenterAPI
# cacheBurstTime: 0
# paginationParams: {}
# responseTransformer: |-
#   return (function (workerList) {
#     return workerList.map((worker)=>{
#       return {
#         ...worker,
#         phase: worker.phase === 'CLOSED'? 'EOP': worker.phase,
#       }
#     })
#   })(res)
