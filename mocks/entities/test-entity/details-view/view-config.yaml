# sections:
#   - label: Basic info #Mapped to 'key' in gds, and 'type' will be set as 'header'
#     key: first
#     # type: 'none'
#     attributes: #Mapped to 'props' in gds ViewContent
#       hasBottomDivider: true
#     tooltip: Information about section
#     fields:
#       - field: somekeywhichdoesnotexist #Unique identifier, angel<PERSON> can use to fill data
#         label: context.something #Mapped to 'key' in gds ViewContent
#         value: context.something #Access to context through 'context' key
#         tooltip: "some information"
#       - section:
#           label: context.something
#           key: section1.0
#           accordionProps: #Mapped to 'accordianProps' in gds 
#             keepExpanded: single
#             variant: boxed
#             disabled: false
#           attributes: #Mapped to 'props' in gds
#             hasBottomDivider: true 
#           fields:
#             - field: somekey3
#               label: context.something #Mapped to 'key' in gds ViewContent
#               value: some value
#               type: link #Default value is 'text'
#               visible: context.hide
#               tooltip: "some information about this field"
#             - field: somekey4
#               label: field3 #Mapped to 'key' in gds ViewContent
#               value: some value
#             - field: dynamic2
#               label: Dynamic Template
#               type: template
#               value: <div class="custom-class"> <span><z-icon icon="search" /> Custom contents {{ data1.first.something }} , {{ context.something }} </span> </div>
#       - field: somekey2 #Unique identifier, angelos can use to fill data
#         label: Status #Mapped to 'key' in gds ViewContent
#         type: status
#         attributes:
#           label: 
#             value: data2.status.label #If an attribute property needs to be taken from api, add a value attribute with binding from the data
#           type: 
#             value: data2.status.type
#       - section:
#           key: section2
#           label: Accordion2
#           accordionProps: #Mapped to 'accordianProps' in gds 
#             keepExpanded: single
#             variant: boxed
#             disabled: false
#           fields:
#             - field: somekey5
#               label: field5 #Mapped to 'key' in gds ViewContent
#               value: some value # section2.someKey5
#             - section:
#                 key: section3
#                 label: Accordion3
#                 accordionProps: #Mapped to 'accordianProps' in gds 
#                   keepExpanded: single
#                   variant: boxed
#                   disabled: false
#                 fields:
#                   - field: somekey6
#                     label: field6 #Mapped to 'key' in gds ViewContent
#                     value: some value # section2.section3.someKey6
#                   - field: somekey7
#                     label: field7 #Mapped to 'key' in gds ViewContent
#                     value: Abhi # section2.section3.someKey7
#                     type: avatar
#                     attributes:
#                       src: 
#                         value: data2.url
#                   - field: somenonexistentkey
#                     label: Json test
#                     type: json
#                     value: data1.first.somekey9
#       - field: somekey9
#         label: Json
#         type: json 
#       - field: somekey10
#         label: Array
#         type: array
#         value: data2.somekey10
#         attributes: 
#           variant: badge
#       - field: dynamic
#         label: Dynamic Template
#         type: template
#         value: |-
#           <div class="custom-class"> <span><z-icon icon="search" /> Custom contents {{ context.something }} </span> </div>
#         visible: true #Boolean value, can be passed through context or from api response, default is true
# header: # For Adding ZUtilityHeader of gds, props are directly bind to this header
#   title: 
#     value: context.something
#   description: Show your view content using AngelosDetailsView
#   type: colored # colored | neutral | colorless
#   paddingless: false
#   size: medium
#   isBordered: false
# container:
#   isShadowed: true
# heightOverflowType: showMore #Maps to 'heightOverflowType' prop for GDS ViewContent, by default 'none' is set
# showMoreHeightThreshold: 400 #Maps to 'showMoreHeightThreshold' prop for GDS ViewContent, by default 500 is set

# Simple section with two fields
# sections:
#     - isJson: true #Free flow json section
#       data: context.free
#       type: accordion
#       labelTransformer: |- 
#           return `MyLabel_${index}`;
#     - label: Section 2
#       fields:
#         - field: name
#           label: Name
#           value: Some Name
#         - field: phone
#           label: Phone 
#           value: 843940
#         - section: #Free flow json section
#               label: Nested free json
#               isJson: true
#               data: context.free
#               labelTransformer: |- 
#                 return `MyNestedLabel_${index}`;
# header:
#     title: test
#     description: yayy


# views: 
#   - sections:
#         data: context.tableData
#         tableConfig:
#           columns:
#             - field: category
#               label: Category
#             - field: limitType
#               label: Limit type
#             - field: limitValue 
#               label: Limit Value
#               template: <span> INR {{data.column}}</span>
#   - sections:
#     - isJson: true #Free flow json section
#       data: context.free
#       type: accordion
#       labelTransformer: |- 
#           return `MyLabel_${index}`;
#     - label: Section 2
#       fields:
#         - field: name
#           label: Name
#           tooltip: test
#           value: context.name
#         - field: phone
#           label: Phone
#           value: context.phone
#         - section: #Free flow json section
#               label: Nested free json
#               isJson: true
#               data: context.free
#               labelTransformer: |- 
#                 return `MyNestedLabel_${index}`;
#     container:
#       isBordered: false
#   - sections:
#     - isJson: true #Free flow json section
#       data: context.free
#       type: accordion
#       labelTransformer: |- 
#           return `MyLabel_${index}`;
#     - label: Section 2
#       fields:
#         - field: name
#           label: Name
#           value: Some Name
#         - field: phone
#           label: Phone 
#           value: 843940
#         - section: #Free flow json section
#               label: Nested free json
#               isJson: true
#               data: context.free
#               labelTransformer: |- 
#                 return `MyNestedLabel_${index}`;
#   - sections:
#     - isJson: true #Free flow json section
#       data: context.free
#       type: accordion
#       labelTransformer: |- 
#           return `MyLabel_${index}`;
#     - label: Section 2
#       fields:
#         - field: name
#           label: Name
#           value: Some Name
#         - field: phone
#           label: Phone 
#           value: 843940
#         - section: #Free flow json section
#               label: Nested free json
#               isJson: true
#               data: context.free
#               labelTransformer: |- 
#                 return `MyNestedLabel_${index}`;
#   - sections:
#         data: context.tableData
#         tableConfig:
#           columns:
#             - field: category
#               label: Category
#             - field: limitType
#               label: Limit type
#             - field: limitValue 
#               label: Limit Value
#               template: <span> INR {{data.column}}</span>
#         isPaginated: true
#         paginationConfig:
#           currentPage: 1
#           currentPerPage: 5
#           perPageItems: [5,10,25]
#     container:
#       isBordered: false

# views:
#   - sections: 
#       type: tab
#       items: 
#         - label: Tab 1
#           sections:
#             - label: Overview
#               fields:
#                 - field: templateCode
#                   label: Template Code
#                   tooltip: Template Code
#                   value: context.templateCode
#                   defaultValue: "-"
#                 - field: code
#                   label: Code
#                   tooltip: Code
#                   value: context.code
#                   defaultValue: "-"
#                 - section:
#                     label: Nested section
#                     fields: 
#                       - field: address
#                         label: address
#                       - section: 
#                           label: Nested section
#                           fields:
#                             - field: city
#                               label: city
#                             - field: state
#                               label: state
#                             - field: country
#                               label: country
#           tab: 
#             label: Tab1

      

# - sections:
#   - label: Overview
#     fields:
#       - field: templateCode
#         label: Template Code
#         tooltip: Template Code
#         value: ["a", "b", "c", "d"]
#         defaultValue: "-"
#       - field: code
#         label: Code
#         tooltip: Code
#         value: context.code
#         defaultValue: "-"
#       - field: status
#         label: Status
#         type: status
#         attributes:
#           label: Active
#           type: warning
#       - field: json
#         label: Json
#         type: json
#         value: '{"key": "value"}'
#       - field: link
#         label: Link
#         type: link
#         attributes:
#           href: "https://www.google.com"
#       - field: somekey
#         label: My template
#         type: template
#         value: |- 
#           <div> <z-badge :value="5" :max="15">
#             <z-avatar />
#           </z-badge> </div>
#       - section:
#           label: Nested section
#           fields: 
#             - field: address
#               label: address
#             - section: 
#                 label: Nested section
#                 fields:
#                   - field: city
#                     label: city
#                   - field: state
#                     label: state
#                   - field: country
#                     label: country
#                   - field: status
#                     label: Status
#                     type: status
#                     attributes:
#                       label: Active
#                       type: success
#                   - field: json
#                     label: Json
#                     type: json
#                     value: '{"key": "value"}'
#                   - field: link
#                     label: Link
#                     type: link
#                     attributes:
#                       href: "https://www.google.com"
#                   - field: somekey
#                     label: My template
#                     type: template
#                     value: |- 
#                       <div> <z-badge :value="5" :max="15">
#                         <z-avatar />
#                       </z-badge> </div>
#   header:
#     title: Header title 
#     description: Header description
#     type: colored
# - sections:
#   - label: Overview
#     fields:
#       - field: templateCode
#         label: Template Code
#         tooltip: Template Code
#         value: context.templateCode
#         defaultValue: "-"
#       - field: code
#         label: Code
#         tooltip: Code
#         value: context.code
#         defaultValue: "-"
#       - section:
#           label: Nested section
#           fields: 
#             - field: address
#               label: address
#             - section: 
#                 label: Nested section
#                 fields:
#                   - field: city
#                     label: city
#                   - field: state
#                     label: state
#                   - field: country
#                     label: country
# - sections:   
#       - isJson: true #Free flow json section
#         data: context.free
#         type: accordion
#         labelTransformer: |- 
#             return `MyLabel_${index}`;
# - sections:
#     - label: Section 2
#       fields:
#         - field: name
#           label: Name
#           value: Some Name
#         - field: phone
#           label: Phone 
#           value: 843940
#         - section: #Free flow json section
#               label: Nested free json
#               isJson: true
#               data: context.free
#               labelTransformer: |- 
#                 return `MyNestedLabel_${index}`;
# - sections:
#     data: context.tableData
#     tableConfig:
#       columns:
#         - field: category
#           label: Category
#         - field: limitType
#           label: Limit type
#           sortable: true
#         - field: limitValue 
#           label: Limit Value
#           template: <span> INR {{data.column}}</span>
#     paginationConfig:
#       currentPage: 1
#       currentPerPage: 4
#       perPageItems: [4,6,8]
#   header:
#     title: Table header
#     description: table description

views:
  - sections: 
      type: tab
      attributes:
        variant: card
      items: 
        - label: Tab 1
          sections:
            - label: Overview
              key: first
              fields:
                - field: templateCode
                  label: Template Code
                  tooltip: Template Code
                  defaultValue: "-"
                - field: code
                  label: Code
                  tooltip: Code
                  # value: context.code
                  defaultValue: "-"
                - section:
                    label: Nested section
                    key: second
                    fields: 
                      - field: address
                        label: address
                      - section: 
                          label: Nested section
                          key: third
                          fields:
                            - field: city
                              label: city
                            - field: state
                              label: state
                            - field: country
                              label: country
          tab: 
            label: Tab1
        - label: Tab 2
          sections:
            data: context.tableData
            tableConfig:
              columns:
                - field: category
                  label: Category
                - field: limitType
                  label: Limit type
                  sortable: true
                - field: limitValue 
                  label: Limit Value
                  template: <span> INR {{data.column}}</span>
            isPaginated: true
            paginationConfig:
              currentPage: 1
              currentPerPage: 4
              perPageItems: [4,6,8]
              removeOnLessData: true
            footerTemplate: <div class="table-footer"><span class="total-key">Total Credit Attributed:</span><span>&nbsp;</span><span class="creditAttributed">{{ context.code }}</span></div>
          header:
            title: Table header
            description: table description
          tab: 
            label: Tab2      
  - sections:
    - label: Overview
      attributes:
        hasBottomDivider: true
      fields:
        - field: templateCode
          label: Template Code
          tooltip: Some Template Code
          value: ["a", "b", "c", "d"]
          defaultValue: "-"
        - field: code
          label: Code
          tooltip: Some Code
          value: context.code
          defaultValue: "-"
        - field: status
          label: Status
          type: status
          attributes:
            label: Active
            type: warning
        - field: json
          label: Json
          type: json
          value: '{"key": "value"}'
        - field: link
          label: Link
          type: link
          attributes:
            href: "https://www.google.com"
        - field: somekey
          label: My template
          type: template
          value: |- 
            <div> 
               <z-icon size="40" color="#0e7a0d">
                <account-balance />
              </z-icon>
            </div>
        - field: postingID
          label: Posting ID
          type: template
          value: <div class="temp-btn"><z-button variant="link" @click="handleClick">{{context.code}}</z-button></div>
        - section:
            label: Nested section
            fields: 
              - field: address
                label: address
                tooltip: Some address
              - field: somekey
                label: My template
                type: template
                value: |- 
                  <div> <z-badge :value="5" :max="15">
                    <z-avatar />
                  </z-badge> </div>
              - section: 
                  label: Nested section
                  fields:
                    - field: city
                      label: city
                    - field: state
                      label: state
                    - field: country
                      label: country
                      tooltip: Some country
                    - field: status
                      label: Status
                      type: status
                      attributes:
                        label: Active
                        type: success
                    - field: json
                      label: Json
                      type: json
                      value: '{"key": "value"}'
                    - field: link
                      label: Link
                      type: link
                      attributes:
                        href: "https://www.google.com"
                    - field: somekey
                      label: My template
                      type: template
                      value: |- 
                        <div> <z-badge :value="10" :max="9">
                          <z-avatar />
                        </z-badge> </div>
    header:
      title: Header title 
      description: Header description
      type: colored
    class: custom-class
  - sections:
    - label: Overview
      type: accordion
      fields:
        - field: templateCode
          label: Template Code
          tooltip: Template Code
          value: context.templateCode
          defaultValue: "-"
        - field: code
          label: Code
          tooltip: Code
          value: context.code
          defaultValue: "-"
        - section:
            label: Nested section
            fields: 
              - field: address
                label: address
              - section: 
                  label: Nested section
                  fields:
                    - field: city
                      label: city
                    - field: state
                      label: state
                    - field: country
                      label: country
  - sections:   
        - isJson: true #Free flow json section
          data: context.free
          type: accordion
          labelTransformer: |- 
              return `MyLabel_${index}`;
  - sections:
      - label: Section 2
        fields:
          - field: name
            label: Name
            value: Some Name
            condition: 
              visibleIf:
                "==":
                  - var: context.showField
                  - true
          - field: phone
            label: Phone 
            value: 843940
          - section: #Free flow json section
                label: Nested free json
                isJson: true
                data: context.free
                labelTransformer: |- 
                  return `MyNestedLabel_${index}`;
  - sections:
      data: context.tableData
      condition: 
        visibleIf:
          "==":
            - var: prefillData.data2.showTable
            - true
      tableConfig:
        columns:
          - field: category
            label: Category
          - field: limitType
            label: Limit type
            sortable: true
          - field: limitValue 
            label: Limit Value
            template: <span> INR {{data.column}}</span>
      isPaginated: true
      paginationConfig:
        currentPage: 1
        currentPerPage: 4
        perPageItems: [4,6,8]
        removeOnLessData: true
    header:
      title: Table header
      description: table description
        
#Sample config with data-table and view-content
# views: 
#   - sections:
#         data: context.tableData
#         tableConfig:
#           columns:
#             - field: category
#               label: Category
#             - field: limitType
#               label: Limit type
#             - field: limitValue 
#               label: Limit Value
#               template: <span> INR {{data.column}}</span>
#   - sections:
#       - label: View content section
#         key: first
#         attributes:
#           hasBottomDivider: true
#         fields:
#           - field: name
#             label: name
#             tooltip: This is name
#           - field: phone
#             label: phone
#             tooltip: This is phone number
 
 
 


# Two sections with data binding through key, data reference and hard coding
# sections:
#  - label: Section 1
#    key: first
#    type: accordion
#    attributes:
#     hasBottomDivider: true
#    fields:
#     - field: name
#       label: name
#       tooltip: This is name
#     - field: phone
#       label: phone
#       tooltip: This is phone number
#  - label: Section 2
#    key: second
#    type: accordion
#    fields:
#     - field: name
#       label: name
#       value: Hardcoded name
#       tooltip: This is name
#     - field: phone
#       label: phone
#       tooltip: This is phone number
#       value: data1.first.info.phone

# Nested sections
# sections:
#   - label: Section 1
#     key: first
#     attributes:
#       hasBottomDivider: true
#     fields:
#       - field: name
#         label: name
#       - section:
#           key: info
#           label: More information
#           fields:
#             - field: phone
#               label: Phone
#             - section:
#                 key: extra
#                 label: Extra information
#                 fields:
#                   - field: address
#                     label: Address
#                   - section: 
#                       key: superextra
#                       label: Super extra information
#                       fields:
#                         - field: hobbies
#                           label: Hobbies
#                           type: array
#                           attributes:
#                             variant: badge
#                         - section:
#                             key: nested1
#                             label: Information
#                             fields:
#                               - field: info1
#                                 label: key
#                                 value: value
#                               - field: info2
#                                 label: key
#                                 value: value
#                               - section:
#                                   key: nested2
#                                   label: Info
#                                   fields:
#                                     - field: info3
#                                       label: key
#                                       value: value
#                                     - section:
#                                         key: nested3
#                                         label: Info
#                                         fields:
#                                           - field: info4
#                                             label: key
#                                             value: value
#                                           - field: info5
#                                             label: key
#                                             value: value

# Section with all the values types
# sections:
#   - label: Section 1
#     key: first
#     type: accordion
#     attributes:
#       hasBottomDivider: true
#     fields: 
#       - field: name
#         label: Name
#       - field: link
#         label: Link
#         type: link 
#       - field: status
#         label: Status
#         type: status
#         attributes:
#           label: 
#             value: data2.status.label
#           type:
#             value: data2.status.type
#       - field: profile
#         label: Profile
#         type: avatar
#         attributes:
#           src: 
#             value: data2.url
#       - field: jsonData
#         label: Json
#         type: json
#       - field: arrayData
#         label: Array
#         type: array
#         attributes:
#           variant: badge    


# version: v2
# header:
#   title: Bundle Definition
#   type: colorless
#   paddingless: true
#   size: small
#   isBordered: false
# sections:
#   - label: Overview
#     key: first
#     attributes:
#       hasBottomDivider: true
#     tooltip: Information about sections
#     fields:
#       - field: ID
#         label: ID
#         tooltip: ID
#         value: context.aetherData.id
#         visibleIf:
#             "==":
#             - var: context.enableRefund\.test
#             - male
#       - field: VBO ID
#         label: VBO ID
#         tooltip: VBO ID
#         value: context.aetherData.vboID
#       - field: Code
#         label: Code
#         tooltip: Code
#         value: context.aetherData.shortCode
#       - field: Name
#         label: Name
#         value: context.aetherData.name
#       - field: Description
#         label: Description
#         value: context.aetherData.description
#       - field: Status
#         label: Status
#         tooltip: Status
#         value: context.aetherData.status
#       - field: Created
#         label: Created
#         value: context.aetherData.createdAt
#       - field: Modified
#         label: Modified
#         value: context.aetherData.updatedAt
#       - section: 
#           label: Overview
#           key: second
#           fields: 
#             - field: Modified
#               label: Modified
#               value: context.aetherData.updatedAt
#             - section: 
#                 label: 'Test' 
#                 key: second
#                 isJson: true
#                 type: accordion
#                 data: context.free
#                 attributes: 
#                   hasBottomDivider: true