version: v2
css: |-
    .custom-ach-search-container{background-color: #f5f5f5;padding: 20px;}
views:
    - sections:
          - attributes:
                hasBottomDivider: true
            key: postingSummary
            fields:
                - field: ID
                  label: ID
                  tooltip: ID
                  value: context.aetherData.id
                  visibleIf:
                      '==':
                          - var: context.enableRefund\.test
                          - male
                - field: VBO ID
                  label: VBO ID
                  tooltip: VBO ID
                  value: context.aetherData.vboID
                - field: Code
                  label: Code
                  tooltip: Code
                  value: context.aetherData.shortCode
                - field: Name
                  label: Name
                  value: context.aetherData.name
                - field: Description
                  label: Description
                  value: context.aetherData.description
                - field: Status
                  label: Status
                  tooltip: Status
                  value: context.aetherData.status
                - field: Created
                  label: Created
                  value: context.aetherData.createdAt
                - field: Modified
                  label: Modified
                  value: context.aetherData.updatedAt
                - section:
                      label: Overview
                      key: second
                      fields:
                          - field: Modified
                            label: Modified
                            value: context.aetherData.updatedAt
                          - section:
                                label: 'Test'
                                key: second
                                isJson: true
                                type: accordion
                                data: context.free
                                attributes:
                                    hasBottomDivider: true
      header:
          title:
              value: data1.postingHeader
      class: posting-summary-view
    - sections:
          - key: second-custom-comp
            type: custom
            config:
                name: search
                package:
                    name: ach-search
                    version: '0.4.6'
                props:
                    ifi-id:
                        value: context.tenantId
                    user-id:
                        value: context.aetherData.name
                    service-base-url:
                        value: 
                          handler: return data1.serviceUrl || data2.serviceBaseUrl || 'https://sb1-god-aries.mum1-pp.zetaapps.in/mars' || ''
                    config:
                        value: data1.searchConfig
                    aan-search-url: 'https://0-0-hercules.mum1-pp.zetaapps.in/atalanta'
                    card-search-url: 'https://sb1-god-acropolis.mum1-pp.zetaapps.in/fusion-tomcat'
                events:
                    accountHolderSearch_linkClicked: |-
                        console.log("accountHolderSearch_linkClicked body executed", eventData);
