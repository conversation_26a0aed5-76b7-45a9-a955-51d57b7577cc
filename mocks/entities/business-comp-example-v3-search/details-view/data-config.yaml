- dataEndPoint: test
  refId: data1
  httpRequest:
      method: 'GET'
      url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: |-
    const searchConfig= JSON.stringify({"real":{"types":["emailId","phone","id","name","crn","card","aan"]},"featureFlags":{"aanApiVersion":"v2"},"useV2SearchExperience":true});
    return { ...res, searchConfig, postingHeader: 'Posting Summary'};
- dataEndPoint: test
  refId: data2
  httpRequest:
      method: 'GET'
      url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: "return { tenantId: '600325',first: { code: '4567', second: { third: { state: 'Karnataka', country: 'India' }} }, showTable: true ,ledger:{id:'1088644771886435437',tenantId:'600309',coaId:'1441665598567441191',serviceUrl:'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon'}}"
