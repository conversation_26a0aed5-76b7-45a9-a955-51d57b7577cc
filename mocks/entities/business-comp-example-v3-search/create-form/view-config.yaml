version: v2
fields:
    searchPanel:
        type: custom
        className: custom-ach-search-container
        config:
            name: search
            package:
                name: ach-search
                version: '0.4.6'
            props:
                ifi-id:
                    value: context.metadata.tenantId
                user-id:
                    value: formModel.name
                service-base-url:
                    value: 'https://sb1-god-aries.mum1-pp.zetaapps.in/mars'
                config:
                    value: prefillData.data2.searchConfig
                aan-search-url: 'https://0-0-hercules.mum1-pp.zetaapps.in/atalanta'
                card-search-url: 'https://sb1-god-acropolis.mum1-pp.zetaapps.in/fusion-tomcat'
            events:
                accountHolderSearch_linkClicked: |-
                    console.log("accountHolderSearch_linkClicked body executed", eventData);
                    return {selectedAch: {...eventData}};
    paymentSearch:
        type: custom
        className: custom-payment-search-container
        config:
            name: payment-search
            package:
                name: payment-views
                version: '1.2.16-sample-form.1'
            props:
                ifi-id:
                    value: context.metadata.tenantId
                name:
                    value:
                        handler: return formModel.name
                service-base-url:
                    value: 'https://sb1-god-aries.mum1-pp.zetaapps.in/mars'
                channels: '[{"name":"VISA outside channel (CHNIN001007)","id":0,"code":"CHNIN001007"},{"name":"MC DMS US Offus Channel (CHNUSZZ1101)","id":1,"code":"CHNUSZZ1101"},{"name":"MC DMS US Offus Channel (CHNUSZZ1102)","id":2,"code":"CHNUSZZ1102"},{"name":"Visa Prepaid","id":3,"code":"Visa Prepaid"},{"name":"Onus","id":4,"code":"Onus"}]'
            events:
                update_form: |-
                    console.log("paymentForm_linkClicked body executed", eventData);
                    return {selectedPayment: {...eventData}};
    paymentForm:
        type: custom
        className: custom-payment-form-container
        config:
            name: payment-form
            package:
                name: payment-views
                version: '1.2.16-sample-form.1'
            props:
                ifi-id:
                    value: context.metadata.tenantId
                service-base-url:
                    value:
                        handler: return 'https://sb1-god-aries.mum1-pp.zetaapps.in/mars'
                name:
                    value:
                        handler: return formModel.description || formModel.name || 'https://sb1-god-aries.mum1-pp.zetaapps.in/mars'
            events:
                update_form: |-
                    console.log("paymentForm_linkClicked body executed", eventData);
                    return {selectedPayment: {...eventData}};
    name:
        label: Name
        placeholder: Enter Name
        prefix: "$"
        suffix: "$"
        maxlength: 50
        rules:
            required: true
    description:
        label: Description (Optional)
        component: AngelosTextArea
        maxlength: 140
        value:
            handler: return formModel.name
        rules:
            required: false
    features:
        label: Business Comps to render
        component: AngelosSelect
        values:
            - label: Search Panel
              value: SEARCH_PANEL
            - label: Payment Form
              value: PAYMENT_FORM
            - label: Payment Search
              value: PAYMENT_SEARCH
    ledgerDetails:
        type: array
        fields:
            glName:
                label: GL Name
                placeholder: Enter Text
                rules:
                    required: true
            glAccount:
                label: GL Account
                placeholder: Enter Text
            accountingType:
                label: Accounting Type
                placeholder: Enter Text
            tags:
                label: Tags
                component: AngelosSelect
                placeholder: Enter Tag
                chipIcon: cancel
                disabled: false
                tags: name
                field: name
                keepOpen: true
                allowNew: true
                autocomplete: true
                allowDuplicates: false
                values:
                    handler: return context.list
    workbenches:
        type: array
        fields:
            name:
                label: Name
                tooltip: Workbench Name
                type: text
            code:
                label: Code
                tooltip: Workbench Code
                type: text
            description:
                label: Description
                tooltip: Workbench Description
                type: textarea
            status:
                label: Status
                tooltip: Workbench Status
                component: AngelosRadioButton
                values:
                    - label: Enabled
                      value: ENABLED
                    - label: Disabled
                      value: DISABLED
            consoles:
                label: Consoles
                tooltip: Workbench Consoles
                component: AngelosCheckbox
                values:
                    - label: Accounts
                      value: Accounts
                    - label: Rewards
                      value: Rewards
                    - label: Dispute
                      value: Dispute
form:
    sections:
        - title: Basic Details
          sections:
              - direction: vertical
                fields:
                    - key: name
                    - key: description
                    - key: features
              - title: Ledger Details
                attributes:
                    bordered: false
                direction: horizontal
                sections:
                    - columns: 12
                      arrayField: ledgerDetails
                      direction: horizontal
                      attributes:
                          bordered: false
                      fields:
                          - key: glName
                            colspan: 3
                          - key: glAccount
                            colspan: 3
                          - key: accountingType
                            colspan: 3
                          - key: tags
                            colspan: 3
        - title: Workbench
          arrayField: workbenches
          subtitle: Expand to edit and modify the configurations
          attributes:
              variant: boxed
              keepExpanded: multiple
              sectionTitleTransformer: console.log("hello"); return data.name + ' - ' + data.code
              addSectionSelection:
                  component: AngelosSelect
                  uniqueKey: name
                  defaultSelectedValue: Add Workbench
                  values:
                      handler: 'return [{label: "Accounts Workbench", value: {name: "Accounts
                          Label", code: "WB008"}}, {label: "Rewards Workbench", value: {name:
                          "Rewards Label", code: "WB008"}}]'
          accordionItemAttributes:
              handler: 'console.log("hello"); return (index ? {disabled: true}: {})'
          sections:
              - fields:
                    - key: name
                      colspan: 6
                    - key: description
                      colspan: 6
                direction: horizontal
              - fields:
                    - key: consoles
                      colspan: 6
                    - key: status
                      colspan: 6
                direction: horizontal
        - title: Account Holder Search
          subtitle: Custom Business component to be rendered
          customField: searchPanel
          condition:
              visibleIf:
                  '===':
                      - var: formModel.features
                      - SEARCH_PANEL
        - title: Payment Form
          subtitle: Custom Business component to be rendered
          customField: paymentForm
          condition:
              visibleIf:
                  '===':
                      - var: formModel.features
                      - PAYMENT_FORM
        - title: Payment Search
          subtitle: Custom Business component to be rendered
          customField: paymentSearch
          condition:
              visibleIf:
                  '===':
                      - var: formModel.features
                      - PAYMENT_SEARCH