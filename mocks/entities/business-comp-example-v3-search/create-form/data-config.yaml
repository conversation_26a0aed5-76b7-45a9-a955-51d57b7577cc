- httpRequest:
      method: POST
      url: 'http://localhost:4000/post-data'
      postData:
          mimeType: application/json
          params: []
          text: <%= JSON.stringify(formModel) %>
      additional: {}
  inputParams: []
  paginationParams: {}
- type: PREFILL
  refId: data1
  httpRequest:
      method: GET
      url: http://localhost:4000/get-data
  inputParams: []
  responseTransformer: "return { userId: 'uyughjbjhj9898hjhj'}"
- type: PREFILL
  refId: data2
  httpRequest:
      method: GET
      url: http://localhost:4000/get-data
  inputParams: []
  responseTransformer: |-
      const searchConfig= JSON.stringify({"real":{"types":["emailId","phone","id","name","crn","card","aan"]},"featureFlags":{"aanApiVersion":"v2"},"useV2SearchExperience":true});
      return { ...res, searchConfig, postingHeader: 'Posting Summary' };
