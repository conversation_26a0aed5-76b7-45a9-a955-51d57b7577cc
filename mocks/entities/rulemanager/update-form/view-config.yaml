version: v2
fields:
  name:
    label: Name
    type: string
    placeholder: "Eg: Block Gambling MCCs Rule"
    maxlength: 180
    rules:
      required: true
    indicatorType: mandatory
    description: "Name shown on the rules manager which identifies as the created rule"
    # value: 
    #   handler: |-
    #     return prefillData.ruleData.name
  optumRuleSchemaId:
    label: Optum rule schema ID
    type: string
    placeholder: Enter ID
    maxlength: 180
    rules:
      required: true
      regex:
        pattern: "^[a-zA-Z0-9_-]+$"
        message: "Only alphanumeric characters, underscores, and hyphens are allowed"
    indicatorType: mandatory
    description: "A unique ID generated by Optum's payment system to determine applicability of this rule"
  maximumAmountUSD:
    label: Maximum amount (USD)
    multiple: false
    type: number
    placeholder: Enter amount
    rules:
      required: true
      min_value:
        value: 0
    indicatorType: mandatory
    description: Maximum allowed transaction amount
    # value: 
    #   handler: |-
    #     return context.maximumAmountUSD
  riskScore:
    label: Risk Scores (1-100)
    placeholder: Enter value
    multiple: false
    type: number
    rules:
      min_value:
        value: 1
      max_value:
        value: 100
    description: "Transactions with risk score above the selected threshold value will be declined"
    # value: 
    #   handler: |-
    #     return context.riskScore
  allowedMCCs:
    label: Allowed Merchant Category Codes (MCCs)
    type: string
    placeholder: "Enter comma separated values"
    rules:
      required: false
    # value: 
    #   handler: |-
    #     return prefillData.ruleData.allowedMCCs
  disallowedMCCs:
    label: "Disallowed Merchant Category Codes (MCCs)"
    type: string
    placeholder: "Enter comma separated values"
    rules:
      required: false
  allowedMIDs:
    label: "Allowed Merchant IDs (MIDs)"
    type: string
    placeholder: "Enter comma separated values"
    rules:
      required: false
  disallowedMIDs:
    label: "Disallowed Merchant IDs (MIDs)"
    type: string
    placeholder: "Enter comma separated values"
    rules:
      required: false

form:
  sections:
    - direction: horizontal
      fields:
        - field_key: name
          colspan: 6
        - field_key: optumRuleSchemaId
          colspan: 6
    - title: Virtual Card usage criteria
    - direction: horizontal
      fields:
        - field_key: maximumAmountUSD
          colspan: 6
        - field_key: riskScore
          colspan: 6
    - direction: horizontal
      fields:
        - field_key: allowedMCCs
          colspan: 6
        - field_key: disallowedMCCs
          colspan: 6
    - direction: horizontal
      fields:
        - field_key: allowedMIDs
          colspan: 6
        - field_key: allowedMIDs
          colspan: 6
  buttons:
    - action: submit
      text: Update
    - action: cancel
      text: Discard

css: |-
  #angelos-form-section-container {
    margin: 2px 24px !important;
  }
  .form-sections-container {
    border-top: none;
    box-shadow: none;
  }

  #angelos-v2-footer {
    padding: 16px 24px !important;
    width: 100%;
    position: relative;
    border-top: 1px solid var(--gds-color-bg-button-tertiary-dark2);
    box-sizing: border-box;
    left: 0;
    right: 0;
    margin: 0 !important;
  }

  #angelos-v2-footer > .z-space {
    width: 100%;
    display: flex;
    justify-content: space-between !important; 
    gap: 0 !important;
  }

  #angelos-v2-footer > .z-space > div {
    max-width: none !important;
  }

  #angelos-v2-footer .z-button.mx-1 {
    margin-left: 0;
    margin-right: 0;
  }

  #angelos-v2-footer .z-button--neutral-type.z-button--outlined-variant {
    --z-border: none !important;
    --z-border-hover: none !important;
    --z-border-pressed: none !important;
    --z-border-focus: none !important;
    --z-border-disabled: none !important;
  }
  .z-layout-scroll-container {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
  }
