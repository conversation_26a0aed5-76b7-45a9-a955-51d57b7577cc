- type: PREFILL
  refId: ruleManager
  httpRequest:
    url: "<%= params.baseUrl %>/tenants/<%= tenantId %>/rules/<%= params.optumRuleSchemaId %>"
    method: GET
    headers:
      - name: Authorization
        value: Bearer <%= authToken %>
    httpVersion: HTTP/1.1
    queryString: []
    inputParams: []
    paginationParams: {}
  responseTransformer: |-

    function formatDate(date) {
      if (date === null || date === undefined || date === '-' || date === '') {
        return "-";
      } else {
        date = new Date(date);
      }
      // Check if the date is invalid
      if (isNaN(date.getTime())) {
        throw new Error("Invalid date");
      }

      const months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];

      const day = String(date.getDate()).padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();
      
      let hours = date.getHours();
      
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${day} ${month} ${year}, ${hours}:${minutes}:${seconds}`;
    } 

    try {
      if (res) {
        const rule = res;
        console.log(rule);
        const data = {
          ruleId: rule.ruleId || '-',
          name: rule.name || '-',
          optumRuleSchemaId: rule.optumRuleSchemaId || '-',
          createdAt: rule.createdAt || '-',
          updatedAt: rule.updatedAt || '-',
          formatedCreatedAt: formatDate(rule.createdAt),
          formatedUpdatedAt: formatDate(rule.updatedAt),
          maximumAmountUSD: rule.maximumAmountUSD || '-',
          riskScore: rule.riskScore || '-',
          allowedMCCs: Array.isArray(rule.allowedMCCs) ? rule.allowedMCCs : ['-'],
          disallowedMCCs: Array.isArray(rule.disallowedMCCs) ? rule.disallowedMCCs : ['-'],
          allowedMIDs: Array.isArray(rule.allowedMIDs) ? rule.allowedMIDs : ['-'],
          disallowedMIDs: Array.isArray(rule.disallowedMIDs) ? rule.disallowedMIDs : ['-'],
        };
        return data;
      }
      return {};
    } catch (error) {
      console.error('Error fetching rule details:', error);
      return {};
    }
