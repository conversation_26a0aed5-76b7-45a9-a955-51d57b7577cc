version: v2
views:
  - sections:
      - key: ruleManager
        fields:
          - field: name
            label: Name
            type: template
            value: "<div>{{ ruleManager.name || '-' }}</div>"
          - field: optumRuleSchemaId
            label: "Optum rule schema ID"
            # tooltip: "A unique ID generated by Optum's payment system to determine applicability of this rule"
            type: template
            value: "<div>{{ ruleManager.optumRuleSchemaId || '-' }}</div>"
          - field: formatedCreatedAt
            label: Created On
            type: template
            value: "<div>{{ ruleManager.formatedCreatedAt || '-' }}</div>"
          - field: formatedUpdatedAt
            label: Last updated on
            type: template
            value: "<div>{{ ruleManager.formatedUpdatedAt || '-' }}</div>"
  - header:
      title: Virtual card usage criteria
    sections:
      - fields:
          - field: maximumAmountUSD
            label: "Maximum amount (USD)"
            type: template
            value: "<div>{{ ruleManager.maximumAmountUSD || '-' }}</div>"
          - field: riskScore
            label: "Risk Score"
            type: template
            value: "<div>{{ ruleManager.riskScore || '-' }}</div>"
          - field: allowedMCCs
            label: Allowed merchant category code (Mcc)
            type: array
            attributes:
              variant: badge
            value: ruleManager.allowedMCCs
          - field: disallowedMCCs
            label: "Disallowed merchant category code (Mcc)"
            type: array
            attributes:
              variant: badge
            value: ruleManager.disallowedMCCs
          - field: allowedMIDs
            label: "Allowed merchant Ids (Mids)"
            type: array
            attributes:
              variant: badge
            value: ruleManager.allowedMIDs
          - field: disallowedMIDs
            label: "Disallowed merchant Ids (Mids)"
            type: array
            attributes:
              variant: badge
            value: ruleManager.disallowedMIDs
css: |-
  .z-details-view__details-node-wrapper {
  display: flex;
  border-bottom: 1px solid var(--gds-color-bg-button-tertiary-dark2);
  box-shadow: none;
  }

  .z-details-view__details-node-wrapper:last-child {
    border-bottom: none;
  }

  .z-popover {
    max-height: 300px;
    overflow-y: auto;
  }
