version: v2
paginationConfig:
  page: 1
  pageSize: 10
  showSizePicker: true
  pageSizes:
    - 5
    - 10
    - 20
    - 50
    - 100
uiDataControl: false
searchConfig:
  searchKey: name
  placeholder: Search by name
tableConfig:
  columns:
    - label: Name
      field: name
      render: |-
        return h(ZButton, {
          variant: 'link',
          onClick: (event) => {
            emitEvent('user-name-clicked', {
              name: row.name,
              row: row
            })
          }
        }, row.name);
      width: 200px
    - label: Rule ID
      field: ruleId
      width: 200px
    - label: "Optum rule schema ID"
      field: optumRuleSchemaId
      width: 200px
    - label: Created on
      field: formatedCreatedAt
      width: 200px
    - label: Last updated on
      field: formatedUpdatedAt
      width: 200px
    - label: ""
      field: ""
      width: 120px
      render: |-
        return h(ZButton, {
          size: 'small',
          onClick: (event) => {
            emitEvent('edit-rule-clicked', {
              ruleId: row.ruleId,
              name: row.name,
              row: row
            });
          }
        }, { icon: () => h(icons.Edit), default: () => 'Edit rule' });
# filterConfig:
#   - field: name
#     fixed: true
#     label: Name
#     selectors:
#       "by-input":
#         placeholder: Search by name
#         searchable: true
#         multiple: false
#   - field: ruleId
#     fixed: true
#     label: Rule ID
#     selectors:
#       "by-input":
#         placeholder: Search by Rule ID
#         searchable: true
        # multiple: false
css: |-
  .angelos-view.angelos-view-section {
    border: none !important;
  }
  .z-data-table-wrapper {
    border: none !important;
  }
  .z-data-table-th__title{
    font-weight: bold;
  }
  .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }
  .status-dot--active {
    background-color: #49CF55;
  }
  .status-dot--inactive {
    background-color: #CA7626;
  }
  .status-dot--terminated {
    background-color: #BE304A;
  }
  .status-dot--unknown {
    background-color: #2A4FF0;
  }
  .phase--failed{
    color: #f04f6d;
  }
  .phase--successful{
    color: #49cf55;
  }
  .phase--danger{
    color: #f09948;
  }
  a {
    text-decoration: none;
  }
  .z-data-table {
    --z-merged-th-color: #fff;
    font-size: 14px;
  }
