refId: RM
httpRequest:
  url: "<%= params.baseUrl %>/tenants/<%= tenantId %>/rules/list"
  method: GET
  bodySize: -1
  headersSize: -1
  httpVersion: HTTP/1.1
  headers:
    - name: Accept
      value: application/json
    - name: Authorization
      value: Bearer <%= authToken %>
  queryString: []
inputParams: []
paginationParams: {}
requestTransformer:
  query: |-
    return {
      pageNo: pagination.pageNumber,
      pageSize: pagination.pageSize
    };
responseTransformer: |-
  const data = [];
  try {
    function formatDate(date) {
      if (date === null || date === undefined || date === '-' || date === '') {
        return "-";
      } else {
        date = new Date(date);
      }
      // Check if the date is invalid
      if (isNaN(date.getTime())) {
        throw new Error("Invalid date");
      }

      const months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];

      const day = String(date.getDate()).padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();
      
      let hours = date.getHours();
      
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${day} ${month} ${year}, ${hours}:${minutes}:${seconds}`;
    }    

    const items = Array.isArray(res.ruleList) ? res.ruleList : (res.data || []);

    items.forEach(rule => {
      const name = rule?.name || '-';
      const ruleId = rule?.ruleId || '-';
      const optumRuleSchemaId = rule?.optumRuleSchemaId || '-';
      const createdAt = rule?.createdAt || '-';
      const formatedCreatedAt = formatDate(createdAt);
      const updatedAt = rule?.updatedAt || '-';
      const formatedUpdatedAt = formatDate(updatedAt);
      const ruleValue = rule;
      data.push({
        name,
        ruleId,
        optumRuleSchemaId,
        createdAt,
        updatedAt,
        formatedCreatedAt,
        formatedUpdatedAt,
        ruleValue
      });
    });
    
  } catch (error) {
    console.log(error);
  }
  const returnedData = {
    data: data,
    total: res.page.totalElements
  };
  return returnedData

