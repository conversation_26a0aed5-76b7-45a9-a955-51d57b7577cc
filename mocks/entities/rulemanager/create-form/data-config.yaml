- refId: RM
- httpRequest:
    url: "<%= params.baseUrl %>/tenants/<%= tenantId %>/rules"
    method: POST
    comment: ""
    cookies: []
    headers:
      - name: Content-Type
        value: application/json
      - name: accept
        value: application/json
      - name: Authorization
        value: Bearer <%= authToken %>
    bodySize: -1
    postData:
      text: |-
        <%= JSON.stringify(requestPayload) %>
      params: []
      mimeType: application/json
    headersSize: -1
    httpVersion: HTTP/1.1
    queryString: []
  inputParams: []
  paginationParams: {}
  requestBodyTransformer: |-
    function parseCommaSeparatedValues(value) {
      return value ? value.split(',').map(item => item.trim()).filter(item => item !== '') : [];
    }

    const requestPayload = {
      name: formModel.name,
      optumRuleSchemaId: formModel.optumRuleSchemaId,
      maximumAmountUSD: formModel.maximumAmountUSD ? parseFloat(formModel.maximumAmountUSD) : null,
      riskScore: formModel.riskScore ? parseFloat(formModel.riskScore) : null,
      allowedMCCs: parseCommaSeparatedValues(formModel.allowedMccs),
      disallowedMCCs: parseCommaSeparatedValues(formModel.disallowedMccs),
      allowedMIDs: parseCommaSeparatedValues(formModel.allowedMids),
      disallowedMIDs: parseCommaSeparatedValues(formModel.disallowedMids)
    };

    return { requestPayload };
