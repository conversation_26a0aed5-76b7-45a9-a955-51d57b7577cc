---
version: v2
fields:
    array: 
      type: array
      fields: 
        test: 
          component: AngelosInput
          label: Test
          rules: 
            required: true
    code:
        name: Code
        label: Code editor
        component: AngelosCodeEditor
        theme: vs-dark
        value: |-
            function test(){ 
              console.log('hello world')
            }
    gender:
        name: Gender
        component: AngelosSelect
        label: Gender
        values:
            - label: Chapter 7
              value: Chapter 7
              description: Liquidation under the Bankruptcy Code
            - label: Chapter 9
              value: Chapter 9
              description: Municipality Bankruptcy
            - label: Chapter 11
              value: Chapter 11
              description: Reorganization Under the Bankruptcy Code
            - label: Chapter 12
              value: Chapter 12
              description: Family Farmer or Family Fisherman Bankruptcy
            - label: Chapter 13
              value: Chapter 13
              description: Individual Debt Adjustment
            - label: Chapter 15
              value: Chapter 15
              description: Ancillary and Other Cross-Border Cases
    daterange:
        label: DEFAULT
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        type: daterange
        dateFormat: YYYY-MM-DD
        outputDateFormat: YYYY-MM-DD
        minDate: 2024-02-20
        maxDate: 2024-03-20
        value: [2024-02-26, 2024-03-10]
        timezone: DEFAULT
    datetimerange:
        label: DEFAULT
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        type: datetimerange
        dateFormat: YYYY-MM-DD HH:mm:ss
        outputDateFormat: YYYY-MM-DD HH:mm:ss
        minDate: 2024-02-20
        maxDate: 2024-03-20
        value: [2024-02-26, 2024-03-10]
        timezone: DEFAULT
    datetime1:
        label: SYSTEM
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        dateFormat: YYYY-MM-DDTHH:mm:ss.SSSZ
        outputDateFormat: YYYY-MM-DDTHH:mm:ss.SSSZ
        value: '2015-07-25T23:09:01.795+07:00'
        timezone: SYSTEM
    datetime2:
        label: PRESERVE
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        dateFormat: YYYY-MM-DDTHH:mm:ss.SSSZ
        outputDateFormat: YYYY-MM-DDTHH:mm:ss.SSSZ
        value: '2015-07-25T23:09:01.795+07:00'
        timezone: PRESERVE
    datetime3:
        label: UTC
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        dateFormat: YYYY-MM-DDTHH:mm:ss.SSSZ
        outputDateFormat: YYYY-MM-DDTHH:mm:ss.SSSZ
        value: '2015-07-25T23:09:01.795+07:00'
        timezone: UTC
    date1:
        label: SYSTEM
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        dateFormat: YYYY-MM-DD
        outputDateFormat: YYYY-MM-DD
        timezone: SYSTEM
        value: 2015-07-25
    time1:
        label: TIME PICKER WITH HOURS, MINUTES AND SECONDS
        description: This is a description
        name: Enable Refund
        component: AngelosDatePicker
        dateFormat: HH:mm:ss
        outputDateFormat: HH:mm:ss
    fruits:
        name: Fruits
        component: AngelosCheckbox
        label: Fruits
        values:
            - label: Orange
              value: Orange
            - label: Mango
              value: Mango
    fruitsRadio:
        name: Fruits
        component: AngelosRadioButton
        label: Fruits
        values:
            - label: Orange
              value: Orange
            - label: Mango
              value: Mango
        condition:
            disabledIf:
                or:
                    - '==':
                          - var: formModel.fruitsSwitcher
                          - null
                    - '==':
                          - var: formModel.fruitsSwitcher
                          - false

    fruitsSwitcher:
        name: Fruits Switcher
        component: AngelosSwitch
        label: Fruits Switcher
        trueLabel: 'Yes'
        falseLabel: 'No'
        condition:
            disabledIf:
                '==':
                    - var: formModel.name
                    - hello
    remarks:
        name: Remarks
        type: textarea
        label: Add Remarks
        maxlength: 140
        placeholder: Add Remarks
        indicatorType: mandatory
        condition:
            visibleIf:
                '===':
                    - var: formModel.fruitsSwitcher
                    - true

    reasonCode:
        name: Reason Code
        label: Reason Code
        rules:
            required:
              value: true
              message: Reason code please enter
        #     email: 
        #       value: true
        #       message: Reason code please enter proper email
        placeholder: Select Reason Code
        indicatorType: mandatory
    name:
        name: Name
        label: Name
        type: number
        # rules:
        #     max_value: 
        #       value: 10
        #       message: Name should follow the max limit
        #     required:
        #       value: true
        #       message: Name please provide
        #     min_value: 
        #       value: 3
        #       message: Name should follow the min limit
    file: 
      label: File
      component: AngelosFileUpload
      maxFileSize: 1000000
      accept:
        handler: return '.jpg,.jpeg,.png'
      message: 'Only .jpg, .jpeg, .png files are allowed' 
      validator: |-
        console.log('Inside validator');
        return {
          errors: []
        } 
    templateTest:
      name: Template Test
      component: AngelosTemplate
      template: |-
          <div>
            <p>This is a template test</p>
            <p>{{ formModel.name }}</p>
          </div>



form:
    sections:
        - direction: horizontal
          title: Section Title
          subtitle: lorem ipsum dolor sit amet consectetue
          headerAttributes: 
            isBordered: false
          sections:
              - arrayField: array
                title: Parameters
                subtitle: Array Field description
                headerAttributes:
                  isBordered: false
                fields: 
                  - key: test
              - direction: vertical
                # title: Section Title
                # subtitle: lorem ipsum dolor sit amet consectetuer
                # columns: 6
                fields:
                    - key: reasonCode
                      colspan: 6
                    - key: code
        - direction: horizontal
          title: Section Title
          subtitle: lorem ipsum dolor sit amet consectetue 2
          layoutType: accordion
          headerAttributes: 
            isBordered: false
          sections:
              - accordionAttributes: 
                  title: Accordion Title
                direction: vertical
                # title: Section Title
                # subtitle: lorem ipsum dolor sit amet consectetuer
                columns: 6
                fields:
                    - key: reasonCode
                      colspan: 6
                    - key: code
                    - key: gender
                    - key: fruits
                    - key: date1
                    - key: daterange
                    - key: datetimerange
                    - key: time1
              - direction: vertical
                columns: 6
                fields:
                    - key: name
                    - key: fruitsRadio
                    - key: fruitsSwitcher
                    - key: remarks
                    - key: datetime1
                    - key: datetime2
                    - key: datetime3
                    - key: file
                    - key: templateTest

    buttons:
        - action: submit
          text: Submit
        - action: cancel
          text: Cancel
    confirmationDialog:
        title: Reversal confirmation
        template: >-
            hello
