version: v2
fields:
  text: 
    label: Text
    component: AngelosInput
  why:
    label: Why
    component: AngelosInput
    value: 
      handler: return formModel.text
  test:
    label: test
    component: AngelosSwitch
    value: true
  pseudoLedger: 
    label: Test Label
    component: AngelosSelect
    values: 
      handler: return context.pseudoLedgerList
    rules:
      required: true
  postingCategories: 
    type: array
    value: 
      handler: return context.someKey.find(item => item.pseudoLedger === formModel.pseudoLedger)?.postingCategories || []
      dependencies: 
        - pseudoLedger
    fields: 
      balanceType:
        label: Balance Type
        component: AngelosRadioButton
        values: 
          - label: Net Balance
            value: NET_BALANCE
          - label: Split Balance
            value: SPLIT_BALANCE
      debit.glName:
        label: Debit GL Name
        component: AngelosSelect
        values:
          handler: return context.glNameList
      debit.attributes:
        label: Debit Attributes
        component: AngelosCodeEditor
      credit.glName:
        label: Credit GL Name
        component: AngelosSelect
        values:
          handler: return context.glNameList
      credit.attributes:
        label: Credit Attributes
        component: AngelosCodeEditor
form:
  sections: 
    - title: XYZ
      sections: 
        - title: pseudoLedger
          direction: horizontal
          fields: 
            - key: text
              colspan: 4
            - key: why
              colspan: 4
            - key: test
              colspan: 4
            - key: pseudoLedger
              colspan: 4
        - title: Posting Categories
          arrayField: postingCategories
          sections: 
            - fields:
              - key: balanceType
                colspan: 12
            - direction: horizontal
              sections:
              - title: Debit
                columns: 6
                fields:
                  - key: debit.glName
                    colspan: 12
                  - key: debit.attributes
                    colspan: 12
                condition:
                  visibleIf: 
                    "===": 
                      - var: arrayItemData.balanceType
                      - NET_BALANCE
              - columns: 6
                title: Credit
                fields:
                  - key: credit.glName
                    colspan: 12
                  - key: credit.attributes
                    colspan: 12
                condition:
                  visibleIf:
                    "===": 
                      - var: formModel.test
                      - true
