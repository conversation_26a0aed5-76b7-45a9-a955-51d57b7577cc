- type: PREFILL
  refId: data1
  httpRequest:
      url: 'http://localhost:4000/get-data'
      method: GET
  inputParams: []
  paginationParams: {}
  responseTransformer: 'return { ...res }'
- httpRequest:
      method: POST
      url: 'http://localhost:4000/post-data'
      postData:
          mimeType: application/json
          params: []
          text: <%= JSON.stringify(formModel) %>
      additional: {}
  inputParams: []
  paginationParams: {}

- type: BEFORE_SUBMIT
  dataTransformer: |-
    console.log('Test', formModel, context);
    return {
      ...formModel,
      data1: 'data1'
    }  
