
version: v2
views:
  - sections:
     - attributes:
         hasBottomDivider: true
       key: postingSummary
       fields:
         - field: ID
           label: ID
           tooltip: ID
           value: context.aetherData.id
           visibleIf:
               "==":
               - var: context.enableRefund\.test
               - male
         - field: VBO ID
           label: VBO ID
           tooltip: VBO ID
           value: context.aetherData.vboID
         - field: Code
           label: Code
           tooltip: Code
           value: context.aetherData.shortCode
         - field: Name
           label: Name
           value: context.aetherData.name
         - field: Description
           label: Description
           value: context.aetherData.description
         - field: Status
           label: Status
           tooltip: Status
           value: context.aetherData.status
         - field: Created
           label: Created
           value: context.aetherData.createdAt
         - field: Modified
           label: Modified
           value: context.aetherData.updatedAt
         - section: 
             label: Overview
             key: second
             fields: 
               - field: Modified
                 label: Modified
                 value: context.aetherData.updatedAt
               - section: 
                   label: 'Test' 
                   key: second
                   isJson: true
                   type: accordion
                   data: context.free
                   attributes: 
                     hasBottomDivider: true
    header:
      title:
        value: data1.postingHeader
    class: posting-summary-view
  - sections:
      - key: first-custom-comp
        type: custom
        config:
          name: entity-details
          package:
            name: cipher-provider
            version: '0.1.9'
          props:
            - key: 'tenantId'
              value: data1.ledger.tenantId
            - key: 'ledgerId'
              value: data1.ledger.id
            - key: 'coaId'
              value: data1.ledger.coaId
            - key: serviceUrl
              value: data1.serviceUrl
  