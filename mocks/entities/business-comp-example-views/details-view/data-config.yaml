- dataEndPoint: test
  refId: data1
  httpRequest:
    method: 'GET'
    url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: "return { ...res }"
- dataEndPoint: test
  refId: data2
  httpRequest:
    method: 'GET'
    url: 'http://localhost:4000/get-data'
  inputParams: []
  responseTransformer: "return { first: { code: '4567', second: { third: { state: 'Karnataka', country: 'India' }} }, showTable: true ,ledger:{id:'1088644771886435437',tenantId:'600309',coaId:'1441665598567441191',serviceUrl:'https://sb1-god-aura.mum1-pp.zetaapps.in/tachyon'}}"
