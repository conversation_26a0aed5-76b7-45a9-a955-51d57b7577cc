/* eslint-disable @typescript-eslint/no-var-requires */
const { getJsonFromYAML } = require('./yml-to-json.cjs');
const url = require('url');

module.exports = (req, res, next) => {
    const _send = res.send;
    res.send = function (body) {
        if (url.parse(req.url, true).query['singular']) {
            try {
                const componentType = url.parse(req.url, true).query['componentType'];
                const businessEntityId = url.parse(req.url, true).query['businessEntityId'];
                const json = JSON.parse(body);
                if (Array.isArray(json)) {
                    if (json.length === 1) {
                        json[0].viewConfig = getJsonFromYAML(
                            componentType,
                            businessEntityId,
                            'view-config'
                        );
                        json[0].dataConfig = getJsonFromYAML(
                            componentType,
                            businessEntityId,
                            'data-config'
                        );
                        return _send.call(this, JSON.stringify(json[0]));
                    } else if (json.length === 0) {
                        return _send.call(this, '{}', 404);
                    }
                }
            } catch (e) {
                console.error('error: ', e);
            }
        }
        return _send.call(this, body);
    };
    next();
};
