// This file is used to set the context and params for the Angelos Entity Component

// Fetch the context and params from the JSON file
fetch('./mocks/context-params.json')
    .then((response) => response.json())
    .then((data) => {
        const angelosContext = data.context;
        const angelosParams = data.params;
        const token = data.token;

        // Clear the local storage and set the token from
        localStorage.clear();
        localStorage.setItem('@zeta::authToken', token);
        localStorage.setItem('authToken', token);
        localStorage.setItem('AT', token);

        // Set the context and params for the Angelos Entity Component
        const angelosEntityComponent = document.getElementById('angelosEntityComponent');
        angelosEntityComponent.setAttribute('context', JSON.stringify(angelosContext));
        angelosEntityComponent.setAttribute('params', JSON.stringify(angelosParams));

        angelosEntityComponent.addEventListener('before-submit', async function (event) {
            console.log('before-submit', event.detail);
        });

        // Dispatch the 'before-submit' event with data
        const beforeSubmitEvent = new CustomEvent('before-submit', { detail: data });
        angelosEntityComponent.dispatchEvent(beforeSubmitEvent);
        // Add event listeners for the external buttons
        const externalSubmit = document.getElementById('externalSubmit');
        const externalReport = document.getElementById('externalReport');
        const externalValidate = document.getElementById('externalValidate');
        const externalReset = document.getElementById('externalReset');
        const validateOptions = {
            silent: false
        };
        // Add event listeners for the external button which will call the Angelos Entity Component validate method
        externalValidate.addEventListener('click', async function () {
            const data = await angelosEntityComponent.checkValidity(validateOptions);
            console.log(data);
        });
        // Add event listeners for the external button which will call the Angelos Entity Component reportValidity method
        externalReport.addEventListener('click', async function () {
            const data = await angelosEntityComponent.reportValidity(validateOptions);
            console.log(data);
        });
        // Add event listeners for the external button which will call the Angelos Entity Component submit method
        externalSubmit.addEventListener('click', async function () {
            const data = await angelosEntityComponent.submit(validateOptions);
            console.log(data);
        });
        // Add event listeners for the external button which will call the Angelos Entity Component reset
        externalReset.addEventListener('click', async function () {
            const data = await angelosEntityComponent.reset(validateOptions);
            console.log(data);
        });
    })
    .catch((error) => console.error('Error fetching context and params:', error));
