{"0": [{"businessEntityId": "business-comp-example-v3-search", "componentType": "create-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "business-comp-example-v3-search", "componentType": "details-view", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "business-comp-example-sections", "componentType": "details-view", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "business-comp-example-views", "componentType": "details-view", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "business-comp-example-views", "componentType": "create-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "test-entity", "componentType": "create-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "test-entity", "componentType": "update-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "test-entity", "componentType": "details-view", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "test-entity", "componentType": "data-table", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "test-entity", "componentType": "dynamic-view", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "angelos-testing-entity", "componentType": "create-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "array-element-from-select", "componentType": "create-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "cc.bin-range", "componentType": "update-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "cc.aether.feature", "componentType": "update-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "dependencies-array-entity", "componentType": "update-form", "viewConfig": {}, "dataConfig": {}}, {"businessEntityId": "rulemanager", "componentType": "update-form", "viewConfig": {}, "dataConfig": {}}], "get-data": {"id": "get", "data": {"name": "Hello", "age": 12, "userId": "wCCSaILL1SVxKUFJkNbWkQ=="}}, "get-table-data": [{"name": "User 1", "age": 22}, {"name": "User 2", "email": "<EMAIL>", "age": 23}, {"name": "User 3", "email": "<EMAIL>", "age": 24}, {"name": "User 4", "age": 25}, {"name": "User 5", "age": 26}, {"name": "User 6", "email": "<EMAIL>", "age": 27}, {"name": "User 1", "age": 22}, {"name": "User 2", "email": "<EMAIL>", "age": 23}, {"name": "User 3", "email": "<EMAIL>", "age": 24}, {"name": "User 4", "age": 25}, {"name": "User 5", "age": 26}, {"name": "User 6", "email": "<EMAIL>", "age": 27}], "post-data": [{"id": 1}, {"id": 2}, {"id": 3}]}