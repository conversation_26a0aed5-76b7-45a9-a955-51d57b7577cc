<!DOCTYPE html>
<html>
<head>
    <title>Manual Module Loading Test</title>
</head>
<body>
    <h1>Manual Module Loading Test</h1>
    <pre id="log"></pre>
    <button onclick="testManualImport()">Test Manual Import</button>
    <div id="component-area" style="border: 1px solid red; padding: 10px; margin-top: 10px;"></div>
    
    <script>
        function log(msg) {
            document.getElementById('log').textContent += new Date().toLocaleTimeString() + ': ' + msg + '\n';
            console.log(msg);
        }

        // Set up globals first
        window.__zeta__ = {
            angelos: { SERVICE_BASE_URL: 'https://test.com/', OMS_SERVICE_BASE_URL: 'http://localhost:5000' }
        };
        window.__HERCULES__ = { $assetsBaseUrl: 'https://test.com', $store: {} };
        localStorage.setItem('AT', 'test');
        
        log('Globals set up');
        
        async function testManualImport() {
            try {
                log('Starting manual import test...');
                
                // Check if custom element is defined before import
                log('Custom element defined before import: ' + !!customElements.get('zwe-angelos-create-form-v3'));
                
                // Try to import the forms chunk directly
                log('Importing forms chunk...');
                const formsModule = await import('./dist/assets/forms-Bl5yvngb.js');
                log('Forms module imported successfully');
                log('Forms module keys: ' + Object.keys(formsModule).join(', '));
                
                // Wait a bit for custom element to be defined
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Check if custom element is now defined
                const isNowDefined = customElements.get('zwe-angelos-create-form-v3');
                log('Custom element defined after import: ' + !!isNowDefined);
                
                if (isNowDefined) {
                    log('Creating and adding component...');
                    const component = document.createElement('zwe-angelos-create-form-v3');
                    component.setAttribute('entity-id', 'test');
                    component.setAttribute('tenant-id', '0');
                    
                    document.getElementById('component-area').appendChild(component);
                    log('Component added to DOM');
                    
                    // Check component after a delay
                    setTimeout(() => {
                        log('Final component check:');
                        log('  Constructor: ' + component.constructor.name);
                        log('  Children: ' + component.children.length);
                        log('  InnerHTML length: ' + component.innerHTML.length);
                        log('  Connected: ' + component.isConnected);
                    }, 2000);
                } else {
                    log('Custom element still not defined after import');
                }
                
            } catch (error) {
                log('Error during manual import: ' + error.message);
                console.error(error);
            }
        }
    </script>
</body>
</html>
