{"name": "angelos-components", "version": "3.6.1", "private": true, "scripts": {"dev": "run-p mock dev:sdk", "local-cdn": "node local-cdn.cjs", "dev:sdk": "vite", "mock": "json-server -p 4000 --routes ./mocks/routes.json --middlewares ./mocks/singular.cjs --watch ./mocks/db.json", "build": "run-p type-check build-only", "preview": "vite preview", "serve:compressed": "node serve-compressed.js", "test": "vitest", "test:unit": "vitest run", "test:watch": "vitest --watch", "test:e2e": "npx cypress run --browser chrome", "test:e2e:open": "npx cypress open", "coverage": "vitest run --coverage", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install", "prerelease": "npm run build", "release": "release-it"}, "dependencies": {"@hercules/build-context": "3.2.8-beta.0", "@hercules/context": "^3.3.0", "@vee-validate/rules": "^4.12.5", "@vee-validate/yup": "^4.12.5", "@zeta-gds/components": "2.2.2", "@zeta-gds/themes.aphrodite": "1.10.0", "@zeta/har": "^1.1.6", "@zeta/icons": "^1.0.2", "@zeta/utils": "^1.1.5", "dayjs": "^1.11.9", "deepmerge": "^4.3.1", "dompurify": "^3.0.5", "dot-prop": "^8.0.2", "happy-dom": "^6.0.4", "json-logic-js": "^2.0.2", "lodash": "^4.17.21", "monaco-editor": "^0.33.0", "pluralize": "^8.0.0", "uuid": "^9.0.1", "vee-validate": "4.11.3", "vue": "~3.3", "vue3-webcomponent-wrapper": "^0.2.0"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.8", "@rushstack/eslint-patch": "^1.3.2", "@tsconfig/node18": "^18.2.0", "@types/dompurify": "^3.0.2", "@types/json-logic-js": "^2.0.2", "@types/lodash": "^4.14.197", "@types/node": "^18.17.0", "@types/pluralize": "^0.0.33", "@types/uuid": "^9.0.8", "@unplugin-vue-ce/sub-style": "^1.0.0-beta.16", "@vitejs/plugin-vue": "^5.0.3", "@vitest/coverage-istanbul": "^1.3.1", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.4.0", "compression": "^1.8.0", "cypress": "^13.13.0", "cypress-image-diff-js": "^2.1.4", "eslint": "^8.45.0", "eslint-plugin-cypress": "^2.15.2", "eslint-plugin-vue": "^9.15.1", "express": "^5.1.0", "fs-extra": "^9.1.0", "husky": "^8.0.3", "jsdom": "^26.1.0", "json-server": "0.16.3", "lint-staged": "^14.0.1", "npm-run-all": "^4.1.5", "prettier": "^3.0.0", "release-it": "^14.0.0", "sass": "^1.81.0", "typescript": "~5.1.6", "unplugin-vue-ce": "^1.0.0-beta.19", "vite": "^5.0.12", "vite-plugin-css-injected-by-js": "^3.3.1", "vite-plugin-dts": "^3.7.1", "vitest": "^1.3.1", "vue-tsc": "^1.8.6"}, "gitHooks": {}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["eslint --fix --ignore-path .gitignore", "prettier --write src/"]}}