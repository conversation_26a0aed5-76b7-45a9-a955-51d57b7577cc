<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Debug Tool</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .component-test {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 10px 0;
            min-height: 100px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔧 Angelos SDK Component Debug Tool</h1>

    <div class="debug-section">
        <h3>📊 SDK Status</h3>
        <div id="sdk-status">Loading...</div>
    </div>

    <div class="debug-section">
        <h3>🧪 Component Tests</h3>
        <button onclick="testCreateForm()">Test Create Form</button>
        <button onclick="testDataTable()">Test Data Table</button>
        <button onclick="checkCustomElements()">Check Custom Elements</button>
        <button onclick="clearTests()">Clear Tests</button>
        
        <div class="component-test" id="test-container">
            <p>Components will appear here...</p>
        </div>
    </div>

    <div class="debug-section">
        <h3>📝 Debug Log</h3>
        <div class="log" id="debug-log">Waiting for activity...</div>
    </div>

    <!-- Global configuration -->
    <script>
        window.__zeta__ = {
            angelos: {
                SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                OMS_SERVICE_BASE_URL: 'http://localhost:5000'
            }
        };
        window.__HERCULES__ = {
            $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
            $store: {}
        };

        // Enhanced logging
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Override console methods to capture all logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            log(args.join(' '), 'info');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            log(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            log(args.join(' '), 'warning');
            originalWarn.apply(console, args);
        };

        // Test functions
        async function testCreateForm() {
            log('🧪 Testing Create Form component...');
            
            const container = document.getElementById('test-container');
            const element = document.createElement('zwe-angelos-create-form-v3');
            
            // Set required attributes
            element.setAttribute('entity-id', 'test-entity');
            element.setAttribute('tenant-id', 'test-tenant');
            element.style.cssText = 'border: 1px solid #007bff; padding: 10px; margin: 5px 0;';
            
            container.appendChild(element);
            log('📦 Create form element added to DOM');
            
            // Check if it gets upgraded
            setTimeout(() => {
                const constructor = customElements.get('zwe-angelos-create-form-v3');
                if (constructor) {
                    log('✅ Custom element constructor found');
                    if (element instanceof constructor) {
                        log('✅ Element successfully upgraded!');
                    } else {
                        log('⚠️ Element not upgraded, attempting manual upgrade...');
                        customElements.upgrade(element);
                        
                        setTimeout(() => {
                            if (element instanceof constructor) {
                                log('✅ Manual upgrade successful!');
                            } else {
                                log('❌ Manual upgrade failed');
                            }
                        }, 500);
                    }
                } else {
                    log('❌ Custom element not defined');
                }
            }, 2000);
        }

        async function testDataTable() {
            log('🧪 Testing Data Table component...');
            
            const container = document.getElementById('test-container');
            const element = document.createElement('zwe-angelos-data-table-v3');
            
            element.setAttribute('entity-id', 'test-entity');
            element.setAttribute('tenant-id', 'test-tenant');
            element.style.cssText = 'border: 1px solid #28a745; padding: 10px; margin: 5px 0;';
            
            container.appendChild(element);
            log('📊 Data table element added to DOM');
        }

        function checkCustomElements() {
            log('🔍 Checking custom element registry...');
            
            const components = [
                'zwe-angelos-create-form-v3',
                'zwe-angelos-update-form-v3',
                'zwe-angelos-data-table-v3',
                'zwe-angelos-details-view-v3',
                'zwe-angelos-dynamic-view-v3'
            ];
            
            components.forEach(name => {
                const constructor = customElements.get(name);
                if (constructor) {
                    log(`✅ ${name} is defined`);
                } else {
                    log(`❌ ${name} is NOT defined`);
                }
            });
            
            // Check SDK status
            if (window.AngelosSDK) {
                const stats = window.AngelosSDK.getStats();
                log(`📊 SDK Stats: ${JSON.stringify(stats)}`);
            } else {
                log('❌ AngelosSDK not available');
            }
        }

        function clearTests() {
            const container = document.getElementById('test-container');
            container.innerHTML = '<p>Components will appear here...</p>';
            log('🧹 Test container cleared');
        }

        function updateStatus() {
            const statusDiv = document.getElementById('sdk-status');
            
            if (window.AngelosSDK) {
                const stats = window.AngelosSDK.getStats();
                statusDiv.innerHTML = `
                    <div class="status success">SDK Ready</div>
                    <p>Components loaded: ${stats.loadedComponents}/${stats.totalComponents}</p>
                    <p>Loading attempts: ${stats.loadingAttempts}</p>
                `;
            } else {
                statusDiv.innerHTML = '<div class="status warning">SDK Loading...</div>';
            }
        }

        // Check SDK status periodically
        setInterval(updateStatus, 1000);
        
        log('🚀 Debug tool initialized');
    </script>

    <!-- Load the SDK -->
    <script type="module" src="./dist/angelos.min.js"></script>
</body>
</html>
