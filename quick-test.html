<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Vue Component Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-area {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
        }
        .log {
            background: #f1f1f1;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Quick Vue Component Test</h1>
        <p>Testing if Vue custom elements render after the build fix...</p>

        <button onclick="testSimpleComponent()">Test Simple Vue Component</button>
        <button onclick="testPlainComponent()">Test Plain JS Component</button>
        <button onclick="clearTest()">Clear Test</button>

        <div id="test-area" class="test-area">
            <p><em>Components will appear here...</em></p>
        </div>

        <div id="log" class="log">Ready to test...\n</div>
    </div>

    <script src="./dist/angelos.min.js"></script>
    <script>
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logEl.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        async function waitForSDK() {
            let attempts = 0;
            while (attempts < 50) {
                if (window.AngelosSDK) {
                    return window.AngelosSDK;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            return null;
        }

        async function testSimpleComponent() {
            try {
                log('🚀 Starting simple Vue component test...', 'info');
                
                const sdk = await waitForSDK();
                if (!sdk) {
                    log('❌ AngelosSDK not available', 'error');
                    return;
                }
                log('✅ SDK loaded successfully', 'success');

                // Load the simple test component
                log('📦 Loading simple test component...', 'info');
                await sdk.debug.forceLoadAndUpgrade('zwe-test-simple-component');
                log('✅ Component loaded', 'success');

                // Create and add element
                const element = document.createElement('zwe-test-simple-component');
                const testArea = document.getElementById('test-area');
                testArea.innerHTML = '<h3>Simple Vue Component:</h3>';
                testArea.appendChild(element);
                
                log('🏗️ Component element created and added to DOM', 'info');

                // Check result
                setTimeout(() => {
                    const isDefined = customElements.get('zwe-test-simple-component');
                    const hasContent = element.innerHTML.length > 0;
                    
                    if (isDefined && hasContent) {
                        log('🎉 SUCCESS: Vue component rendered correctly!', 'success');
                        log(`   Component content: "${element.innerHTML}"`, 'success');
                    } else {
                        log('❌ FAILED: Vue component did not render', 'error');
                        log(`   Defined: ${isDefined ? 'YES' : 'NO'}`, 'error');
                        log(`   Has content: ${hasContent ? 'YES' : 'NO'}`, 'error');
                        log(`   Constructor: ${element.constructor.name}`, 'error');
                    }
                }, 500);

            } catch (error) {
                log(`❌ ERROR: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        async function testPlainComponent() {
            try {
                log('🚀 Starting plain JS component test...', 'info');
                
                const sdk = await waitForSDK();
                if (!sdk) {
                    log('❌ AngelosSDK not available', 'error');
                    return;
                }

                // Load the plain test component
                log('📦 Loading plain test component...', 'info');
                await sdk.debug.forceLoadAndUpgrade('zwe-plain-test-component');
                log('✅ Component loaded', 'success');

                // Create and add element
                const element = document.createElement('zwe-plain-test-component');
                const testArea = document.getElementById('test-area');
                testArea.innerHTML = '<h3>Plain JS Component:</h3>';
                testArea.appendChild(element);
                
                log('🏗️ Component element created and added to DOM', 'info');

                // Check result
                setTimeout(() => {
                    const isDefined = customElements.get('zwe-plain-test-component');
                    const hasContent = element.innerHTML.length > 0;
                    
                    if (isDefined && hasContent) {
                        log('✅ SUCCESS: Plain JS component rendered correctly!', 'success');
                        log(`   Component content: "${element.innerHTML}"`, 'success');
                    } else {
                        log('❌ FAILED: Plain JS component did not render', 'error');
                    }
                }, 500);

            } catch (error) {
                log(`❌ ERROR: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        function clearTest() {
            document.getElementById('test-area').innerHTML = '<p><em>Components will appear here...</em></p>';
            document.getElementById('log').innerHTML = 'Test cleared...\n';
        }

        // Initialize
        log('🎯 Quick test page loaded', 'info');
        log('📋 Click the test buttons to verify components work', 'info');
    </script>
</body>
</html>