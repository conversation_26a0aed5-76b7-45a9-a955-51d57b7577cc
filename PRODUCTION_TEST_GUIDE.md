# 🧪 Production Build Chunk Loading Test Guide

## 🚀 Quick Start

1. **Start the production test server:**
   ```bash
   node serve-prod.js
   ```

2. **Open your browser to:**
   ```
   http://localhost:8080                    (Clean test - no context errors)
   http://localhost:8080/prod-test-clean.html  (Same as above)
   http://localhost:8080/prod-test.html      (With context loading)
   ```

## ✅ **Issues Fixed:**

- ✅ **Favicon 404 error** - Fixed by copying favicon to dist/
- ✅ **setContextAndParams.js error** - Added null check for missing elements  
- ✅ **Context loading issues** - Created clean test without problematic dependencies

## 📊 What to Test

### 🔍 **Network Tab Monitoring**

Open **DevTools → Network Tab** and watch for these chunks to load dynamically:

| Action | Expected Chunks | Description |
|--------|----------------|-------------|
| **Page Load** | `angelos.min.js` (~85KB) | Only the main bundle loads initially |
| **Add Form** | `forms-*.js` (~0.44KB) | Form components chunk |
| **Add Table** | `data-table-*.js` (~0.36KB) | Data table chunk |
| **Add Details** | `details-view-*.js` (~0.36KB) | Details view chunk |
| **Monaco Editor** | `monaco-editor-*.js` (~3MB) | Code editor (if form has code fields) |

### 🎯 **Testing Steps**

1. **Clear Network Tab** (🗑️ button in DevTools)
2. **Refresh Page** - Watch only `angelos.min.js` load
3. **Click "Add Create Form"** - Watch `forms-*.js` load
4. **Click "Add Data Table"** - Watch `data-table-*.js` load  
5. **Click "Add Details View"** - Watch `details-view-*.js` load

### 📈 **Expected Behavior**

✅ **Correct (Lazy Loading Working):**
- Only `angelos.min.js` loads on page refresh
- Component chunks load only when components are added
- Each chunk loads only once (subsequent clicks don't reload)
- Page loads fast (~85KB initial vs 6.7MB before)

❌ **Incorrect (Lazy Loading Broken):**
- All chunks load immediately on page refresh
- Components don't work after chunk loading
- Multiple requests for same chunks

## 🔧 **Alternative Testing Methods**

### **Method 1: Using Python Simple Server**
```bash
# If you prefer Python
python3 -m http.server 8080
# Then visit: http://localhost:8080/prod-test.html
```

### **Method 2: Using npx serve**
```bash
# If you have serve installed
npx serve -p 8080
# Then visit: http://localhost:8080/prod-test.html
```

### **Method 3: Manual File Inspection**
```bash
# Check what chunks were built
ls -la dist/assets/

# Expected files:
# - data-table-*.js     (Data table chunk)
# - details-view-*.js   (Details view chunk) 
# - forms-*.js          (Forms chunk)
# - monaco-editor-*.js  (Monaco editor chunk)
# - vendor-*.js         (Core dependencies)
```

## 🐛 **Troubleshooting**

### **Problem: Chunks not loading**
- Check browser console for errors
- Verify `window.AngelosSDK` is available
- Check if server is serving files correctly

### **Problem: Components not appearing**
- Verify mock server is running (`json-server` on port 4000)
- Check entity configuration in mocks
- Look for JavaScript errors in console

### **Problem: All chunks load immediately**
- Check if Vite config has `inlineDynamicImports: true` (should be false)
- Verify entry points in `src/entries/` exist
- Check dynamic import statements in component loader

## 📋 **Production vs Development Comparison**

| Aspect | Development (`npm run dev`) | Production (`node serve-prod.js`) |
|--------|----------------------------|-----------------------------------|
| **Entry Point** | `/src/main.ts` | `/dist/angelos.min.js` |
| **Chunk Loading** | Vite dev server | Static file serving |
| **File Size** | Unminified | Minified & compressed |
| **Source Maps** | Yes | No |
| **Hot Reload** | Yes | No |

## 🎉 **Success Criteria**

Your lazy loading is working correctly if:

1. ✅ Initial page load is fast (~85KB)
2. ✅ Components load dynamically when clicked
3. ✅ Network tab shows chunks loading on-demand
4. ✅ No errors in browser console
5. ✅ Components render and function correctly
6. ✅ Each chunk loads only once per session

## 🔗 **Additional Resources**

- **Main bundle:** `dist/angelos.min.js`
- **Chunk directory:** `dist/assets/`
- **Component entries:** `src/entries/`
- **Dynamic loader:** `src/utils/dynamic-component-loader.ts`
