#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('📊 Angelos SDK Bundle Analysis - BASELINE BEFORE OPTIMIZATION\n');

const distDir = path.join(__dirname, 'dist');
const assetsDir = path.join(distDir, 'assets');

// Helper function to format file sizes
function formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Analyze main bundle
const mainBundle = path.join(distDir, 'angelos.min.js');
const mainFile = path.join(distDir, 'main-DUo1I8VF.mjs');
const vueShared = path.join(distDir, '__federation_shared_vue-C8RqAwCB.mjs');
const lodashShared = path.join(distDir, '__federation_shared_lodash-Ccg7QSq4.mjs');

let totalSize = 0;
let coreSize = 0;

console.log('🎯 CORE BUNDLE ANALYSIS:');
console.log('='.repeat(50));

if (fs.existsSync(mainBundle)) {
    const size = fs.statSync(mainBundle).size;
    totalSize += size;
    coreSize += size;
    console.log(`📦 angelos.min.js: ${formatSize(size)} (Entry point - just imports main)`);
}

if (fs.existsSync(mainFile)) {
    const size = fs.statSync(mainFile).size;
    totalSize += size;
    coreSize += size;
    console.log(`📦 main-DUo1I8VF.mjs: ${formatSize(size)} (MASSIVE - Contains everything!)`);
}

if (fs.existsSync(vueShared)) {
    const size = fs.statSync(vueShared).size;
    totalSize += size;
    coreSize += size;
    console.log(`📦 __federation_shared_vue: ${formatSize(size)} (Vue framework)`);
}

if (fs.existsSync(lodashShared)) {
    const size = fs.statSync(lodashShared).size;
    totalSize += size;
    coreSize += size;
    console.log(`📦 __federation_shared_lodash: ${formatSize(size)} (Lodash utilities)`);
}

console.log(`\n🔥 CORE BUNDLE TOTAL: ${formatSize(coreSize)}`);
console.log('⚠️  PROBLEM: Everything loads immediately on page load!\n');

// Analyze Monaco Editor files
console.log('🎨 MONACO EDITOR ANALYSIS:');
console.log('='.repeat(50));

let monacoSize = 0;
const monacoFiles = [];

// Check for Monaco workers in assets
let workers = [];
if (fs.existsSync(assetsDir)) {
    workers = fs
        .readdirSync(assetsDir)
        .filter((file) => file.includes('worker') && file.endsWith('.js'))
        .map((file) => {
            const filePath = path.join(assetsDir, file);
            const size = fs.statSync(filePath).size;
            monacoSize += size;
            totalSize += size;
            return { file, size };
        })
        .sort((a, b) => b.size - a.size);

    workers.forEach((worker) => {
        console.log(`⚡ ${worker.file}: ${formatSize(worker.size)}`);
    });
}

// Check for Monaco language files in root dist
const monacoLanguages = fs
    .readdirSync(distDir)
    .filter(
        (file) =>
            file.endsWith('.mjs') &&
            !file.includes('federation') &&
            !file.includes('main') &&
            !file.includes('commonjs')
    )
    .map((file) => {
        const filePath = path.join(distDir, file);
        const size = fs.statSync(filePath).size;
        monacoSize += size;
        totalSize += size;
        return { file, size };
    })
    .sort((a, b) => b.size - a.size);

console.log(`\n📝 Monaco Language Files (${monacoLanguages.length} files):`);
monacoLanguages.slice(0, 10).forEach((lang) => {
    console.log(`   ${lang.file}: ${formatSize(lang.size)}`);
});

if (monacoLanguages.length > 10) {
    console.log(`   ... and ${monacoLanguages.length - 10} more language files`);
}

console.log(`\n🎨 MONACO TOTAL: ${formatSize(monacoSize)}`);
console.log('⚠️  PROBLEM: All Monaco languages load even if not used!\n');

// Summary and optimization opportunities
console.log('📋 BASELINE SUMMARY:');
console.log('='.repeat(50));
console.log(`📦 Total Bundle Size: ${formatSize(totalSize)}`);
console.log(
    `📊 Core Bundle: ${formatSize(coreSize)} (${((coreSize / totalSize) * 100).toFixed(1)}%)`
);
console.log(
    `🎨 Monaco Editor: ${formatSize(monacoSize)} (${((monacoSize / totalSize) * 100).toFixed(1)}%)`
);

console.log('\n🎯 OPTIMIZATION OPPORTUNITIES:');
console.log('='.repeat(50));
console.log('1. 🚀 Implement lazy loading for components');
console.log('2. 🎨 Lazy load Monaco Editor only when code fields are used');
console.log('3. 📝 Load Monaco languages on-demand');
console.log('4. 🎯 Optimize icon loading (currently loads all icons)');
console.log('5. 📦 Split components into separate chunks');
console.log('6. 🗜️  Enable better compression and tree shaking');

console.log('\n🎯 EXPECTED IMPROVEMENTS:');
console.log('='.repeat(50));
console.log('• Initial load: ~50KB (vs current ~7.5MB)');
console.log('• Component chunks: Load on-demand (~100-500KB each)');
console.log('• Monaco: Load only when needed (~5MB)');
console.log('• Languages: Load specific languages only');

console.log('\n📝 NEXT STEPS:');
console.log('='.repeat(50));
console.log('1. Create component entry points for lazy loading');
console.log('2. Implement dynamic component loader');
console.log('3. Optimize Monaco Editor integration');
console.log('4. Configure advanced Vite optimizations');
console.log('5. Test and measure improvements');

// Save baseline data for comparison
const baselineData = {
    timestamp: new Date().toISOString(),
    totalSize,
    coreSize,
    monacoSize,
    files: {
        main: fs.existsSync(mainFile) ? fs.statSync(mainFile).size : 0,
        vue: fs.existsSync(vueShared) ? fs.statSync(vueShared).size : 0,
        lodash: fs.existsSync(lodashShared) ? fs.statSync(lodashShared).size : 0,
        monacoWorkers: workers.length,
        monacoLanguages: monacoLanguages.length
    }
};

fs.writeFileSync('baseline-metrics.json', JSON.stringify(baselineData, null, 2));
console.log('\n💾 Baseline metrics saved to baseline-metrics.json');
