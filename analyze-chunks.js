#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('📊 Angelos SDK Bundle Analysis\n');

const distDir = path.join(__dirname, 'dist');
const assetsDir = path.join(distDir, 'assets');

// Analyze main bundle
const mainBundle = path.join(distDir, 'angelos.min.js');
if (fs.existsSync(mainBundle)) {
    const mainSize = fs.statSync(mainBundle).size;
    console.log(`📦 Main Bundle:`);
    console.log(`   angelos.min.js: ${(mainSize / 1024).toFixed(2)} KB`);
    console.log(`   Load: Immediately on page load\n`);
}

// Analyze chunks
if (fs.existsSync(assetsDir)) {
    const chunks = fs.readdirSync(assetsDir)
        .filter(file => file.endsWith('.js'))
        .map(file => {
            const filePath = path.join(assetsDir, file);
            const size = fs.statSync(filePath).size;
            return { file, size };
        })
        .sort((a, b) => b.size - a.size);

    console.log(`🧩 Lazy-Loaded Chunks:`);
    
    const chunkInfo = {
        'forms-': '📝 Forms (Create/Update)',
        'data-table-': '📊 Data Tables',
        'details-view-': '👁️ Details View',
        'dynamic-view-': '🔄 Dynamic View',
        'monaco-editor-': '⚡ Monaco Code Editor',
        'vue-vendor-': '🖼️ Vue Framework',
        'ui-vendor-': '🎨 UI Components',
        'icons-vendor-': '🎯 Icon Library',
        'utils-vendor-': '🔧 Utilities',
        'vendor-': '📚 Core Dependencies',
        'web-component-factory-': '🏭 Component Factory'
    };

    chunks.forEach(chunk => {
        const size = (chunk.size / 1024).toFixed(2);
        const prefix = Object.keys(chunkInfo).find(p => chunk.file.startsWith(p));
        const description = prefix ? chunkInfo[prefix] : '❓ Unknown';
        
        console.log(`   ${chunk.file}`);
        console.log(`   ├─ Size: ${size} KB`);
        console.log(`   └─ Type: ${description}\n`);
    });
}

// Calculate total sizes
const totalChunks = fs.readdirSync(assetsDir)
    .filter(file => file.endsWith('.js'))
    .reduce((total, file) => {
        return total + fs.statSync(path.join(assetsDir, file)).size;
    }, 0);

const mainSize = fs.statSync(mainBundle).size;

console.log(`📈 Summary:`);
console.log(`   Initial Load: ${(mainSize / 1024).toFixed(2)} KB (just main bundle)`);
console.log(`   Total Available: ${((mainSize + totalChunks) / 1024 / 1024).toFixed(2)} MB (all chunks)`);
console.log(`   Lazy Loading Savings: ${(((totalChunks) / (mainSize + totalChunks)) * 100).toFixed(1)}% deferred`);

console.log(`\n🎯 Testing Instructions:`);
console.log(`   1. Start server: node serve-prod.js`);
console.log(`   2. Open: http://localhost:8080`);
console.log(`   3. Open DevTools → Network tab`);
console.log(`   4. Click component buttons and watch chunks load!`);
