import { test, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import DatePicker from '../../src/components/atomic/form-controls/DatePicker/DatePicker.vue';
import { defaultMountOptions } from '../test-utils';

test('renders with default props', async () => {
    const wrapper = mount(DatePicker, {
        ...defaultMountOptions,
        props: {
            formModelPath: 'datePicker',
            name: 'DATE WITH UTC TIMEZONE',
            label: 'DATE WITH UTC TIMEZONE',
            dateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            outputDateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            value: '2015-01-10T00:00:00.123+07:00',
            timezone: 'UTC'
        }
    });
    expect(wrapper.exists()).toBe(true);
});

// DEVNOTE: This test is failing because the resposibility to show label and description belongs to ZFormItem which has been moved to a separate component.
test.skip('renders with correct label and description', async () => {
    const label = 'DATE WITH UTC TIMEZONE';
    const description = 'TESTING DESCRIPTION';
    const wrapper = mount(DatePicker, {
        props: {
            formModelPath: 'datePicker',
            name: 'DATE WITH UTC TIMEZONE',
            label: label,
            description: description,
            dateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            outputDateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            value: '2015-01-10T00:00:00.123+07:00',
            timezone: 'UTC'
        }
    });
    expect(wrapper.find('.z-form-item-label__text').text()).toBe(label);
    expect(wrapper.find('.z-ellipsis').text()).toBe(description);
});

test('emits correct output with range picker', async () => {
    const label = 'DATE WITH DEFAULT TIMEZONE';
    const description = 'TESTING DESCRIPTION';

    // Mock the dateTransformer and modifyDate functions
    const wrapper = mount(DatePicker, {
        ...defaultMountOptions,
        props: {
            formModelPath: 'datePicker',
            name: 'DATE',
            label: label,
            description: description,
            type: 'daterange',
            minDate: '2024-02-20',
            maxDate: '2024-03-20',
            value: ['2024-02-26', '2024-03-10']
        }
    });

    // Wait for the next tick for the watcher to be triggered
    await wrapper.vm.$nextTick();

    // Check if the input event was emitted with the correct value
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0]).toEqual([
        ['2024-02-26T00:00:00+05:30', '2024-03-10T00:00:00+05:30'],
        'datePicker'
    ]);
});

test('emits correct output with default timezone', async () => {
    const label = 'DATE WITH DEFAULT TIMEZONE';
    const description = 'TESTING DESCRIPTION';

    // Mock the dateTransformer and modifyDate functions
    const wrapper = mount(DatePicker, {
        ...defaultMountOptions,
        props: {
            formModelPath: 'datePicker',
            name: 'DATE',
            label: label,
            description: description,
            outputDateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            value: '2022-01-01'
        }
    });

    // Wait for the next tick for the watcher to be triggered
    await wrapper.vm.$nextTick();

    // Check if the input event was emitted with the correct value
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0]).toEqual(['2022-01-01T00:00:00.000+05:30', 'datePicker']);
});

test('emits correct output with UTC timezone', async () => {
    const label = 'DATE WITH DEFAULT TIMEZONE';
    const description = 'TESTING DESCRIPTION';

    // Mock the dateTransformer and modifyDate functions
    const wrapper = mount(DatePicker, {
        ...defaultMountOptions,
        props: {
            formModelPath: 'datePicker',
            name: 'DATE',
            label: label,
            description: description,
            outputDateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            value: '2022-01-01',
            timezone: 'UTC'
        }
    });

    // Wait for the next tick for the watcher to be triggered
    await wrapper.vm.$nextTick();

    // Check if the input event was emitted with the correct value
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0]).toEqual(['2021-12-31T18:30:00.000+00:00', 'datePicker']);
});

test('emits correct output with system timezone', async () => {
    const label = 'DATE WITH DEFAULT TIMEZONE';
    const description = 'TESTING DESCRIPTION';

    // Mock the dateTransformer and modifyDate functions
    const wrapper = mount(DatePicker, {
        ...defaultMountOptions,
        props: {
            formModelPath: 'datePicker',
            name: 'DATE',
            label: label,
            description: description,
            outputDateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            value: '2022-01-01',
            timezone: 'SYSTEM'
        }
    });

    // Wait for the next tick for the watcher to be triggered
    await wrapper.vm.$nextTick();

    // Check if the input event was emitted with the correct value
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0]).toEqual(['2022-01-01T00:00:00.000+05:30', 'datePicker']);
});

test('emits correct output with preserve timezone', async () => {
    const label = 'DATE WITH DEFAULT TIMEZONE';
    const description = 'TESTING DESCRIPTION';

    // Mock the dateTransformer and modifyDate functions
    const wrapper = mount(DatePicker, {
        ...defaultMountOptions,
        props: {
            formModelPath: 'datePicker',
            name: 'DATE',
            label: label,
            description: description,
            outputDateFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
            value: '2022-01-01',
            timezone: 'PRESERVE'
        }
    });

    // Wait for the next tick for the watcher to be triggered
    await wrapper.vm.$nextTick();

    // Check if the input event was emitted with the correct value
    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0]).toEqual(['2022-01-01T00:00:00.000+05:30', 'datePicker']);
});
