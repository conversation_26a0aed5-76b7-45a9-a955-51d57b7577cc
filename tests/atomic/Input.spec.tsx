import { test, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import Input from '../../src/components/atomic/form-controls/Input.vue';
import { defaultMountOptions } from '../test-utils';

test('renders with multiple condition true', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            multiple: true,
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test'
        }
    });
    expect(wrapper.find('template[v-if="multiple"]').exists()).toBe(false);
});

test('renders with multiple condition false', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            multiple: false,
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test'
        }
    });
    expect(wrapper.find('ZFormItem-stub').exists()).toBe(false);
});

test('renders with disabled condition true', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            disabled: true,
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test'
        }
    });
    expect(wrapper.find('input[disabled]').exists()).toBe(true);
});

test('renders with disabled condition false', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            disabled: false,
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test'
        }
    });
    expect(wrapper.find('input[disabled]').exists()).toBe(false);
});

test('renders with different input types', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            type: 'number',
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test'
        }
    });
    expect(wrapper.find('input[type="number"]').exists()).toBe(true);
});

test('renders with prefix as a prop', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test',
            prefix: '$'
        }
    });
    expect(wrapper.html()).toContain('$');
});

test('renders with suffix as a prop', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            value: 'initial value',
            formFieldKey: 'test',
            formModelPath: 'test',
            suffix: 'kg'
        }
    });
    expect(wrapper.html()).toContain('kg');
});

test('renders with string value', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            value: 'test',
            formFieldKey: 'field',
            formModelPath: 'model.field'
        }
    });
    expect(wrapper.find('input').element.value).toBe('test');
});

test('renders with number value as string', async () => {
    const wrapper = mount(Input, {
        ...defaultMountOptions,
        props: {
            value: 123,
            formFieldKey: 'field',
            formModelPath: 'model.field',
            type: 'number'
        }
    });
    expect(wrapper.find('input').element.value).toBe(123);
    expect(wrapper.find('input').attributes('type')).toBe('number');
});
