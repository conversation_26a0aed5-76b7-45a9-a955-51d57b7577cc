import { useComponentStore, StoreKey } from '../src/composable/useComponentStore';

/**
 * Creates a mock store for testing component that need store injection
 */
export function createMockStore() {
    return useComponentStore('test-entity', 'test-tenant', 'create-form');
}

/**
 * Global provides object that can be used in Vue Test Utils mount
 */
export const globalProvides = {
    [StoreKey as symbol]: createMockStore()
};

/**
 * Default mount options with store provided
 */
export const defaultMountOptions = {
    global: {
        provide: globalProvides
    }
};
