<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Super Simple Test</title>
    </head>
    <body>
        <h1>🔍 Super Simple Test</h1>
        <p>Basic HTML is working.</p>
        
        <button onclick="testBasic()">Test Basic JS</button>
        <button onclick="testAngelosLoad()">Test Angelos Load</button>
        
        <div id="output">
            <p>Click buttons to test...</p>
        </div>
        
        <script>
            function log(message) {
                console.log(message);
                document.getElementById('output').innerHTML += '<div>' + message + '</div>';
            }
            
            function testBasic() {
                log('✅ Basic JavaScript is working');
            }
            
            function testAngelosLoad() {
                log('🔄 Attempting to load Angelos SDK...');
                
                // Try to load the script dynamically
                const script = document.createElement('script');
                script.type = 'module';
                script.src = './dist/angelos.min.js';
                script.onload = () => {
                    log('✅ Script loaded successfully');
                    setTimeout(() => {
                        if (window.AngelosSDK) {
                            log('✅ AngelosSDK is available');
                        } else {
                            log('❌ AngelosSDK is NOT available after load');
                        }
                    }, 1000);
                };
                script.onerror = (error) => {
                    log('❌ Script failed to load: ' + error);
                };
                document.head.appendChild(script);
            }
            
            log('📝 Page initialized');
        </script>
    </body>
</html>
