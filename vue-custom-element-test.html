<!DOCTYPE html>
<html>
<head>
    <title>Vue Custom Element Test</title>
</head>
<body>
    <h1>Vue Custom Element Test</h1>
    <pre id="log"></pre>
    <button onclick="runTest()">Run Test</button>
    <div id="test-area" style="border: 1px solid blue; padding: 10px; margin-top: 10px;"></div>
    
    <script type="module">
        function log(msg) {
            document.getElementById('log').textContent += new Date().toLocaleTimeString() + ': ' + msg + '\n';
            console.log(msg);
        }

        // Set up required globals
        window.__zeta__ = {
            angelos: { SERVICE_BASE_URL: 'https://test.com/', OMS_SERVICE_BASE_URL: 'http://localhost:5000' }
        };
        window.__HERCULES__ = { $assetsBaseUrl: 'https://test.com', $store: {} };
        localStorage.setItem('AT', 'test');
        
        window.runTest = async function() {
            try {
                log('Loading Vue vendor chunk...');
                
                // Import vue vendor first (this contains Vue and components)
                const vueVendor = await import('./dist/assets/vue-vendor-1FKnyJJB.js');
                log('Vue vendor loaded, keys: ' + Object.keys(vueVendor).slice(0, 10).join(', ') + '...');
                
                // Import web component factory
                const factory = await import('./dist/assets/web-component-factory-BIFTdB1i.js');
                log('Web component factory loaded, keys: ' + Object.keys(factory).join(', '));
                
                // Import forms chunk
                const forms = await import('./dist/assets/forms-Bl5yvngb.js');
                log('Forms chunk loaded, keys: ' + Object.keys(forms).join(', '));
                
                // Wait for custom element definition
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const isDefinedNow = customElements.get('zwe-angelos-create-form-v3');
                log('Custom element defined after all imports: ' + !!isDefinedNow);
                
                if (isDefinedNow) {
                    log('Creating component...');
                    const comp = document.createElement('zwe-angelos-create-form-v3');
                    comp.setAttribute('entity-id', 'test');
                    comp.setAttribute('tenant-id', '0');
                    
                    document.getElementById('test-area').appendChild(comp);
                    log('Component added');
                    
                    // Monitor component over time
                    let checks = 0;
                    const monitor = setInterval(() => {
                        checks++;
                        log(`Check ${checks}: constructor=${comp.constructor.name}, children=${comp.children.length}, innerHTML=${comp.innerHTML.length}`);
                        
                        if (checks >= 10) {
                            clearInterval(monitor);
                            log('Monitoring complete');
                        }
                    }, 1000);
                } else {
                    log('Custom element still not defined');
                }
                
            } catch (error) {
                log('Error: ' + error.message);
                console.error(error);
            }
        };
        
        log('Test ready');
    </script>
</body>
</html>
