<!DOCTYPE html>
<html>
<head>
    <title>Simple Component Test</title>
</head>
<body>
    <h1>Simple Component Test</h1>
    <div id="logs"></div>
    <button onclick="testComponent()">Test Component</button>
    <div id="component-area"></div>

    <script type="module" src="./dist/angelos.min.js"></script>
    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            logs.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        // Configure globals
        window.__zeta__ = {
            angelos: { SERVICE_BASE_URL: 'https://test.com/', OMS_SERVICE_BASE_URL: 'http://localhost:5000' }
        };
        window.__HERCULES__ = { $assetsBaseUrl: 'https://test.com', $store: {} };
        localStorage.setItem('AT', 'test');

        window.addEventListener('DOMContentLoaded', () => {
            log('DOM loaded');
            
            setTimeout(() => {
                if (window.AngelosSDK) {
                    log('SDK available: ' + JSON.stringify(window.AngelosSDK.getStats()));
                } else {
                    log('SDK not available');
                }
            }, 1000);
        });

        function testComponent() {
            log('Creating component...');
            const area = document.getElementById('component-area');
            const comp = document.createElement('zwe-angelos-create-form-v3');
            comp.setAttribute('entity-id', 'test');
            comp.setAttribute('tenant-id', '0');
            area.appendChild(comp);
            log('Component added to DOM');
            
            setTimeout(() => {
                log('After 2s - Constructor: ' + comp.constructor.name);
                log('After 2s - Children: ' + comp.children.length);
                log('After 2s - InnerHTML: ' + comp.innerHTML.length);
            }, 2000);
        }
    </script>
</body>
</html>
