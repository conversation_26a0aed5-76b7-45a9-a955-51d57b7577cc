/**
 * Angelos SDK - Main Entry Point with Lazy Loading
 *
 * This is the new optimized main entry that only loads components when needed.
 * Components are loaded dynamically when they appear in the DOM.
 */

import { initializeLazyLoading } from './core/lazy-loader';

console.log('🚀 Angelos SDK starting with lazy loading...');

// Initialize the lazy loading system
// This will:
// 1. Scan the DOM for existing components and load them
// 2. Set up observers to load components when they're added to DOM
// 3. Set up intersection observers for viewport-based loading
initializeLazyLoading();

console.log('✅ Angelos SDK initialized - components will load on demand');
