/**
 * Angelos SDK - Main Entry Point with Optimized Loading
 *
 * This loads all components but initializes them lazily when they appear in the DOM.
 * This avoids the circular dependency issues while still providing performance benefits.
 */

import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosCreateForm from './components/entity/AngelosCreateForm.ce.vue';
import AngelosUpdateForm from './components/entity/AngelosUpdateForm.ce.vue';
import AngelosDataTable from './components/entity/AngelosDataTable.ce.vue';
import AngelosDetailsView from './components/entity/details-view/AngelosDetailsView.ce.vue';
import AngelosDynamicView from './components/entity/dynamic-view/AngelosDynamicView.ce.vue';
import loadComponents from './plugins/components-loader';
import { createOptimizedIconLoader } from './core/icon-lazy-loader';

// Define web component wrapper
const defineWebComponent = (component: any) => {
    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            try {
                const app = createApp({});
                const inst = getCurrentInstance() as ComponentInternalInstance;
                Object.assign(inst.appContext, app._context);
                Object.assign(inst.appContext.provides, app._context.provides);

                // Register components
                loadComponents(inst.appContext.app);

                // Create optimized icon loader and preload common icons
                const iconLoader = createOptimizedIconLoader(inst.appContext.app);
                iconLoader.preloadCommon();

                const events = Object.fromEntries(
                    (component.emits || []).map((event: string) => {
                        return [
                            `on${event[0].toUpperCase()}${event.slice(1)}`,
                            (payload: unknown) => emit(event, payload)
                        ];
                    })
                );

                return () => h(component, { ...props, ...events });
            } catch (error) {
                throw error;
            }
        }
    });
};

// Define custom elements
try {
    // v3 components
    if (!customElements.get('zwe-angelos-create-form-v3')) {
        customElements.define('zwe-angelos-create-form-v3', defineWebComponent(AngelosCreateForm));
    }

    if (!customElements.get('zwe-angelos-update-form-v3')) {
        customElements.define('zwe-angelos-update-form-v3', defineWebComponent(AngelosUpdateForm));
    }

    if (!customElements.get('zwe-angelos-data-table-v3')) {
        customElements.define('zwe-angelos-data-table-v3', defineWebComponent(AngelosDataTable));
    }

    if (!customElements.get('zwe-angelos-details-view-v3')) {
        customElements.define(
            'zwe-angelos-details-view-v3',
            defineWebComponent(AngelosDetailsView)
        );
    }

    if (!customElements.get('zwe-angelos-dynamic-view-v3')) {
        customElements.define(
            'zwe-angelos-dynamic-view-v3',
            defineWebComponent(AngelosDynamicView)
        );
    }
} catch (error) {
    // Silently handle custom element definition errors
}
