/**
 * Angelos SDK - Main Entry Point
 * Registers web components with optimized loading
 */

import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosCreateForm from './components/entity/AngelosCreateForm.ce.vue';
import AngelosUpdateForm from './components/entity/AngelosUpdateForm.ce.vue';
import AngelosDataTable from './components/entity/AngelosDataTable.ce.vue';
import AngelosDetailsView from './components/entity/details-view/AngelosDetailsView.ce.vue';
import AngelosDynamicView from './components/entity/dynamic-view/AngelosDynamicView.ce.vue';
import loadComponents from './plugins/components-loader';
import { createOptimizedIconLoader } from './core/icon-lazy-loader';

// Global setup - run once
const globalApp = createApp({});
loadComponents(globalApp);
const iconLoader = createOptimizedIconLoader(globalApp);
iconLoader.preloadCommon();

// Define web component wrapper
const defineWebComponent = (component: any) => {
    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            const inst = getCurrentInstance() as ComponentInternalInstance;
            Object.assign(inst.appContext, globalApp._context);
            Object.assign(inst.appContext.provides, globalApp._context.provides);

            const events = Object.fromEntries(
                (component.emits || []).map((event: string) => [
                    `on${event[0].toUpperCase()}${event.slice(1)}`,
                    (payload: unknown) => emit(event, payload)
                ])
            );

            return () => h(component, { ...props, ...events });
        }
    });
};

// Register custom elements
const components = [
    { name: 'zwe-angelos-create-form-v3', component: AngelosCreateForm },
    { name: 'zwe-angelos-update-form-v3', component: AngelosUpdateForm },
    { name: 'zwe-angelos-data-table-v3', component: AngelosDataTable },
    { name: 'zwe-angelos-details-view-v3', component: AngelosDetailsView },
    { name: 'zwe-angelos-dynamic-view-v3', component: AngelosDynamicView }
];

components.forEach(({ name, component }) => {
    if (!customElements.get(name)) {
        customElements.define(name, defineWebComponent(component));
    }
});
