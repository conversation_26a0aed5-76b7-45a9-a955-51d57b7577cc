import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosCreateForm from './components/entity/AngelosCreateForm.ce.vue';
import AngelosDataTable from './components/entity/AngelosDataTable.ce.vue';
import AngelosUpdateForm from './components/entity/AngelosUpdateForm.ce.vue';
import AngelosDetailsView from './components/entity/details-view/AngelosDetailsView.ce.vue';
import AngelosDynamicView from './components/entity/dynamic-view/AngelosDynamicView.ce.vue';
import loadComponents from './plugins/components-loader';
import * as icons from '@zeta/icons';

const defineWebComponent = (component: any) => {
    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            const app = createApp({});
            const inst = getCurrentInstance() as ComponentInternalInstance;
            Object.assign(inst.appContext, app._context);
            Object.assign(inst.appContext.provides, app._context.provides);

            //Register components
            loadComponents(inst.appContext.app);

            //Register icons, TODO: Register the icons used by design team to reduce build size
            for (const [name, icon] of Object.entries(icons)) {
                app.component(name, icon);
            }
            const events = Object.fromEntries(
                (component.emits || []).map((event: string) => {
                    return [
                        `on${event[0].toUpperCase()}${event.slice(1)}`,
                        (payload: unknown) => emit(event, payload)
                    ];
                })
            );

            return () => h(component, { ...props, ...events });
        }
    });
};

customElements.define('zwe-angelos-data-table-v3', defineWebComponent(AngelosDataTable));
customElements.define('zwe-angelos-create-form-v3', defineWebComponent(AngelosCreateForm));
customElements.define('zwe-angelos-update-form-v3', defineWebComponent(AngelosUpdateForm));
customElements.define('zwe-angelos-details-view-v3', defineWebComponent(AngelosDetailsView));
customElements.define('zwe-angelos-dynamic-view-v3', defineWebComponent(AngelosDynamicView));
