/**
 * Angelos SDK - Main Entry Point with Optimized Loading
 *
 * This loads all components but initializes them lazily when they appear in the DOM.
 * This avoids the circular dependency issues while still providing performance benefits.
 */

import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosCreateForm from './components/entity/AngelosCreateForm.ce.vue';
import AngelosUpdateForm from './components/entity/AngelosUpdateForm.ce.vue';
import AngelosDataTable from './components/entity/AngelosDataTable.ce.vue';
import AngelosDetailsView from './components/entity/details-view/AngelosDetailsView.ce.vue';
import AngelosDynamicView from './components/entity/dynamic-view/AngelosDynamicView.ce.vue';
import loadComponents from './plugins/components-loader';
import { createOptimizedIconLoader } from './core/icon-lazy-loader';

console.log('🚀 Angelos SDK starting with optimized loading...');

// Define web component wrapper
const defineWebComponent = (component: any) => {
    console.log('🏗️ Creating web component wrapper for:', component.name || 'Unknown');

    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            console.log('🔧 Web component setup() called with props:', props);

            try {
                const app = createApp({});
                const inst = getCurrentInstance() as ComponentInternalInstance;
                Object.assign(inst.appContext, app._context);
                Object.assign(inst.appContext.provides, app._context.provides);

                console.log('🔧 Vue app context set up');

                // Register components
                console.log('🔧 Loading components...');
                loadComponents(inst.appContext.app);
                console.log('✅ Components loaded');

                // Create optimized icon loader and preload common icons
                console.log('🔧 Setting up icon loader...');
                const iconLoader = createOptimizedIconLoader(inst.appContext.app);
                iconLoader.preloadCommon();
                console.log('✅ Icon loader set up');

                const events = Object.fromEntries(
                    (component.emits || []).map((event: string) => {
                        return [
                            `on${event[0].toUpperCase()}${event.slice(1)}`,
                            (payload: unknown) => emit(event, payload)
                        ];
                    })
                );

                console.log('🔧 Events set up:', Object.keys(events));

                return () => {
                    console.log('🎨 Rendering component with props:', props);
                    return h(component, { ...props, ...events });
                };
            } catch (error) {
                console.error('❌ Error in web component setup:', error);
                throw error;
            }
        }
    });
};

// Define custom elements
console.log('🔧 Defining custom elements...');

try {
    // v3 components
    if (!customElements.get('zwe-angelos-create-form-v3')) {
        customElements.define('zwe-angelos-create-form-v3', defineWebComponent(AngelosCreateForm));
        console.log('✅ Defined zwe-angelos-create-form-v3');
    }

    if (!customElements.get('zwe-angelos-update-form-v3')) {
        customElements.define('zwe-angelos-update-form-v3', defineWebComponent(AngelosUpdateForm));
        console.log('✅ Defined zwe-angelos-update-form-v3');
    }

    if (!customElements.get('zwe-angelos-data-table-v3')) {
        customElements.define('zwe-angelos-data-table-v3', defineWebComponent(AngelosDataTable));
        console.log('✅ Defined zwe-angelos-data-table-v3');
    }

    if (!customElements.get('zwe-angelos-details-view-v3')) {
        customElements.define(
            'zwe-angelos-details-view-v3',
            defineWebComponent(AngelosDetailsView)
        );
        console.log('✅ Defined zwe-angelos-details-view-v3');
    }

    if (!customElements.get('zwe-angelos-dynamic-view-v3')) {
        customElements.define(
            'zwe-angelos-dynamic-view-v3',
            defineWebComponent(AngelosDynamicView)
        );
        console.log('✅ Defined zwe-angelos-dynamic-view-v3');
    }

    console.log('✅ All custom elements defined successfully');
} catch (error) {
    console.error('❌ Error defining custom elements:', error);
}

console.log('✅ Angelos SDK initialized - components ready for use');
