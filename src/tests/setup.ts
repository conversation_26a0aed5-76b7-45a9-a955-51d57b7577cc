/**
 * Test Setup File
 * Global configuration for all tests
 */

import { vi, afterEach } from 'vitest';

// Mock global objects that might not be available in test environment
Object.defineProperty(window, 'customElements', {
    value: {
        define: vi.fn(),
        get: vi.fn(),
        upgrade: vi.fn()
    },
    writable: true
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
}));

// Mock MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn()
}));

// Mock performance API
Object.defineProperty(window, 'performance', {
    value: {
        now: vi.fn(() => Date.now()),
        memory: {
            usedJSHeapSize: 1000000,
            totalJSHeapSize: 2000000,
            jsHeapSizeLimit: 4000000
        }
    },
    writable: true
});

// Mock console methods to avoid noise in tests
global.console = {
    ...console,
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

// Setup DOM environment
document.body.innerHTML = '';

// Clean up after each test
afterEach(() => {
    document.body.innerHTML = '';
    vi.clearAllMocks();
});
