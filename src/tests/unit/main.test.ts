/**
 * Unit Tests for Main Entry Point
 * Tests the main SDK initialization and component registration
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock Vue
const mockVue = {
    createApp: vi.fn().mockReturnValue({
        _context: { provides: {} }
    }),
    defineCustomElement: vi.fn().mockImplementation((config) => {
        return class MockCustomElement extends HTMLElement {
            public config: any;
            constructor() {
                super();
                this.config = config;
            }
        };
    }),
    getCurrentInstance: vi.fn().mockReturnValue({
        appContext: { app: {}, provides: {} }
    }),
    h: vi.fn().mockReturnValue({ type: 'div', children: [] })
};

// Mock components
const mockComponents = {
    AngelosCreateForm: { name: 'AngelosCreateForm', emits: ['submit', 'cancel'] },
    AngelosUpdateForm: { name: 'AngelosUpdateForm', emits: ['update', 'cancel'] },
    AngelosDataTable: { name: 'AngelosDataTable', emits: ['row-click', 'sort'] },
    AngelosDetailsView: { name: 'AngelosDetailsView', emits: ['edit'] },
    AngelosDynamicView: { name: 'AngelosDynamicView', emits: ['action'] }
};

// Mock plugins
const mockLoadComponents = vi.fn();
const mockCreateOptimizedIconLoader = vi.fn().mockReturnValue({
    preloadCommon: vi.fn().mockResolvedValue(undefined)
});

// Apply mocks
vi.mock('vue', () => mockVue);
vi.mock('../components/entity/AngelosCreateForm.ce.vue', () => ({
    default: mockComponents.AngelosCreateForm
}));
vi.mock('../components/entity/AngelosUpdateForm.ce.vue', () => ({
    default: mockComponents.AngelosUpdateForm
}));
vi.mock('../components/entity/AngelosDataTable.ce.vue', () => ({
    default: mockComponents.AngelosDataTable
}));
vi.mock('../components/entity/details-view/AngelosDetailsView.ce.vue', () => ({
    default: mockComponents.AngelosDetailsView
}));
vi.mock('../components/entity/dynamic-view/AngelosDynamicView.ce.vue', () => ({
    default: mockComponents.AngelosDynamicView
}));
vi.mock('../plugins/components-loader', () => ({ default: mockLoadComponents }));
vi.mock('../core/icon-lazy-loader', () => ({
    createOptimizedIconLoader: mockCreateOptimizedIconLoader
}));

describe('Main SDK Entry Point', () => {
    let originalCustomElements: any;
    let mockCustomElements: any;

    beforeEach(() => {
        // Mock customElements
        originalCustomElements = global.customElements;
        mockCustomElements = {
            define: vi.fn(),
            get: vi.fn().mockReturnValue(undefined)
        };
        global.customElements = mockCustomElements;

        // Clear console mocks
        vi.clearAllMocks();

        // Mock console methods
        vi.spyOn(console, 'log').mockImplementation(() => {});
        vi.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
        global.customElements = originalCustomElements;
        vi.restoreAllMocks();
    });

    describe('SDK Initialization', () => {
        it('should initialize SDK with correct console messages', async () => {
            const consoleSpy = vi.spyOn(console, 'log');

            // Import main to trigger initialization
            await import('../../main');

            expect(consoleSpy).toHaveBeenCalledWith(
                '🚀 Angelos SDK starting with optimized loading...'
            );
            expect(consoleSpy).toHaveBeenCalledWith(
                '✅ Angelos SDK initialized - components ready for use'
            );
        });

        it('should create web component wrappers for all components', async () => {
            await import('../../main');

            expect(mockVue.defineCustomElement).toHaveBeenCalledTimes(5);

            // Verify each component wrapper was created
            const calls = mockVue.defineCustomElement.mock.calls;
            expect(
                calls.some((call) => call[0].emits === mockComponents.AngelosCreateForm.emits)
            ).toBe(true);
            expect(
                calls.some((call) => call[0].emits === mockComponents.AngelosUpdateForm.emits)
            ).toBe(true);
            expect(
                calls.some((call) => call[0].emits === mockComponents.AngelosDataTable.emits)
            ).toBe(true);
        });
    });

    describe('Custom Element Registration', () => {
        it('should register all v3 custom elements', async () => {
            await import('../../main');

            expect(mockCustomElements.define).toHaveBeenCalledWith(
                'zwe-angelos-create-form-v3',
                expect.any(Function)
            );
            expect(mockCustomElements.define).toHaveBeenCalledWith(
                'zwe-angelos-update-form-v3',
                expect.any(Function)
            );
            expect(mockCustomElements.define).toHaveBeenCalledWith(
                'zwe-angelos-data-table-v3',
                expect.any(Function)
            );
            expect(mockCustomElements.define).toHaveBeenCalledWith(
                'zwe-angelos-details-view-v3',
                expect.any(Function)
            );
            expect(mockCustomElements.define).toHaveBeenCalledWith(
                'zwe-angelos-dynamic-view-v3',
                expect.any(Function)
            );
        });

        it('should not redefine already existing custom elements', async () => {
            mockCustomElements.get.mockReturnValue(class ExistingElement {});

            await import('../../main');

            // Should not call define for existing elements
            expect(mockCustomElements.define).not.toHaveBeenCalled();
        });

        it('should handle custom element definition errors gracefully', async () => {
            const consoleError = vi.spyOn(console, 'error');
            mockCustomElements.define.mockImplementation(() => {
                throw new Error('Custom element already defined');
            });

            await import('../../main');

            expect(consoleError).toHaveBeenCalledWith(
                '❌ Error defining custom elements:',
                expect.any(Error)
            );
        });
    });

    describe('Component Setup', () => {
        it('should load components plugin for each wrapper', async () => {
            await import('../../main');

            // Should be called during web component setup
            expect(mockLoadComponents).toHaveBeenCalled();
        });

        it('should create and configure icon loader', async () => {
            await import('../../main');

            expect(mockCreateOptimizedIconLoader).toHaveBeenCalled();
        });

        it('should preload common icons', async () => {
            await import('../../main');

            const iconLoader = mockCreateOptimizedIconLoader.mock.results[0].value;
            expect(iconLoader.preloadCommon).toHaveBeenCalled();
        });
    });

    describe('Web Component Wrapper', () => {
        it('should create wrapper with correct configuration', async () => {
            await import('../../main');

            const wrapperConfig = mockVue.defineCustomElement.mock.calls[0][0];
            expect(wrapperConfig).toHaveProperty('emits');
            expect(wrapperConfig).toHaveProperty('setup');
            expect(typeof wrapperConfig.setup).toBe('function');
        });

        it('should handle component events correctly', async () => {
            await import('../../main');

            const wrapperConfig = mockVue.defineCustomElement.mock.calls[0][0];
            const mockEmit = vi.fn();
            const mockProps = { 'entity-id': 'test' };

            // Call setup function
            const setupResult = wrapperConfig.setup(mockProps, { emit: mockEmit });
            expect(typeof setupResult).toBe('function');
        });
    });

    describe('Error Handling', () => {
        it('should handle Vue app creation errors', async () => {
            const consoleError = vi.spyOn(console, 'error');
            mockVue.createApp.mockImplementation(() => {
                throw new Error('Vue app creation failed');
            });

            await import('../../main');

            expect(consoleError).toHaveBeenCalled();
        });

        it('should handle component loading errors', async () => {
            const consoleError = vi.spyOn(console, 'error');
            mockLoadComponents.mockImplementation(() => {
                throw new Error('Component loading failed');
            });

            await import('../../main');

            expect(consoleError).toHaveBeenCalled();
        });
    });
});
