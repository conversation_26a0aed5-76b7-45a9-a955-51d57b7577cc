/**
 * Unit Tests for Monaco Lazy Loader
 * Tests the lazy loading functionality for Monaco Editor
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { MonacoLazyLoader } from '../../core/monaco-lazy-loader';

// Mock Monaco Editor
const mockMonaco = {
    editor: {
        create: vi.fn().mockReturnValue({
            dispose: vi.fn(),
            getValue: vi.fn().mockReturnValue('test code'),
            setValue: vi.fn(),
            getModel: vi.fn().mockReturnValue({
                getLanguageId: vi.fn().mockReturnValue('javascript')
            })
        }),
        defineTheme: vi.fn(),
        setTheme: vi.fn()
    },
    languages: {
        register: vi.fn(),
        setMonarchTokensProvider: vi.fn()
    }
};

// Mock dynamic import - use vi.hoisted to ensure it runs before imports
vi.mock('monaco-editor', () => mockMonaco);

// Mock the dynamic import function
vi.mock('../../core/monaco-lazy-loader', async () => {
    const actual = await vi.importActual('../../core/monaco-lazy-loader');
    return {
        ...actual,
        MonacoLazyLoader: {
            createEditor: vi.fn().mockResolvedValue(mockMonaco.editor.create()),
            getStats: vi.fn().mockReturnValue({
                coreLoaded: false,
                editorsCreated: 0,
                languagesLoaded: 0,
                loadingAttempts: 0
            }),
            preloadCommonLanguages: vi.fn().mockResolvedValue(undefined),
            isReady: vi.fn().mockReturnValue(false),
            loadLanguage: vi.fn().mockResolvedValue(undefined)
        }
    };
});

describe('Monaco Lazy Loader', () => {
    let container: HTMLElement;
    let loader: any;

    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);

        // Reset mocks
        vi.clearAllMocks();

        // Reset global state
        (global as any).MonacoLazyLoader = undefined;

        loader = MonacoLazyLoader;
    });

    afterEach(() => {
        document.body.removeChild(container);
        vi.clearAllMocks();
    });

    describe('Initialization', () => {
        it('should create MonacoLazyLoader global object', () => {
            expect((global as any).MonacoLazyLoader).toBeDefined();
            expect((global as any).MonacoLazyLoader.createEditor).toBeInstanceOf(Function);
            expect((global as any).MonacoLazyLoader.getStats).toBeInstanceOf(Function);
        });

        it('should initialize with correct default stats', () => {
            const stats = loader.getStats();
            expect(stats).toEqual({
                coreLoaded: false,
                editorsCreated: 0,
                languagesLoaded: 0,
                loadingAttempts: 0
            });
        });
    });

    describe('Editor Creation', () => {
        it('should create editor with default options', async () => {
            const editor = await loader.createEditor(container);

            expect(mockMonaco.editor.create).toHaveBeenCalledWith(
                container,
                expect.objectContaining({
                    value: '',
                    language: 'javascript',
                    theme: 'vs-dark',
                    automaticLayout: true
                })
            );

            expect(editor).toBeDefined();
        });

        it('should create editor with custom options', async () => {
            const options = {
                value: 'console.log("test");',
                language: 'typescript',
                theme: 'vs-light'
            };

            await loader.createEditor(container, options);

            expect(mockMonaco.editor.create).toHaveBeenCalledWith(
                container,
                expect.objectContaining(options)
            );
        });

        it('should update stats after creating editor', async () => {
            await loader.createEditor(container);

            const stats = loader.getStats();
            expect(stats.coreLoaded).toBe(true);
            expect(stats.editorsCreated).toBe(1);
            expect(stats.loadingAttempts).toBe(1);
        });

        it('should handle multiple editor creation', async () => {
            const container2 = document.createElement('div');
            document.body.appendChild(container2);

            await loader.createEditor(container);
            await loader.createEditor(container2);

            const stats = loader.getStats();
            expect(stats.editorsCreated).toBe(2);
            expect(stats.loadingAttempts).toBe(1); // Core loaded only once

            document.body.removeChild(container2);
        });
    });

    describe('Language Support', () => {
        it('should support common programming languages', async () => {
            const languages = ['javascript', 'typescript', 'python', 'java', 'csharp'];

            for (const language of languages) {
                await loader.createEditor(container, { language });
                expect(mockMonaco.editor.create).toHaveBeenCalledWith(
                    container,
                    expect.objectContaining({ language })
                );
            }
        });
    });

    describe('Error Handling', () => {
        it('should handle Monaco loading failure gracefully', async () => {
            // Mock import failure
            vi.doMock('monaco-editor', () => {
                throw new Error('Failed to load Monaco');
            });

            const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

            const editor = await loader.createEditor(container);

            expect(editor).toBeNull();
            expect(consoleError).toHaveBeenCalledWith(
                expect.stringContaining('Failed to load Monaco Editor'),
                expect.any(Error)
            );

            consoleError.mockRestore();
        });

        it('should handle invalid container', async () => {
            const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

            const editor = await loader.createEditor(null as any);

            expect(editor).toBeNull();
            expect(consoleError).toHaveBeenCalled();

            consoleError.mockRestore();
        });
    });

    describe('Performance Tracking', () => {
        it('should track loading performance', async () => {
            const startTime = Date.now();
            await loader.createEditor(container);
            const endTime = Date.now();

            const stats = loader.getStats();
            expect(stats.coreLoaded).toBe(true);
            expect(stats.loadingAttempts).toBe(1);

            // Should complete within reasonable time (less than 1 second in tests)
            expect(endTime - startTime).toBeLessThan(1000);
        });
    });
});
