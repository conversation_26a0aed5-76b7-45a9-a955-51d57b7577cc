/**
 * Unit Tests for Icon Lazy Loader
 * Tests the lazy loading functionality for Zeta Icons
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createOptimizedIconLoader } from '../../core/icon-lazy-loader';
import { createApp } from 'vue';

// Mock Zeta Icons
const mockIcons = {
    Add: { name: 'Add' },
    Edit: { name: 'Edit' },
    Delete: { name: 'Delete' },
    Save: { name: 'Save' },
    Cancel: { name: 'Cancel' }
};

vi.mock('@zeta/icons', () => mockIcons);

describe('Icon Lazy Loader', () => {
    let app: any;
    let loader: any;

    beforeEach(() => {
        app = createApp({});
        vi.clearAllMocks();
        
        // Reset global state
        (global as any).IconLazyLoader = undefined;
        
        loader = createOptimizedIconLoader(app);
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Initialization', () => {
        it('should create IconLazyLoader global object', () => {
            expect((global as any).IconLazyLoader).toBeDefined();
            expect((global as any).IconLazyLoader.getStats).toBeInstanceOf(Function);
            expect((global as any).IconLazyLoader.registerIconsOnDemand).toBeInstanceOf(Function);
        });

        it('should initialize with correct default stats', () => {
            const stats = loader.getStats();
            expect(stats).toEqual({
                loaded: 0,
                preloaded: 0,
                onDemandLoaded: 0,
                scanAttempts: 0
            });
        });
    });

    describe('Preloading', () => {
        it('should preload common icons', async () => {
            await loader.preloadCommon();
            
            const stats = loader.getStats();
            expect(stats.preloaded).toBeGreaterThan(0);
            expect(stats.loaded).toBeGreaterThan(0);
        });

        it('should not preload icons multiple times', async () => {
            await loader.preloadCommon();
            const firstStats = loader.getStats();
            
            await loader.preloadCommon();
            const secondStats = loader.getStats();
            
            expect(secondStats.preloaded).toBe(firstStats.preloaded);
        });
    });

    describe('On-Demand Loading', () => {
        it('should register icons on demand', async () => {
            const iconNames = ['Add', 'Edit', 'Delete'];
            
            await loader.registerIconsOnDemand(app, iconNames);
            
            const stats = loader.getStats();
            expect(stats.onDemandLoaded).toBe(iconNames.length);
            expect(stats.loaded).toBeGreaterThanOrEqual(iconNames.length);
        });

        it('should handle duplicate icon registration', async () => {
            const iconNames = ['Add', 'Add', 'Edit'];
            
            await loader.registerIconsOnDemand(app, iconNames);
            
            const stats = loader.getStats();
            // Should only load unique icons
            expect(stats.onDemandLoaded).toBe(2);
        });

        it('should handle invalid icon names gracefully', async () => {
            const consoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {});
            
            const iconNames = ['ValidIcon', 'InvalidIcon', 'Add'];
            await loader.registerIconsOnDemand(app, iconNames);
            
            // Should continue processing valid icons
            const stats = loader.getStats();
            expect(stats.onDemandLoaded).toBeGreaterThan(0);
            
            consoleWarn.mockRestore();
        });
    });

    describe('DOM Scanning', () => {
        it('should scan DOM for icon usage', () => {
            // Create mock DOM with icon usage
            document.body.innerHTML = `
                <div>
                    <zeta-icon name="Add"></zeta-icon>
                    <zeta-icon name="Edit"></zeta-icon>
                    <span class="icon-save"></span>
                </div>
            `;
            
            loader.scanAndLoadIcons();
            
            const stats = loader.getStats();
            expect(stats.scanAttempts).toBe(1);
            
            // Clean up
            document.body.innerHTML = '';
        });

        it('should handle empty DOM gracefully', () => {
            document.body.innerHTML = '';
            
            loader.scanAndLoadIcons();
            
            const stats = loader.getStats();
            expect(stats.scanAttempts).toBe(1);
        });
    });

    describe('Performance Optimization', () => {
        it('should batch icon loading', async () => {
            const iconNames = ['Add', 'Edit', 'Delete', 'Save', 'Cancel'];
            
            const startTime = Date.now();
            await loader.registerIconsOnDemand(app, iconNames);
            const endTime = Date.now();
            
            const stats = loader.getStats();
            expect(stats.onDemandLoaded).toBe(iconNames.length);
            
            // Should complete quickly (batched loading)
            expect(endTime - startTime).toBeLessThan(100);
        });

        it('should avoid loading already loaded icons', async () => {
            await loader.registerIconsOnDemand(app, ['Add', 'Edit']);
            const firstStats = loader.getStats();
            
            await loader.registerIconsOnDemand(app, ['Add', 'Delete']);
            const secondStats = loader.getStats();
            
            // Should only load the new icon (Delete)
            expect(secondStats.onDemandLoaded - firstStats.onDemandLoaded).toBe(1);
        });
    });

    describe('Error Handling', () => {
        it('should handle icon loading failures gracefully', async () => {
            const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
            
            // Mock import failure
            vi.doMock('@zeta/icons', () => {
                throw new Error('Failed to load icons');
            });
            
            await loader.registerIconsOnDemand(app, ['Add']);
            
            expect(consoleError).toHaveBeenCalled();
            
            consoleError.mockRestore();
        });

        it('should handle invalid app instance', async () => {
            const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
            
            await loader.registerIconsOnDemand(null, ['Add']);
            
            expect(consoleError).toHaveBeenCalled();
            
            consoleError.mockRestore();
        });
    });
});
