import { getProperty, hasProperty } from 'dot-prop';
import deepmerge from 'deepmerge';
import dayjs from 'dayjs';
import lodash, { isString, cloneDeep } from 'lodash-es';
import { HAR } from '@zeta/har';
import { HERCULES_ASSETS_DOMAIN_KEY, ERROR_MESSAGES, DEFAULT_ERROR } from './constants';
import { extendedJsonLogic } from './json-logic';
// import * as yup from 'yup';
import type {
    ErrorStructure,
    RuleConfig,
    RuleValue,
    TableInputFilter,
    ValidationRuleType
} from '@/types';
import { inject, ref, type Ref } from 'vue';
import { createFilterItemSelector, filterItemInjectionKey, ZInput } from '@zeta-gds/components';

export const getAssetUrl = (assetPath: string, version?: string) => {
    if (!hasProperty(window, HERCULES_ASSETS_DOMAIN_KEY)) {
        console.error('__HERCULES__.$assetsBaseUrl not found. Using fallback domain');
    }

    const assetBaseUrl = getProperty(
        window,
        HERCULES_ASSETS_DOMAIN_KEY,
        'https://hercules-assets.mum1-pp.zetaapps.in'
    );

    const baseUrl = `${assetBaseUrl}/common-assets`;
    if (version) {
        return `${baseUrl}/${version}/${assetPath}`;
    }

    return `${baseUrl}/${assetPath}`;
};

export const isValidJsonLogic = (
    logic: object | string,
    context: ComponentInputContext,
    additionalData: any = {}
) => {
    const _logic = typeof logic === 'string' ? JSON.parse(logic) : logic;
    return extendedJsonLogic.apply(_logic, Object.assign({}, context, additionalData));
};

interface ArgumentsMap {
    [k: string]: any;
}

export const executeTransformerFunction = (body: string, _argsMap: ArgumentsMap) => {
    const argsMap = deepmerge({}, _argsMap);
    Object.assign(argsMap, { dayjs, lodash });
    const argNames = Object.keys(argsMap);
    const argData = argNames.map((argKey) => argsMap[argKey]);
    if (Array.isArray(argNames) && typeof body === 'string') {
        try {
            return new Function(...argNames, body)(...argData);
        } catch (error) {
            console.error('Error: Executing transformer function', error);
        }
    }
};
// this will mutate the formModel
export const executeEventFunction = (body: string, _argsMap: ArgumentsMap) => {
    const argsMap = deepmerge({}, _argsMap);
    Object.assign(argsMap, { dayjs, lodash });
    // keeping formModel as a same reference
    argsMap.formModel = argsMap.formModel || {};
    const argNames = Object.keys(argsMap);
    const argData = argNames.map((argKey) => argsMap[argKey]);
    if (Array.isArray(argNames) && typeof body === 'string') {
        try {
            const updatedFormModelKeys = new Function(...argNames, body)(...argData);
            return updatedFormModelKeys;
        } catch (error) {
            console.error('Error: Executing Event function', error);
        }
    }
};

interface ComponentInputContext {
    tenantId: string;
    entityId: string;
    context: any;
    params: any;
}

type ChainDataConfigOptions = {
    configList: any[];
    context: ComponentInputContext;
    requestData: any;
    executeResponseTransformer?: boolean;
};
/**
 * Function to create a filter selector for the input
 * @param filterConfig
 * @param key
 */
export function createInputFilterSelector(filterConfig: TableInputFilter, key: string) {
    const compareFn = filterConfig.selectors[key].compareFn;
    const placeholder = filterConfig.selectors[key].placeholder ?? 'Enter Text';
    createFilterItemSelector(key, {
        label: filterConfig.selectors[key].label ?? 'By Input',
        component: {
            props: {
                value: [String, Array]
            },
            components: {
                ZInput
            },
            setup(props: any) {
                const Filter = inject(filterItemInjectionKey, null);
                const value = ref<string | string[] | null>(
                    Array.isArray(props.value) ? props.value : [props.value]
                );
                return {
                    value,
                    handler(data: any) {
                        Filter?.updateValue(data.trim());
                    }
                };
            },
            template: `
            <div style="padding:16px; width:300px"> <z-input v-model="value" @update:modelValue="handler" placeholder="${placeholder}" /></div>
            `
        },
        filterFn: (field, filter, rowData, model) => {
            // If there is apply function then that would be used to filter the data
            if (compareFn) {
                return executeTransformerFunction(compareFn, {
                    field,
                    filter,
                    rowData,
                    model
                });
            } else {
                // By default we will do a simple string match
                return String(rowData[field])
                    .toLowerCase()
                    .includes(String(model.value).toLowerCase());
            }
        }
    });
}

export const useDataStore = () => {
    const store = new Map<number, any>();
    const setData = (page: number, data: any) => {
        store.set(page, data);
    };
    const getAllData = () => {
        return Object.fromEntries(store);
    };
    const getDataForPage = (page: number) => {
        return store.get(page);
    };
    const clearData = () => {
        store.clear();
    };
    return {
        setData,
        getAllData,
        getDataForPage,
        clearData
    };
};
export const chainDataConfigs: (options: ChainDataConfigOptions) => Promise<any> = async ({
    configList,
    context,
    requestData,
    executeResponseTransformer = false
}) => {
    const params: any = deepmerge({}, { ...requestData, ...context });
    const errors = [];

    for (let idx = 0; idx < configList.length; idx++) {
        const invokeIfCondition: string | object | undefined = getProperty(
            configList[idx],
            'condition.invokeIf'
        );

        if (typeof invokeIfCondition !== 'undefined') {
            if (!isValidJsonLogic(invokeIfCondition, context, { ...params })) {
                continue;
            }
        }

        const config: any = deepmerge({}, configList[idx]);

        try {
            if (config.iterator && (config.iteratorField || config.iteratorFieldFn)) {
                const iteratorArray: any[] = (() => {
                    if (
                        config.iteratorField &&
                        hasProperty(params, config.iteratorField) &&
                        Array.isArray(getProperty(params, config.iteratorField))
                    ) {
                        return getProperty(params, config.iteratorField, []);
                    }

                    const iteratorFieldFn =
                        executeTransformerFunction(config.iteratorFieldFn, params) || [];

                    return iteratorFieldFn;
                })();

                const res = await Promise.all(
                    iteratorArray.map((item, idx) => {
                        let transformedRequest = {};
                        if (typeof config.requestBodyTransformer === 'string') {
                            const req = executeTransformerFunction(config.requestBodyTransformer, {
                                ...params,
                                $iterator: {
                                    key: idx,
                                    value: item
                                }
                            });

                            if (typeof req === 'object') {
                                transformedRequest = req;
                            }
                        }

                        if (config.httpRequest) {
                            new HAR(config.httpRequest).send({
                                ...transformedRequest,
                                ...params,
                                $iterator: {
                                    key: idx,
                                    value: item
                                }
                            });
                        }
                    })
                );

                if (config.refId) {
                    params[config.refId] =
                        config.responseTransformer && executeResponseTransformer
                            ? executeTransformerFunction(config.responseTransformer, {
                                  res,
                                  ...params
                              })
                            : res;
                }
            } else {
                let transformedRequest = {};
                if (typeof config.requestBodyTransformer === 'string') {
                    const req = executeTransformerFunction(config.requestBodyTransformer, params);

                    if (typeof req === 'object') {
                        transformedRequest = req;
                    }
                }

                let res;
                if (config.httpRequest) {
                    res = await new HAR(config.httpRequest).send({
                        ...transformedRequest,
                        ...params
                    });
                }

                if (config.refId) {
                    params[config.refId] =
                        config.responseTransformer && executeResponseTransformer
                            ? executeTransformerFunction(config.responseTransformer, {
                                  res,
                                  ...params
                              })
                            : res;
                }
            }
        } catch (_error) {
            const error = _error as any;
            const errorResponse = error.errorResponse
                ? {
                      status: error.errorResponse.status,
                      statusText: error.errorResponse.statusText,
                      type: error.errorResponse.type,
                      url: error.errorResponse.url,
                      response: await error.errorResponse.json()
                  }
                : error;

            const errorData = {
                refId: config.refId,
                config,
                index: idx,
                error: errorResponse
            };

            if (config.ignoreFailure) {
                errors.push(errorData);
            } else {
                return Promise.reject(errorData);
            }
        }
    }

    return { response: params, errors };
};

export function executeJsonLogic(
    field: any,
    propToCheck: string,
    context: ComponentInputContext,
    defaultValue: any,
    additionalData: object = {}
) {
    if (field && field.condition && field.condition[propToCheck]) {
        return isValidJsonLogic(field.condition[propToCheck], context, { ...additionalData });
    }
    return defaultValue;
}

export function isArrayOfStrings(data: any) {
    if (!Array.isArray(data)) return false;
    return data.filter((item) => typeof item !== 'string').length === 0;
}

export function reduceArrayToObject(data: any[]) {
    return Object.entries(data).reduce((prev, [_, v]) => {
        return Object.assign(prev, v);
    }, {});
}

export const coerceArray = (params: unknown | unknown[]) => {
    return Array.isArray(params) ? params : [params];
};

export const overwriteMerge = (target: any[], source: any[]) => source;

export const hardDeepCopy = (obj: object) => {
    const newObj = JSON.parse(JSON.stringify(obj)); // TODO: structuredClone can be used
    return newObj;
};

/* The function is designed to flatten an object and return a list of all key paths within the object. */
export function flattenObject(obj = {}) {
    const result: any = {};

    const flatten = (collection: any, prefix = '', suffix = '') => {
        lodash.forEach(collection, (value: any, key: any) => {
            const path = `${prefix}${key}${suffix}`;

            if (lodash.isArray(value)) {
                flatten(value, `${path}.`, '');
            } else if (lodash.isPlainObject(value)) {
                flatten(value, `${path}.`);
            } else {
                result[path] = value;
            }
        });
    };

    flatten(obj);

    return result;
}

export function isArrayOfPrimitives(data: any) {
    if (!Array.isArray(data)) return false;
    const primitiveDataType = ['string', 'number', 'boolean', 'undefined', 'bigint', 'symbol'];
    const isPrimitive = (item: any) => primitiveDataType.includes(typeof item);
    return (
        data.filter((item) => (!isPrimitive(item) && item !== null) || Array.isArray(item))
            .length === 0
    );
}

export function getComponentName(name: string) {
    if (name === 'AngelosComboBox') {
        return 'AngelosSelect';
    }
    return name || 'AngelosInput';
}
export const harRequestWithLruCache = async (
    dataConfig: any,
    getHarRequestParams: any,
    lruKey: string,
    lru: any,
    requestData: any
) => {
    const cacheBurstTime = dataConfig.cacheBurstTime;
    const isCacheEnabled = cacheBurstTime === 0 ? false : true;
    const cacheMaxAge = isCacheEnabled && (cacheBurstTime || Infinity); // if we didn't pass cacheBurstTime then automatily it will take it as Infinity
    if (isCacheEnabled) {
        lru.set('maxAge', cacheMaxAge);
    }
    if (isCacheEnabled && lru.has(lruKey)) {
        return lru.get(lruKey);
    } else {
        const { response } = await chainDataConfigs({
            configList: [dataConfig],
            context: getHarRequestParams(),
            requestData: requestData || {},
            executeResponseTransformer: true
        });
        const key = dataConfig.refId;

        const data = response[key];
        if (isCacheEnabled) {
            lru.set(lruKey, data);
        }

        return data;
    }
};

/**
 * Converts form field rules to async validator schema format
 * @param rules - Validation rules for the field
 * @param field - Field configuration
 * @returns Array of rule configurations
 */
export function rulesToAsyncValidatorSchema(
    rules: Record<string, RuleValue> | undefined = {},
    field: any
) {
    const ruleConfig: RuleConfig[] = [];
    const name = getProperty(field, 'component.name', getComponentName(field.component));
    const label = field.label || 'This field';

    // Determine field types
    const isNumberType = field.type === 'number';
    const isEmailType = field.type === 'email';
    const isArrayType = name === 'AngelosSelect' && field.multiSelect;

    // Create a copy of rules to avoid mutating the original
    const rulesCopy = cloneDeep(rules);

    // Add email validation rule if field type is email and no email rule exists
    if (isEmailType && !rulesCopy.email) {
        rulesCopy.email = true;
    }

    // Process each rule in the rulesCopy object
    Object.keys(rulesCopy).forEach((key) => {
        const rawRule = rulesCopy[key];
        const value =
            typeof rawRule === 'object' && rawRule !== null && !(rawRule instanceof RegExp)
                ? rawRule.value
                : rawRule;
        const message =
            typeof rawRule === 'object' && rawRule !== null && !(rawRule instanceof RegExp)
                ? rawRule.message
                : undefined;

        const currentRule: RuleConfig = {
            trigger: ['blur'],
            type: isArrayType ? 'array' : isNumberType ? 'number' : 'string'
        };

        if (message) currentRule.message = message;

        switch (key as ValidationRuleType) {
            case 'required':
                currentRule.required = Boolean(value);
                currentRule.type = isArrayType ? 'array' : isNumberType ? 'number' : 'string';
                currentRule.message ??= `${label} is required`;
                break;

            case 'email':
                currentRule.type = 'email';
                currentRule.message ??= `Please enter a valid email for ${label}`;
                break;

            case 'min':
            case 'min_value':
                currentRule.min = Number(value);
                if (key === 'min_value') currentRule.type = 'number';
                currentRule.message ??= `${label} must be at least ${value}${key === 'min' ? ' characters' : ''}`;
                currentRule.trigger = ['blur', 'change'];
                break;

            case 'max':
            case 'max_value':
                currentRule.max = Number(value);
                if (key === 'max_value') currentRule.type = 'number';
                currentRule.message ??= `${label} cannot exceed ${value}${key === 'max' ? ' characters' : ''}`;
                currentRule.trigger = ['blur', 'change'];
                break;

            case 'length':
                currentRule.len = Number(value);
                currentRule.message ??= `${label} must be exactly ${value} characters`;
                currentRule.trigger = ['blur', 'change'];
                break;

            case 'regex':
                currentRule.pattern = value instanceof RegExp ? value : String(value);
                currentRule.message ??= `${label} format is invalid`;
                currentRule.trigger = ['blur', 'change'];
                break;

            default:
                console.warn(`Unknown validation rule type: ${key}`);
                return;
        }

        // Merge in any extra rule properties
        if (typeof rawRule === 'object' && rawRule !== null && !(rawRule instanceof RegExp)) {
            const { value: _, message: __, ...rest } = rawRule;
            Object.assign(currentRule, rest);
        }
        ruleConfig.push(currentRule);
    });

    return ruleConfig;
}

export function filterDataConfig(
    dataConfig: unknown | unknown[],
    type: 'BEFORE_SUBMIT' | 'PREFILL' | null
) {
    const dataConfigsList = coerceArray(dataConfig);
    return dataConfigsList.filter((config) => {
        if (!config.type) {
            config.type = null; // null case denotes that the data config is applicable for SUBMIT type, it is the default one
            return config.type === type;
        }
        return config.type.toLowerCase() === type?.toLowerCase();
    });
}

export function transformFormModelBeforeSubmit(
    data: ArgumentsMap,
    dataConfig: unknown | unknown[]
) {
    const beforeSubmitDataConfig = filterDataConfig(dataConfig, 'BEFORE_SUBMIT');
    return beforeSubmitDataConfig.length
        ? executeTransformerFunction(beforeSubmitDataConfig[0].dataTransformer, data)
        : data.formModel;
}

/**
 * Finds error accordions based on the provided items, error validation structure, and extractor function.
 * Array section uses uuids whereas other accordion uses indexes for names, therefore creating this generic function.
 * @param items An array of items to iterate over.
 * @param errorValidation The error validation structure to check against.
 * @param extractor A function that extracts the error ID from each item based on the error validation structure.
 * @returns An array of extracted error IDs (strings or numbers).
 */
export function findErrorAccordions<T>(
    items: T[],
    errorValidation: ErrorStructure,
    extractor: (item: T, errorValidation: ErrorStructure, index: number) => string | number | null
): (string | number)[] {
    return items.reduce((acc: (string | number)[], item: T, index) => {
        const result = extractor(item, errorValidation, index);
        if (result !== null) {
            acc.push(result);
        }
        return acc;
    }, []);
}

// Update expanded names helper function
export function updateExpandedNames(
    expandedNames: Ref<(string | number)[]>,
    newNames: (string | number)[]
) {
    expandedNames.value = [...new Set([...expandedNames.value, ...newNames])];
}

export function isShadowRoot(element: HTMLElement | Element | ShadowRoot | string | null): boolean {
    return element instanceof ShadowRoot;
}

export function getErrorDetails(statusCode: string | number) {
    return ERROR_MESSAGES[statusCode] ?? DEFAULT_ERROR;
}
