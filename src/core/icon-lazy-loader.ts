/**
 * Icon Lazy Loader
 * Loads icons on-demand instead of loading all icons upfront
 */

import type { App } from 'vue';

// Track loaded icons to avoid duplicate loading
const loadedIcons = new Set<string>();
const loadingIcons = new Map<string, Promise<any>>();

// Icon loading statistics
let iconStats = {
    totalAvailable: 0,
    loaded: 0,
    loadingAttempts: 0
};

/**
 * Load a specific icon dynamically
 */
async function loadIcon(iconName: string): Promise<any> {
    if (loadedIcons.has(iconName)) {
        return; // Already loaded
    }

    if (loadingIcons.has(iconName)) {
        return loadingIcons.get(iconName)!;
    }

    console.log(`🎯 Loading icon: ${iconName}`);
    iconStats.loadingAttempts++;

    const loadingPromise = import('@zeta/icons')
        .then((iconsModule) => {
            const icon = iconsModule[iconName as keyof typeof iconsModule];
            if (icon) {
                loadedIcons.add(iconName);
                iconStats.loaded++;
                console.log(`✅ Icon loaded: ${iconName}`);
                return icon;
            } else {
                console.warn(`⚠️ Icon not found: ${iconName}`);
                return null;
            }
        })
        .catch((error) => {
            console.error(`❌ Failed to load icon ${iconName}:`, error);
            throw error;
        })
        .finally(() => {
            loadingIcons.delete(iconName);
        });

    loadingIcons.set(iconName, loadingPromise);
    return loadingPromise;
}

/**
 * Register icons with Vue app on-demand
 */
export async function registerIconsOnDemand(app: App, iconNames: string[]): Promise<void> {
    const loadPromises = iconNames.map(async (iconName) => {
        try {
            const icon = await loadIcon(iconName);
            if (icon) {
                app.component(iconName, icon);
            }
        } catch (error) {
            console.error(`Failed to register icon ${iconName}:`, error);
        }
    });

    await Promise.all(loadPromises);
}

/**
 * Scan component template for icon usage and load them
 */
export function extractIconsFromTemplate(template: string): string[] {
    const iconPattern = /<([A-Z][a-zA-Z0-9]*)\s*[^>]*>/g;
    const icons = new Set<string>();
    let match;

    while ((match = iconPattern.exec(template)) !== null) {
        const tagName = match[1];
        // Check if it looks like an icon (starts with capital letter and is likely an icon name)
        if (tagName && /^[A-Z][a-zA-Z0-9]*$/.test(tagName)) {
            icons.add(tagName);
        }
    }

    return Array.from(icons);
}

/**
 * Preload commonly used icons
 */
export async function preloadCommonIcons(app: App): Promise<void> {
    const commonIcons = [
        'Add',
        'Edit',
        'Delete',
        'Search',
        'Close',
        'Menu',
        'Home',
        'Settings',
        'User',
        'Save',
        'Cancel',
        'Check',
        'Warning',
        'Error',
        'Info',
        'ArrowLeft',
        'ArrowRight',
        'ArrowUp',
        'ArrowDown'
    ];

    console.log('🎯 Preloading common icons...');
    await registerIconsOnDemand(app, commonIcons);
    console.log('✅ Common icons preloaded');
}

/**
 * Smart icon loader that analyzes component usage
 */
export class SmartIconLoader {
    private app: App;
    private observedComponents = new Set<string>();

    constructor(app: App) {
        this.app = app;
    }

    /**
     * Analyze a component and load its icons
     */
    async analyzeAndLoadIcons(componentName: string, componentSource?: string): Promise<void> {
        if (this.observedComponents.has(componentName)) {
            return;
        }

        this.observedComponents.add(componentName);

        if (componentSource) {
            const icons = extractIconsFromTemplate(componentSource);
            if (icons.length > 0) {
                console.log(`🔍 Found ${icons.length} icons in ${componentName}:`, icons);
                await registerIconsOnDemand(this.app, icons);
            }
        }
    }

    /**
     * Load icons based on DOM scanning
     */
    async scanDOMForIcons(): Promise<void> {
        const allElements = document.querySelectorAll('*');
        const foundIcons = new Set<string>();

        allElements.forEach((element) => {
            const tagName = element.tagName;
            if (tagName && /^[A-Z][A-Z0-9-]*$/.test(tagName)) {
                // Convert kebab-case to PascalCase for icon names
                const iconName = tagName.split('-')
                    .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
                    .join('');
                foundIcons.add(iconName);
            }
        });

        if (foundIcons.size > 0) {
            console.log(`🔍 Found ${foundIcons.size} potential icons in DOM`);
            await registerIconsOnDemand(this.app, Array.from(foundIcons));
        }
    }
}

/**
 * Create optimized icon loader for components
 */
export function createOptimizedIconLoader(app: App) {
    const smartLoader = new SmartIconLoader(app);

    return {
        // Load specific icons
        loadIcons: (iconNames: string[]) => registerIconsOnDemand(app, iconNames),
        
        // Preload common icons
        preloadCommon: () => preloadCommonIcons(app),
        
        // Analyze component for icons
        analyzeComponent: (name: string, source?: string) => 
            smartLoader.analyzeAndLoadIcons(name, source),
        
        // Scan DOM for icons
        scanDOM: () => smartLoader.scanDOMForIcons(),
        
        // Get statistics
        getStats: () => ({ ...iconStats, loadedIcons: Array.from(loadedIcons) }),
        
        // Check if icon is loaded
        isLoaded: (iconName: string) => loadedIcons.has(iconName)
    };
}

/**
 * Get icon loading statistics
 */
export function getIconStats() {
    return {
        ...iconStats,
        loadedIcons: Array.from(loadedIcons),
        loadingIcons: Array.from(loadingIcons.keys())
    };
}

// Export for global access
export const IconLazyLoader = {
    loadIcon,
    registerIconsOnDemand,
    preloadCommonIcons,
    extractIconsFromTemplate,
    createOptimizedIconLoader,
    getStats: getIconStats
};

// Make available globally
declare global {
    interface Window {
        IconLazyLoader: typeof IconLazyLoader;
    }
}

if (typeof window !== 'undefined') {
    window.IconLazyLoader = IconLazyLoader;
}
