import { getProperty, hasProperty } from 'dot-prop';
import { getServiceUrl } from './config-manager';
import { API_KEY_PRIORITY, AUTH_TOKEN_KEY, HERCULES_API_KEY_PATH } from './constants';

const detectMobile = () => {
    return (
        [/Android/i, /iPhone/i, /iPad/i, /iPod/i].filter((toMatchItem) =>
            navigator.userAgent.match(toMatchItem)
        ).length > 0
    );
};

const bindZetaReady = () =>
    new Promise<void>((resolve, reject) => {
        if (typeof window.zeta === 'undefined' || !window.zeta) {
            document.addEventListener('zetaready', () => {
                resolve();
            });
        } else if ('zeta' in window) {
            resolve();
        } else {
            reject();
        }
    });

const getTokenFromZetaBridge = () => {
    const retry = () => {
        setTimeout(() => {
            getTokenFromZetaBridge();
        }, 500);
    };

    return bindZetaReady()
        .then(() => {
            return new Promise<string>((resolve, reject) => {
                window.zeta.getAuthToken(
                    null,
                    (data: { authToken: string }) => {
                        localStorage.setItem(AUTH_TOKEN_KEY, data.authToken);
                        resolve(data.authToken);
                    },
                    (err: any) => {
                        retry();
                    }
                );
            });
        })
        .catch(() => {
            retry();
        });
};

export const getApiKey = () => {
    const priority = getProperty(window, API_KEY_PRIORITY, true);
    const apiKey: string | undefined = getProperty(window, HERCULES_API_KEY_PATH);
    return priority && apiKey;
};

const getTokenFromLocalStorage = () => {
    let counter = 0;
    let timer: any;
    return new Promise((resolve, reject) => {
        const tokenResolver = () => {
            counter++;
            const apiKey = getApiKey();
            if (typeof apiKey === 'string') {
                resolve(apiKey);
                clearInterval(timer);
            } else if (hasProperty(localStorage, AUTH_TOKEN_KEY)) {
                resolve(localStorage.getItem(AUTH_TOKEN_KEY));
                clearInterval(timer);
            } else if (counter > 5) {
                clearInterval(timer);
                const authTokenError: any = new Error();
                authTokenError.status = 401;
                reject(authTokenError);
            }
        };
        timer = setInterval(tokenResolver, 500);
        tokenResolver();
    });
};

export const ensureAuthToken = () => {
    return detectMobile() ? getTokenFromZetaBridge() : getTokenFromLocalStorage();
};

export const getAuthToken = () => {
    return localStorage.getItem(AUTH_TOKEN_KEY)?.toString() || null;
};

const getHeaders = () => {
    const headers = new Headers();
    const apiKey: any = getApiKey();
    if (apiKey) {
        headers.append('H-API-KEY', apiKey);
    } else {
        headers.append('Authorization', `Bearer ${getAuthToken()}`);
    }

    headers.append('Content-Type', 'application/json');
    return headers;
};

interface APIResponse {
    status: number;
    statusText: string;
    data: any;
}

export default {
    getViewConfig(
        tenantId: string,
        businessEntityId: string,
        viewType: string
    ): Promise<APIResponse> {
        const serviceBaseUrl = `${getServiceUrl()}/api/v1/tenants/${tenantId}/business-entities/${businessEntityId}/component-type/${viewType}`;
        // return ensureAuthToken()
        //     .then(() => fetch(serviceBaseUrl, { method: 'GET', headers: getHeaders() }))
        return fetch(serviceBaseUrl, { method: 'GET', headers: getHeaders() }).then(async (res) => {
            const result: APIResponse = {
                status: res.status,
                statusText: res.statusText,
                data: await res.json()
            };

            if (!res.ok) {
                return Promise.reject(result);
            }

            return result;
        });
    }
};
