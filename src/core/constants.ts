export const SERVICE_BASE_URL = '__zeta__.angelos.SERVICE_BASE_URL';
export const OMS_SERVICE_BASE_URL = '__zeta__.angelos.OMS_SERVICE_BASE_URL';
export const BASE_THEME = '__zeta__.angelos.THEME';
export const GOOGLE_PLACES_API_KEY = '__zeta__.angelos.GOOGLE_PLACES_API_KEY';
export const API_KEY_PRIORITY = '__zeta__.angelos.API_KEY_PRIORITY';
export const HERCULES_ASSETS_DOMAIN_KEY = '__HERCULES__.$assetsBaseUrl';
export const GLOBAL_ASSETS_VARIABLES = [
    '__BUSINESS_COMPONENTS_BASE_URL__',
    HERCULES_ASSETS_DOMAIN_KEY
];
export const PACKAGE_PREFIX = 'zeta-business';
export const PACKAGE_EXPOSED_WITH = 'registerComponents';

export const COMPONENT_TYPES = {
    form: 'ANGELOS_FORM',
    table: 'ANGELOS_TABLE'
};
export const FIELD_TYPES = { CUSTOM: 'custom', ARRAY: 'array' };

export const DEFAULT_VALUE = '---';

export const AUTH_TOKEN_KEY = '@zeta::authToken';
export const HERCULES_API_KEY_PATH = '__HERCULES__.$apiKey';
export const HYDRATE_STYLE_ID = 'hydrate';
export const ASSETS_BASE_DIR = 'https://hercules-assets.mum1-stage.zetaapps.in';

export const AUTO_FILL_EVENT_PREFIX = 'autoFill:';

export const DEFAULT_DATE_FORMAT = 'DD/MM/YYYY';
export const DEFAULT_DATE_TIME_FORMAT = 'DD MMM YYYY, HH:mm';
export const DEFAULT_GRID_COLUMNS = 2;
export const FILE_TYPES = {
    CSV: 'text/csv'
};
export const DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024;
export const MAX_NESTED_LEVEL = 2;
export const GROUP_TYPE = {
    TAB: 'tab'
};

export const DEFAULT_FORM_CONFIG = {
    layoutType: 'simple', // simple|accordion
    form: {
        title: '',
        subTitle: '',
        layoutType: 'simple', // simple|accordion
        buttons: [
            {
                action: 'submit',
                type: 'primary',
                text: 'Submit',
                expanded: false,
                iconLeft: null,
                iconRight: null
            }
        ]
    },
    submitSuccess: {
        icon: 'check-circle',
        type: 'bottom-sheet', // or bottom-sheet
        title: 'Data submitted successfully!',
        subtitle: null,
        buttons: [
            {
                text: 'Confirm',
                id: '',
                expanded: false
            }
        ]
    },
    submitError: {
        icon: 'check-circle',
        title: 'We were unable to process your request.',
        subtitle: 'Please retry after some time.'
    }
};

export const FORM_STATE_KEYS = {
    FORM_MODEL: 'FORM_MODEL',
    PREFILL_DATA: 'PREFILL_DATA',
    CONTEXT: 'CONTEXT',
    PARAMS: 'PARAMS',
    RULES: 'RULES',
    FILE_UPLOAD_FIELDS: 'FILE_UPLOAD_FIELDS',
    ERROR_VALIDATION: 'ERROR_VALIDATION'
} as const;

export const DEFAULT_ERROR = {
    errorName: 'Unknown Error',
    userFriendlyMessage: 'An unknown error occurred. Please try again later.',
    description: 'An unknown error occurred. Please try again later.'
};
export const ERROR_MESSAGES: {
    [k: string | number]: {
        errorName: string;
        userFriendlyMessage: string;
        description: string;
    };
} = {
    500: {
        errorName: 'Internal Server Error',
        userFriendlyMessage: 'Internal Server Error',
        description:
            'The server encountered an internal error and was unable to complete your request.'
    },
    501: {
        errorName: 'Not Implemented',
        userFriendlyMessage: 'Something broke at our end',
        description:
            'The server either does not recognize the request method, or it lacks the ability to fulfill the request.'
    },
    502: {
        errorName: 'Bad Gateway',
        userFriendlyMessage: 'Internal Server Error',
        description: 'There was an issue with one of our servers. We are trying to fix it.'
    },
    503: {
        errorName: 'Service Unavailable',
        userFriendlyMessage: 'Service is temporarily unavailable',
        description:
            'The server is temporarily unable to service your request due to maintenance downtime or overload. Please try again later.'
    },
    504: {
        errorName: 'Gateway Timeout',
        userFriendlyMessage: 'The page cannot be reached',
        description:
            'The server did not receive a timely response from the upstream server while attempting to complete the request.'
    },
    400: {
        errorName: 'Bad Request',
        userFriendlyMessage: 'The request cannot be completed',
        description:
            'The request could not be understood by the server due to malformed syntax or invalid request.'
    },
    401: {
        errorName: 'Unauthorized',
        userFriendlyMessage: 'Authentication failed/Unable to access token',
        description:
            'It appears your login details may be invalid. Please fill in the correct login details or try refreshing the page.'
    },
    403: {
        errorName: 'Forbidden',
        userFriendlyMessage: 'Required authorisation',
        description: 'You are not authorised to access this resource.'
    },
    404: {
        errorName: 'Data Not Found',
        userFriendlyMessage: 'Requested data does not exist',
        description: 'We could not find what you were looking for.'
    },
    408: {
        errorName: 'Request Timeout',
        userFriendlyMessage: 'Something went wrong',
        description:
            'We did not receive a timely response to complete your request due to several possible reasons.'
    }
};

export const FILTER_DEFAULT_VALUE_SELECTOR = {
    actions: ['apply', 'cancel'],
    caseSensitive: false,
    multiple: true,
    value: [],
    options: [],
    searchable: true,
    showIndicator: false,
    valueTransformer: (item: any) => item
};

export const ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS = {
    CHECK_VALIDITY: 'angelos_check_validity',
    RESET: 'angelos_reset',
    CANCEL: 'angelos_cancel',
    GET_FORM_DATA: 'angelos_get_form_data'
};

export const PROMISE_STATUS = {
    FULFILLED: 'fulfilled',
    REJECTED: 'rejected'
};

export const DATA_TABLE_EVENTS = ['on-data', 'item-clicked', 'custom-events', 'selection-update'];

export const DEBOUNCE_TIME = 500;
export const BODY_SUPPORTED_REST_METHODS = ['POST', 'PUT', 'PATCH'];
export const ANGELOS_COMPONENT_TYPES = {
    CREATE_FORM: 'create-form',
    UPDATE_FORM: 'update-form',
    DETAILS_VIEW: 'details-view',
    DATA_TABLE: 'data-table',
    DYNAMIC_VIEW: 'dynamic-view'
} as const;
