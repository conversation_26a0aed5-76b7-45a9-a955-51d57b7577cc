import lodash from 'lodash';
import { getAuthToken } from './service';
import dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';

const filterTextConditions = {
    sw: 'sw',
    ew: 'ew',
    excat: 'excat',
    nexact: 'nexact',
    ine: 'ine',
    ie: 'ie',
    ncontains: 'ncontains',
    contains: 'contains'
};
export function ganymedeAdopter() {
    return {
        getHeaders: function () {
            return [{ name: 'Gan-AuthToken', value: getAuthToken() }];
        },
        getPaginationParams: function (size: number, currentPage: number) {
            return [
                { name: 'limit', value: size },
                { name: 'offset', value: (currentPage - 1) * size }
            ];
        },
        getSearchParams: function (searchKeyList: string[], searchValue: string) {
            const searchParams = [];
            if (searchValue) {
                for (const key of searchKeyList) {
                    searchParams.push(`${key}.ilike.%${searchValue}%`);
                }
                return [{ name: 'or', value: `(${searchParams.join(',')})` }];
            }
            return [];
        },
        getFilterParams: function (
            filterConditonsMap: Map<string, string>,
            filterBarArr: string[]
        ) {
            const filterParams = [];
            for (const [index, filter] of filterConditonsMap.entries()) {
                const [filterName, filterValue] = filter.split('=');
                const fitlerIndex = index as any;
                switch (filterName) {
                    case filterTextConditions.contains:
                        if (filterValue) {
                            filterParams.push({
                                name: filterBarArr[fitlerIndex],
                                value: `ilike.%${filterValue}%`
                            });
                        }
                        break;
                    case filterTextConditions.sw:
                        if (filterValue) {
                            filterParams.push({
                                name: filterBarArr[fitlerIndex],
                                value: `ilike.${filterValue}%`
                            });
                        }
                        break;
                    case filterTextConditions.ew:
                        if (filterValue) {
                            filterParams.push({
                                name: filterBarArr[fitlerIndex],
                                value: `ilike.%${filterValue}`
                            });
                        }
                        break;
                    case filterTextConditions.excat:
                        if (filterValue) {
                            filterParams.push({
                                name: filterBarArr[fitlerIndex],
                                value: `ilike.${filterValue}`
                            });
                        }
                        break;
                    case filterTextConditions.nexact:
                        if (filterValue) {
                            filterParams.push({
                                name: filterBarArr[fitlerIndex],
                                value: `ilike.%${filterValue}%`
                            });
                        }
                        break;
                    case filterTextConditions.ine:
                        filterParams.push({
                            name: filterBarArr[fitlerIndex],
                            value: `not.is.null`
                        });
                        break;
                    case filterTextConditions.ie:
                        filterParams.push({
                            name: filterBarArr[fitlerIndex],
                            value: `is.null`
                        });
                        break;
                    case filterTextConditions.ncontains:
                        if (filterValue) {
                            filterParams.push({
                                name: filterBarArr[fitlerIndex],
                                value: `not.ilike.%${filterValue}%`
                            });
                        }
                        break;
                }
            }
            return filterParams;
        }
    };
}

export function financeCenterAPIAdopter() {
    return {
        getHeaders: function () {
            return [{ name: 'authorization', value: `Bearer ${getAuthToken()}` }];
        },
        getPaginationParams: function (size: number, currentPage: number) {
            return [
                { name: 'pageSize', value: size },
                { name: 'pageNo', value: currentPage }
            ];
        },

        getFilterParams: function (
            filterConditonsMap: Map<string, string>,
            filterBarArr: string[]
        ) {
            const filterParams = [];
            for (const [name, value] of filterConditonsMap.entries()) {
                if (name === 'transaction_date' && value) {
                    const date = value.split(',')[0].toString();

                    // filterParams.push({
                    //     name: 'after',
                    //     value: new Date(
                    //         dayjs(value).startOf('date').add(1, 'day').startOf('date').valueOf(),
                    //     ),
                    // });
                    // filterParams.push({
                    //     name: 'before',
                    //     value: new Date(dayjs(value).startOf('date').valueOf()),
                    // });
                } else if (name && value) {
                    filterParams.push({
                        name: name,
                        value: value
                    });
                }
            }
            return filterParams;
        }
    };
}

/* The function is designed to flatten an object and return a list of all key paths within the object. */
export function flattenObject(obj = {}) {
    const result: any = {};

    const flatten = (collection: any, prefix = '', suffix = '') => {
        lodash.forEach(collection, (value: any, key: any) => {
            const sanitisedKey = typeof key === 'string' ? key.split('.').join('\\.') : key;
            const path = `${prefix}${sanitisedKey}${suffix}`;

            if (lodash.isArray(value)) {
                flatten(value, `${path}.`, '');
            } else if (lodash.isPlainObject(value)) {
                flatten(value, `${path}.`);
            } else {
                result[path] = value;
            }
        });
    };

    flatten(obj);

    return result;
}
export function supportCenterAPIAdopter() {
    return {
        getHeaders: function () {
            return [{ name: 'X-Zeta-Authtoken', value: `${getAuthToken()}` }];
        },
        getPaginationParams: function (size: number, currentPage: number) {
            return [
                { name: 'pageSize', value: size },
                { name: 'pageNo', value: currentPage }
            ];
        }
    };
}

/**
 * API Adapter for Audit log Center & its components
 * @returns Adapter configuration
 */
export function auditLogAPIAdapter(context: any) {
    return {
        getHeaders: function () {
            return [{ name: 'authorization', value: `Bearer ${getAuthToken()}` }];
        },
        getPaginationParams: function (size: number, currentPage: number) {
            /**
             * TODO- The following default POST request body is added to Pagination Params because we don't need any filter
             * We will pass all required filter for POST request in the context & pass it in the body via this method
             */
            const startDate =
                context?.requestBody?.performedAfter ??
                dayjs().subtract(1, 'month').startOf('date').valueOf();
            const endDate =
                context?.requestBody?.performedBefore ?? dayjs().endOf('date').valueOf();
            return {
                pageStart: currentPage - 1,
                pageSize: size,
                requestId: uuid(),
                'header.businessEntityName': context?.requestBody?.businessEntityName,
                'header.businessEntityId': context?.requestBody?.businessEntityId,
                performedAfter: dayjs(startDate).startOf('date').valueOf(),
                performedBefore: dayjs(endDate).endOf('date').valueOf(),
                sortBy: context?.requestBody?.sortBy,
                sortOrder: context?.requestBody?.sortOrder
            };
        },
        getFilterParams: function (
            filterConditonsMap: Map<string, string>,
            filterBarArr: string[]
        ) {
            const filterParams = [];
            let startDate =
                context?.requestBody?.performedAfter ??
                dayjs().subtract(1, 'month').startOf('date').valueOf();
            let endDate = context?.requestBody?.performedBefore ?? dayjs().endOf('date').valueOf();
            for (const [index, value] of filterConditonsMap.entries()) {
                if (Array.isArray(value) && value.length) {
                    [startDate, endDate] = value;
                }
            }
            return {
                performedAfter: dayjs(startDate).startOf('date').valueOf(),
                performedBefore: dayjs(endDate).endOf('date').valueOf()
            };
        }
    };
}

/**
 * API adopter for Ruby List APIs
 * @returns Header and Pagination params
 */
export function rubyListAPIAdopter() {
    return {
        getHeaders: function () {
            return [{ name: 'X-Zeta-Authtoken', value: `${getAuthToken()}` }];
        },
        getPaginationParams: function (size: number, currentPage: number) {
            return [
                { name: 'pageSize', value: size },
                { name: 'pageNumber', value: currentPage - 1 }
            ];
        }
    };
}

/**
 * API adopter for Aura ledger manager List APIs
 * @returns Header and Pagination params
 */
export function auraLedgerManagerAPIAdopter() {
    return {
        getHeaders: function () {
            return [{ name: 'Authorization', value: `Bearer ${getAuthToken()}` }];
        },
        getPaginationParams: function (size: number, currentPage: number) {
            return [
                { name: 'pageSize', value: size },
                { name: 'pageNumber', value: currentPage }
            ];
        }
    };
}
