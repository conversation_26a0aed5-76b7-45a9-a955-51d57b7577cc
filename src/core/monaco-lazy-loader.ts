/**
 * Monaco Editor La<PERSON>
 * Loads Monaco Editor and its language modules only when needed
 */

let monacoLoaded = false;
let monacoLoadingPromise: Promise<any> | null = null;
let monacoInstance: any = null;

// Track which languages have been loaded
const loadedLanguages = new Set<string>();
const loadingLanguages = new Map<string, Promise<void>>();

/**
 * Load Monaco Editor core
 */
async function loadMonacoCore(): Promise<any> {
    if (monacoLoaded && monacoInstance) {
        return monacoInstance;
    }

    if (monacoLoadingPromise) {
        return monacoLoadingPromise;
    }

    monacoLoadingPromise = import('monaco-editor')
        .then((monacoModule) => {
            const monaco = monacoModule;

            if (!monaco || !monaco.editor) {
                throw new Error('Monaco Editor not properly loaded');
            }

            // Configure Monaco Environment for web components
            (self as any).MonacoEnvironment = {
                getWorker: function () {
                    // Simple worker implementation to avoid configuration issues
                    return new Worker(
                        URL.createObjectURL(
                            new Blob(['self.onmessage = function() {};'], {
                                type: 'application/javascript'
                            })
                        )
                    );
                }
            };

            monacoLoaded = true;
            monacoInstance = monaco;
            return monaco;
        })
        .catch((error) => {
            monacoLoadingPromise = null; // Reset so it can be retried
            throw error;
        });

    return monacoLoadingPromise;
}

/**
 * Load a specific Monaco language
 */
async function loadMonacoLanguage(language: string): Promise<void> {
    if (loadedLanguages.has(language)) {
        return;
    }

    if (loadingLanguages.has(language)) {
        return loadingLanguages.get(language)!;
    }

    const loadingPromise = (async () => {
        // Languages are included in the main Monaco bundle
        // Mark as loaded to avoid duplicate loading attempts
        loadedLanguages.add(language);
    })();

    loadingLanguages.set(language, loadingPromise);
    await loadingPromise;
    loadingLanguages.delete(language);
}

/**
 * Create Monaco Editor with lazy loading
 */
export async function createMonacoEditor(
    container: HTMLElement,
    options: {
        value?: string;
        language?: string;
        theme?: string;
        readOnly?: boolean;
        [key: string]: any;
    } = {}
): Promise<any> {
    if (!container) {
        throw new Error('Container element is required');
    }

    // Load Monaco core
    const monaco = await loadMonacoCore();

    if (!monaco || !monaco.editor) {
        throw new Error('Monaco Editor not properly loaded - editor object missing');
    }

    // Load specific language if specified
    if (options.language) {
        await loadMonacoLanguage(options.language);
    }

    // Create editor
    const editor = monaco.editor.create(container, {
        value: options.value || '',
        language: options.language || 'javascript',
        theme: options.theme || 'vs-dark',
        readOnly: options.readOnly || false,
        automaticLayout: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        ...options
    });

    // Clean up Monaco aria containers that cause UI issues in web components
    setTimeout(() => {
        Array.from(document.getElementsByClassName('monaco-aria-container')).forEach((item) =>
            item.remove()
        );
    }, 100);

    return editor;
}

/**
 * Preload commonly used Monaco languages
 */
export async function preloadCommonLanguages(): Promise<void> {
    const commonLanguages = ['javascript', 'typescript', 'json', 'html', 'css'];

    console.log('🔤 Preloading common Monaco languages...');

    const loadPromises = commonLanguages.map((lang) => loadMonacoLanguage(lang));
    await Promise.all(loadPromises);

    console.log('✅ Common Monaco languages preloaded');
}

/**
 * Get Monaco loading statistics
 */
export function getMonacoStats() {
    return {
        coreLoaded: monacoLoaded,
        loadedLanguages: Array.from(loadedLanguages),
        loadingLanguages: Array.from(loadingLanguages.keys())
    };
}

/**
 * Check if Monaco is ready for a specific language
 */
export function isMonacoReady(language?: string): boolean {
    if (!monacoLoaded) return false;
    if (!language) return true;
    return loadedLanguages.has(language);
}

// Export for global access
export const MonacoLazyLoader = {
    createEditor: createMonacoEditor,
    preloadCommonLanguages,
    getStats: getMonacoStats,
    isReady: isMonacoReady,
    loadLanguage: loadMonacoLanguage
};

// Make available globally
declare global {
    interface Window {
        MonacoLazyLoader: typeof MonacoLazyLoader;
    }
}

if (typeof window !== 'undefined') {
    window.MonacoLazyLoader = MonacoLazyLoader;
}
