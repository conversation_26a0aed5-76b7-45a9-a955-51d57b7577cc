/**
 * Monaco Editor <PERSON><PERSON>
 * Loads Monaco Editor and its language modules only when needed
 */

let monacoLoaded = false;
let monacoLoadingPromise: Promise<any> | null = null;

// Track which languages have been loaded
const loadedLanguages = new Set<string>();
const loadingLanguages = new Map<string, Promise<void>>();

/**
 * Load Monaco Editor core
 */
async function loadMonacoCore(): Promise<any> {
    if (monacoLoaded) {
        return (await import('monaco-editor')).default;
    }

    if (monacoLoadingPromise) {
        return monacoLoadingPromise;
    }

    console.log('🎨 Loading Monaco Editor core...');

    monacoLoadingPromise = import('monaco-editor')
        .then((monacoModule) => {
            console.log('🔍 Monaco module loaded:', monacoModule);
            console.log('🔍 Monaco module keys:', Object.keys(monacoModule));
            console.log('🔍 Monaco module.default:', monacoModule.default);
            console.log('🔍 Monaco module.editor:', monacoModule.editor);

            // Try different ways to access Monaco
            let monaco = null;

            // Method 1: Direct access to editor
            if (monacoModule.editor) {
                monaco = monacoModule;
                console.log('✅ Found Monaco via direct access');
            }
            // Method 2: Default export
            else if (monacoModule.default && monacoModule.default.editor) {
                monaco = monacoModule.default;
                console.log('✅ Found Monaco via default export');
            }
            // Method 3: Check if the module itself is Monaco
            else if (monacoModule.languages && monacoModule.Uri) {
                monaco = monacoModule;
                console.log('✅ Found Monaco via module structure detection');
            }

            if (!monaco) {
                console.error('❌ Monaco Editor not found in any expected location');
                console.error('Module structure:', monacoModule);
                throw new Error('Monaco Editor not properly loaded - no valid Monaco object found');
            }

            if (!monaco.editor) {
                console.error(
                    '❌ Monaco.editor not found. Available properties:',
                    Object.keys(monaco)
                );
                throw new Error('Monaco Editor not properly loaded - editor object missing');
            }

            // Set up workers
            (self as any).MonacoEnvironment = {
                getWorkerUrl: function (moduleId: any, label: string) {
                    if (label === 'json') {
                        return './assets/json.worker.js';
                    }
                    if (label === 'css' || label === 'scss' || label === 'less') {
                        return './assets/css.worker.js';
                    }
                    if (label === 'html' || label === 'handlebars' || label === 'razor') {
                        return './assets/html.worker.js';
                    }
                    if (label === 'typescript' || label === 'javascript') {
                        return './assets/ts.worker.js';
                    }
                    return './assets/editor.worker.js';
                }
            };

            monacoLoaded = true;
            console.log('✅ Monaco Editor core loaded successfully');
            return monaco;
        })
        .catch((error) => {
            console.error('❌ Failed to load Monaco Editor:', error);
            monacoLoadingPromise = null; // Reset so it can be retried
            throw error;
        });

    return monacoLoadingPromise;
}

/**
 * Load a specific Monaco language
 */
async function loadMonacoLanguage(language: string): Promise<void> {
    if (loadedLanguages.has(language)) {
        return;
    }

    if (loadingLanguages.has(language)) {
        return loadingLanguages.get(language)!;
    }

    console.log(`🔤 Loading Monaco language: ${language}`);

    const loadingPromise = (async () => {
        try {
            // For now, we'll skip individual language loading and rely on Monaco's built-in language support
            // This avoids build issues with dynamic imports of specific language modules
            console.log(`✅ Monaco language ${language} will be available through core Monaco`);
            loadedLanguages.add(language);
        } catch (error) {
            console.error(`❌ Failed to load Monaco language ${language}:`, error);
            throw error;
        }
    })();

    loadingLanguages.set(language, loadingPromise);
    await loadingPromise;
    loadingLanguages.delete(language);
}

/**
 * Create Monaco Editor with lazy loading
 */
export async function createMonacoEditor(
    container: HTMLElement,
    options: {
        value?: string;
        language?: string;
        theme?: string;
        readOnly?: boolean;
        [key: string]: any;
    } = {}
): Promise<any> {
    try {
        console.log('🎨 Creating Monaco Editor...');

        if (!container) {
            throw new Error('Container element is required');
        }

        // Load Monaco core
        const monaco = await loadMonacoCore();

        if (!monaco || !monaco.editor) {
            throw new Error('Monaco Editor not properly loaded - editor object missing');
        }

        console.log('🔍 Monaco object:', monaco);
        console.log('🔍 Monaco.editor:', monaco.editor);

        // Load specific language if specified
        if (options.language) {
            await loadMonacoLanguage(options.language);
        }

        // Create editor
        const editor = monaco.editor.create(container, {
            value: options.value || '',
            language: options.language || 'javascript',
            theme: options.theme || 'vs-dark',
            readOnly: options.readOnly || false,
            automaticLayout: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            ...options
        });

        // Clean up Monaco aria containers that cause UI issues in web components
        setTimeout(() => {
            Array.from(document.getElementsByClassName('monaco-aria-container')).forEach((item) =>
                item.remove()
            );
        }, 100);

        console.log('✅ Monaco Editor created successfully');
        return editor;
    } catch (error) {
        console.error('❌ Failed to create Monaco Editor:', error);
        throw error;
    }
}

/**
 * Preload commonly used Monaco languages
 */
export async function preloadCommonLanguages(): Promise<void> {
    const commonLanguages = ['javascript', 'typescript', 'json', 'html', 'css'];

    console.log('🔤 Preloading common Monaco languages...');

    const loadPromises = commonLanguages.map((lang) => loadMonacoLanguage(lang));
    await Promise.all(loadPromises);

    console.log('✅ Common Monaco languages preloaded');
}

/**
 * Get Monaco loading statistics
 */
export function getMonacoStats() {
    return {
        coreLoaded: monacoLoaded,
        loadedLanguages: Array.from(loadedLanguages),
        loadingLanguages: Array.from(loadingLanguages.keys())
    };
}

/**
 * Check if Monaco is ready for a specific language
 */
export function isMonacoReady(language?: string): boolean {
    if (!monacoLoaded) return false;
    if (!language) return true;
    return loadedLanguages.has(language);
}

// Export for global access
export const MonacoLazyLoader = {
    createEditor: createMonacoEditor,
    preloadCommonLanguages,
    getStats: getMonacoStats,
    isReady: isMonacoReady,
    loadLanguage: loadMonacoLanguage
};

// Make available globally
declare global {
    interface Window {
        MonacoLazyLoader: typeof MonacoLazyLoader;
    }
}

if (typeof window !== 'undefined') {
    window.MonacoLazyLoader = MonacoLazyLoader;
}
