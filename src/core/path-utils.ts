// This is required as new version of dotprop does not support dot convention for array
export const convertPath = (path: string) => {
    const parts = path.split('.');
    let output = parts.shift() as string;

    while (parts.length > 0) {
        const part = parts.shift();
        if (!isNaN(part as any)) {
            output += `[${part}]`;
        } else {
            output += `.${part}`;
        }
    }

    return output;
};
