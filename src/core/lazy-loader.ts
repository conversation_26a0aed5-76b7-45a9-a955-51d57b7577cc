/**
 * Lazy Component Loader
 * Dynamically loads components when they are detected in the DOM
 */

// Component to entry file mapping
const COMPONENT_REGISTRY = {
    'zwe-angelos-create-form-v3': () => import('@/entries/forms'),
    'zwe-angelos-update-form-v3': () => import('@/entries/forms'),
    'zwe-angelos-data-table-v3': () => import('@/entries/data-table'),
    'zwe-angelos-details-view-v3': () => import('@/entries/details-view'),
    'zwe-angelos-dynamic-view-v3': () => import('@/entries/dynamic-view')
};

// Track loaded components to avoid duplicate loading
const loadedComponents = new Set<string>();
const loadingComponents = new Map<string, Promise<void>>();

// Statistics for debugging
let stats = {
    totalComponents: Object.keys(COMPONENT_REGISTRY).length,
    loadedComponents: 0,
    loadingAttempts: 0,
    upgradeAttempts: 0
};

/**
 * Load a specific component entry
 */
async function loadComponent(componentName: string): Promise<void> {
    if (loadedComponents.has(componentName)) {
        console.log(`✅ Component ${componentName} already loaded`);
        return;
    }

    if (loadingComponents.has(componentName)) {
        console.log(`⏳ Component ${componentName} already loading, waiting...`);
        return loadingComponents.get(componentName)!;
    }

    const loader = COMPONENT_REGISTRY[componentName as keyof typeof COMPONENT_REGISTRY];
    if (!loader) {
        console.warn(`⚠️ No loader found for component: ${componentName}`);
        return;
    }

    console.log(`🔄 Loading component: ${componentName}`);
    stats.loadingAttempts++;

    const loadingPromise = loader()
        .then(() => {
            loadedComponents.add(componentName);
            stats.loadedComponents++;
            console.log(`✅ Component loaded: ${componentName}`);
        })
        .catch((error) => {
            console.error(`❌ Failed to load component ${componentName}:`, error);
            throw error;
        })
        .finally(() => {
            loadingComponents.delete(componentName);
        });

    loadingComponents.set(componentName, loadingPromise);
    return loadingPromise;
}

/**
 * Upgrade custom elements after component is loaded
 */
function upgradeElements(componentName: string): void {
    const elements = document.querySelectorAll(componentName);
    if (elements.length > 0) {
        console.log(`🔧 Upgrading ${elements.length} ${componentName} elements`);
        stats.upgradeAttempts++;
        
        elements.forEach((element) => {
            if (customElements.get(componentName)) {
                customElements.upgrade(element);
            }
        });
    }
}

/**
 * Scan DOM for components and load them
 */
async function scanAndLoadComponents(root: Element | Document = document): Promise<void> {
    const componentNames = Object.keys(COMPONENT_REGISTRY);
    const foundComponents = new Set<string>();

    // Find all component instances in the DOM
    componentNames.forEach(componentName => {
        const elements = root.querySelectorAll(componentName);
        if (elements.length > 0) {
            foundComponents.add(componentName);
        }
    });

    // Load found components
    const loadPromises = Array.from(foundComponents).map(async (componentName) => {
        try {
            await loadComponent(componentName);
            upgradeElements(componentName);
        } catch (error) {
            console.error(`Failed to load and upgrade ${componentName}:`, error);
        }
    });

    await Promise.all(loadPromises);
}

/**
 * Set up DOM observers for automatic component loading
 */
function setupObservers(): void {
    // Mutation Observer - watches for new elements added to DOM
    const mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        scanAndLoadComponents(element);
                    }
                });
            }
        });
    });

    mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Intersection Observer - loads components when they come into viewport
    const intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const componentName = element.tagName.toLowerCase();
                
                if (COMPONENT_REGISTRY[componentName as keyof typeof COMPONENT_REGISTRY]) {
                    loadComponent(componentName).then(() => {
                        upgradeElements(componentName);
                    });
                }
            }
        });
    });

    // Observe all existing component elements
    Object.keys(COMPONENT_REGISTRY).forEach(componentName => {
        const elements = document.querySelectorAll(componentName);
        elements.forEach(element => {
            intersectionObserver.observe(element);
        });
    });

    console.log('👀 DOM observers set up for lazy loading');
}

/**
 * Initialize the lazy loading system
 */
export function initializeLazyLoading(): void {
    console.log('🚀 Initializing lazy loading system...');
    
    // Initial scan for existing components
    scanAndLoadComponents();
    
    // Set up observers for future components
    setupObservers();
    
    console.log('✅ Lazy loading system initialized');
}

/**
 * Public API for manual component loading and debugging
 */
export const AngelosSDK = {
    // Load specific components
    async preload(componentNames: string[]): Promise<void> {
        const loadPromises = componentNames.map(name => loadComponent(name));
        await Promise.all(loadPromises);
    },

    // Get loading statistics
    getStats() {
        return { ...stats };
    },

    // Debug methods
    debug: {
        isLoaded: (componentName: string) => loadedComponents.has(componentName),
        
        async forceLoadAndUpgrade(componentName: string): Promise<void> {
            await loadComponent(componentName);
            upgradeElements(componentName);
        },
        
        async upgradeAllElements(): Promise<void> {
            await scanAndLoadComponents();
        },
        
        getRegistry: () => ({ ...COMPONENT_REGISTRY }),
        getLoadedComponents: () => Array.from(loadedComponents),
        getLoadingComponents: () => Array.from(loadingComponents.keys())
    }
};

// Make SDK available globally
declare global {
    interface Window {
        AngelosSDK: typeof AngelosSDK;
    }
}

if (typeof window !== 'undefined') {
    window.AngelosSDK = AngelosSDK;
}
