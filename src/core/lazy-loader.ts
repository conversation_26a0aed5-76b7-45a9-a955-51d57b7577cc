/**
 * Lazy Component Loader
 * Dynamically loads components when they are detected in the DOM
 */

// Component to entry file mapping - supports both v2 and v3 versions
const COMPONENT_REGISTRY = {
    // v3 components
    'zwe-angelos-create-form-v3': () => import('../entries/forms'),
    'zwe-angelos-update-form-v3': () => import('../entries/forms'),
    'zwe-angelos-data-table-v3': () => import('../entries/data-table'),
    'zwe-angelos-details-view-v3': () => import('../entries/details-view'),
    'zwe-angelos-dynamic-view-v3': () => import('../entries/dynamic-view'),

    // v2 components (backward compatibility)
    'zwe-angelos-create-form-v2': () => import('../entries/forms'),
    'zwe-angelos-update-form-v2': () => import('../entries/forms'),
    'zwe-angelos-data-table-v2': () => import('../entries/data-table'),
    'zwe-angelos-details-view-v2': () => import('../entries/details-view'),
    'zwe-angelos-dynamic-view-v2': () => import('../entries/dynamic-view')
};

// Track loaded components to avoid duplicate loading
const loadedComponents = new Set<string>();
const loadingComponents = new Map<string, Promise<void>>();

// Statistics for debugging
let stats = {
    totalComponents: Object.keys(COMPONENT_REGISTRY).length,
    loadedComponents: 0,
    loadingAttempts: 0,
    upgradeAttempts: 0
};

/**
 * Load a specific component entry with proper dependency order
 */
async function loadComponent(componentName: string): Promise<void> {
    console.log(`🔍 loadComponent called for: ${componentName}`);

    if (loadedComponents.has(componentName)) {
        console.log(`✅ Component ${componentName} already loaded`);
        return;
    }

    if (loadingComponents.has(componentName)) {
        console.log(`⏳ Component ${componentName} already loading, waiting...`);
        return loadingComponents.get(componentName)!;
    }

    const loader = COMPONENT_REGISTRY[componentName as keyof typeof COMPONENT_REGISTRY];
    if (!loader) {
        console.warn(`⚠️ No loader found for component: ${componentName}`);
        console.log(`Available components:`, Object.keys(COMPONENT_REGISTRY));
        return;
    }

    console.log(`🔄 Loading component: ${componentName}`);
    stats.loadingAttempts++;

    const loadingPromise = (async () => {
        try {
            // Add a small delay to ensure proper initialization order
            await new Promise((resolve) => setTimeout(resolve, 50));

            console.log(`📦 Importing module for: ${componentName}`);
            const module = await loader();

            // Add another small delay after loading to ensure Vue is ready
            await new Promise((resolve) => setTimeout(resolve, 100));

            loadedComponents.add(componentName);
            stats.loadedComponents++;
            console.log(`✅ Component loaded: ${componentName}`, module);

            // Force upgrade elements after loading with additional delay
            setTimeout(() => {
                upgradeElements(componentName);
            }, 200);
        } catch (error) {
            console.error(`❌ Failed to load component ${componentName}:`, error);
            throw error;
        }
    })();

    loadingComponents.set(componentName, loadingPromise);

    loadingPromise.finally(() => {
        loadingComponents.delete(componentName);
    });

    return loadingPromise;
}

/**
 * Upgrade custom elements after component is loaded
 */
function upgradeElements(componentName: string): void {
    const elements = document.querySelectorAll(componentName);
    if (elements.length > 0) {
        console.log(`🔧 Upgrading ${elements.length} ${componentName} elements`);
        stats.upgradeAttempts++;

        elements.forEach((element) => {
            const customElementConstructor = customElements.get(componentName);
            if (customElementConstructor) {
                console.log(`🔧 Upgrading element:`, element);

                // Check if element is already upgraded
                if (!(element instanceof customElementConstructor)) {
                    customElements.upgrade(element);
                    console.log(`✅ Upgraded ${componentName} element`);
                } else {
                    console.log(`⚠️ Element already upgraded: ${componentName}`);
                }
            } else {
                console.warn(`⚠️ Custom element not defined yet: ${componentName}`);

                // Retry after a short delay
                setTimeout(() => {
                    const retryConstructor = customElements.get(componentName);
                    if (retryConstructor) {
                        console.log(`🔄 Retrying upgrade for ${componentName}`);
                        customElements.upgrade(element);
                    }
                }, 500);
            }
        });
    } else {
        console.log(`ℹ️ No ${componentName} elements found to upgrade`);
    }
}

/**
 * Scan DOM for components and load them
 */
async function scanAndLoadComponents(root: Element | Document = document): Promise<void> {
    console.log(`🔍 Scanning DOM for components...`);
    const componentNames = Object.keys(COMPONENT_REGISTRY);
    const foundComponents = new Set<string>();

    // Find all component instances in the DOM
    componentNames.forEach((componentName) => {
        const elements = root.querySelectorAll(componentName);
        if (elements.length > 0) {
            console.log(`📦 Found ${elements.length} instances of ${componentName}`);
            foundComponents.add(componentName);
        }
    });

    console.log(`🎯 Found components to load:`, Array.from(foundComponents));

    // Load found components sequentially to avoid race conditions
    for (const componentName of foundComponents) {
        try {
            console.log(`🔄 Loading component sequentially: ${componentName}`);
            await loadComponent(componentName);
        } catch (error) {
            console.error(`Failed to load and upgrade ${componentName}:`, error);
        }
    }
    console.log(`✅ Scan complete. Loaded ${foundComponents.size} component types.`);
}

/**
 * Set up DOM observers for automatic component loading
 */
function setupObservers(): void {
    console.log('👀 Setting up DOM observers...');

    // Mutation Observer - watches for new elements added to DOM
    const mutationObserver = new MutationObserver((mutations) => {
        let shouldScan = false;

        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        const tagName = element.tagName.toLowerCase();

                        // Check if the added element is a component we care about
                        if (COMPONENT_REGISTRY[tagName as keyof typeof COMPONENT_REGISTRY]) {
                            console.log(`🆕 Detected new component in DOM: ${tagName}`);
                            shouldScan = true;
                        }

                        // Also check if any child elements are components
                        const componentNames = Object.keys(COMPONENT_REGISTRY);
                        componentNames.forEach((componentName) => {
                            if (element.querySelector && element.querySelector(componentName)) {
                                console.log(
                                    `🆕 Detected new component in subtree: ${componentName}`
                                );
                                shouldScan = true;
                            }
                        });
                    }
                });
            }
        });

        if (shouldScan) {
            console.log('🔄 Mutation detected, scanning for components...');
            scanAndLoadComponents();
        }
    });

    mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Intersection Observer - loads components when they come into viewport
    const intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const componentName = element.tagName.toLowerCase();

                if (COMPONENT_REGISTRY[componentName as keyof typeof COMPONENT_REGISTRY]) {
                    loadComponent(componentName).then(() => {
                        upgradeElements(componentName);
                    });
                }
            }
        });
    });

    // Observe all existing component elements
    Object.keys(COMPONENT_REGISTRY).forEach((componentName) => {
        const elements = document.querySelectorAll(componentName);
        elements.forEach((element) => {
            intersectionObserver.observe(element);
        });
    });

    console.log('👀 DOM observers set up for lazy loading');
}

/**
 * Initialize the lazy loading system
 */
export function initializeLazyLoading(): void {
    console.log('🚀 Initializing lazy loading system...');

    // Initial scan for existing components
    scanAndLoadComponents();

    // Set up observers for future components
    setupObservers();

    console.log('✅ Lazy loading system initialized');
}

/**
 * Public API for manual component loading and debugging
 */
export const AngelosSDK = {
    // Load specific components
    async preload(componentNames: string[]): Promise<void> {
        const loadPromises = componentNames.map((name) => loadComponent(name));
        await Promise.all(loadPromises);
    },

    // Get loading statistics
    getStats() {
        return { ...stats };
    },

    // Debug methods
    debug: {
        isLoaded: (componentName: string) => loadedComponents.has(componentName),

        async forceLoadAndUpgrade(componentName: string): Promise<void> {
            await loadComponent(componentName);
            upgradeElements(componentName);
        },

        async upgradeAllElements(): Promise<void> {
            await scanAndLoadComponents();
        },

        getRegistry: () => ({ ...COMPONENT_REGISTRY }),
        getLoadedComponents: () => Array.from(loadedComponents),
        getLoadingComponents: () => Array.from(loadingComponents.keys())
    }
};

// Make SDK available globally
declare global {
    interface Window {
        AngelosSDK: typeof AngelosSDK;
    }
}

if (typeof window !== 'undefined') {
    window.AngelosSDK = AngelosSDK;
}
