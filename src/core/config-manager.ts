import { hasProperty, setProperty, getProperty } from 'dot-prop';
import { SERVICE_BASE_URL } from './constants';

// process.env --> import.meta.env
function getServiceBaseUrl() {
    if (!hasProperty(window, SERVICE_BASE_URL)) {
        setProperty(window, SERVICE_BASE_URL, null);
    }

    if (!import.meta.env.PROD) {
        // json server hostname
        return 'http://localhost:4000';
    }

    return import.meta.env.PROD
        ? getProperty(window, SERVICE_BASE_URL, null)
        : 'https://angelos-hercules.mum1-pp.zeta.in';
}

export const getServiceUrl = (): string => {
    const serviceBaseUrl = getServiceBaseUrl();

    if (!serviceBaseUrl) {
        throw new ReferenceError(`${SERVICE_BASE_URL} is not defined`);
    }

    if (typeof serviceBaseUrl !== 'string') {
        throw new TypeError(`${SERVICE_BASE_URL} is not a string`);
    }

    return serviceBaseUrl;
};
