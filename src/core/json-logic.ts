import jsonLogic from 'json-logic-js';
import { getProperty, hasProperty } from 'dot-prop';

/**
 * Json Logic can be extended to provide custom functionality
 * Visit -> https://jsonlogic.com/add_operation.html for documentation
 * Visit -> https://github.com/jwadhams/json-logic-js/blob/master/logic.js for code
 */

function getValue(path: any, defaultValue: any) {
    const notFound = defaultValue === undefined ? null : defaultValue;
    // @ts-ignore
    const data = this;
    if (typeof path === 'undefined' || path === '' || path === null) {
        return data;
    }
    if (hasProperty(data, path)) {
        return getProperty(data, path);
    }
    return notFound;
}

jsonLogic.add_operation('var', getValue);

export const extendedJsonLogic = jsonLogic;
