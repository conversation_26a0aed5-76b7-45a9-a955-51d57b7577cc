import _ from 'lodash';
import { executeTransformerFunction, isValidJsonLogic } from './utils';
import {
    ZTag,
    ZIcon,
    ZLink,
    ZButton,
    ZText,
    ZTooltip,
    ZDropdown,
    ZEllipsis,
    ZPopover
} from '@zeta-gds/components';
import { h } from 'vue';
import * as icons from '@zeta/icons';
import type {
    DataTableGeneratedConfig,
    InputParams,
    TableViewConfig,
    TableColumn,
    EmitFunction
} from '@/types';
/**
 * Exhaustive list of GDS components that would be supported by the custom templates for columns. This list has been derived from the previous data grid template support list
 */
const GdsComponents = {
    ZTag,
    ZIcon,
    ZLink,
    ZButton,
    ZText,
    ZTooltip,
    ZDropdown,
    ZPopover,
    ZEllipsis
};
export const gdsDataGridContractMapper = (
    config: TableViewConfig,
    inputParams: InputParams,
    emit: EmitFunction
): DataTableGeneratedConfig => {
    /**
     * Do we have ui pagination or be pagination ?
     * If the pagination params are not supplied, we can assume that it filtering etc would be on ui
     */
    const uiDataControl = !config.paginationConfig || config.uiDataControl;

    let processedColumns: TableColumn[] = config.tableConfig?.columns
        ? [...config.tableConfig.columns]
        : [];

    // 1. Filter columns based on condition.visibleIf
    if (config.tableConfig?.columns) {
        processedColumns = processedColumns.filter((column: TableColumn) => {
            if (
                column.condition &&
                column.condition.visibleIf &&
                typeof column.condition.visibleIf === 'object'
            ) {
                // inputParams (which includes context, params, tenantId, entityId) is used as the data for JSONLogic
                return isValidJsonLogic(column.condition.visibleIf, inputParams, {});
            }
            return true; // If no condition or no visibleIf rule, column is visible
        });
    }

    /**
     * Maps the application's column configuration
     * This function transforms our internal TableColumn objects into the column configuration objects
     * required by the UI component, with the following key operations:
     * - Maps field names to expected 'key' property
     * - Handles column alignment and title alignment properties
     * - Sets up sorting behavior based on uiDataControl setting
     * - Configures fixed/pinned columns
     * - Transfers type and multiple selection properties
     * - Sets up width constraints (width, minWidth, maxWidth)
     * - Creates render functions for custom cell rendering, with access to:
     *   - Row data (including the _uuid property for stable identification)
     *   - Input parameters from parent component
     *   - GDS component references for rendering
     *   - Event emission capability for custom interactions
     */
    const mappedColumns = processedColumns.map((column: TableColumn) => {
        const mappedColumn: any = {
            key: column.field,
            title: column.label,
            align: column.align || 'left',
            titleAlign: column.titleAlign || 'left',
            sorter:
                uiDataControl && Boolean(column.sortable)
                    ? defaultDataSorter(column.field, column.sortOrder)
                    : Boolean(column.sortable),
            sortOrder: column.sortOrder,
            fixed: column.pinned,
            ...(column.type ? { type: column.type } : {}),
            ...(column.multiple !== undefined ? { multiple: column.multiple } : {}),
            ...(column.width ? { width: column.width } : {}),
            ...(column.minWidth ? { minWidth: column.minWidth } : {}),
            ...(column.maxWidth ? { maxWidth: column.maxWidth } : {}),
            ...(column.selectionHandler ? { selectionHandler: column.selectionHandler } : {}),
            ...(column.render
                ? {
                      render: (rowData: any) => {
                          return executeTransformerFunction(column.render || '', {
                              row: rowData,
                              ...inputParams,
                              ...GdsComponents,
                              h,
                              icons,
                              emitEvent: (eventName: string, payload: any) => {
                                  emit('custom-events', {
                                      eventName,
                                      payload
                                  });
                              }
                          });
                      }
                  }
                : {})
        };

        return mappedColumn;
    });

    const generatedConfigBase: DataTableGeneratedConfig = {
        title: config.title,
        description: config.description,
        uiDataControl,
        tableActions: config.tableActions?.length ? config.tableActions : [],
        paginationConfig: config.paginationConfig,
        tableConfig: {
            // Initialize with other tableConfig properties from original config if they exist
            ...(config.tableConfig || {}),
            columns: mappedColumns // Override with processed columns
        }
    };
    const finalConfig: DataTableGeneratedConfig = {
        ...config,
        ...generatedConfigBase,
        tableConfig: {
            ...(config.tableConfig || {}),
            ...(generatedConfigBase.tableConfig || {}),
            columns: mappedColumns
        }
    };

    return finalConfig;
};

/**
 * Function to get the render table actions context
 */
export const getRenderTableActionContext = (inputParams: InputParams, emit: EmitFunction) => {
    return {
        h,
        icons,
        ...GdsComponents,
        ...inputParams,
        emitEvent: (eventName: string, payload: any) => {
            emit('custom-events', {
                eventName,
                payload
            });
        }
    };
};
/*
 * Writing the defaultDataSorter function to sort the data based on the key and order
 * The sorting offered by GDS does not account for undefined or null properties
 */
function defaultDataSorter(key: string, order: 'descend' | 'ascend' | false = 'ascend') {
    return (a: any, b: any) => {
        try {
            if (typeof a[key] === 'string' || typeof b[key] === 'string') {
                if (!a[key]) {
                    return order === 'ascend' ? -1 : 1;
                } else if (!b[key]) {
                    return order === 'ascend' ? 1 : -1;
                }

                return order === 'ascend'
                    ? a[key]?.localeCompare(b[key])
                    : b[key]?.localeCompare(a[key]);
            }
            return order === 'ascend' ? a[key] - b[key] : b[key] - a[key];
        } catch (e) {
            console.error(e);
            //If an error occurs, return unsorted data
            return 0;
        }
    };
}

export function deepMergeWithLodash<T, U>(obj1: T, obj2: U): T & U {
    return _.merge({}, obj1, obj2);
}
