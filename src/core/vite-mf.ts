/* eslint-disable @typescript-eslint/ban-ts-comment */
import camelCase from 'camelcase';
import { getProperty, hasProperty } from 'dot-prop';
import { GLOBAL_ASSETS_VARIABLES } from '@/core/constants';
import { FEDERATED_DEPENDENCIES } from '@hercules/build-context';
import { getPluginConfig } from '@hercules/context';

const ErrorTypes = {
    MODULE_SCRIPT_LOADING_ERROR: 'MODULE_SCRIPT_LOADING_ERROR',
    MODULE_NOT_FOUND_ERROR: 'MODULE_NOT_FOUND_ERROR',
    ASSET_BASE_URL_NOT_FOUND_ERROR: 'ASSET_BASE_URL_NOT_FOUND_ERROR',
    UNRESOLVED_ASSET_BASE_URL: 'UNRESOLVED_ASSET_BASE_URL',
    DUPLICATE_SCRIPT_LOADING_ERROR: 'DUPLICATE_SCRIPT_LOADING_ERROR'
};

class <PERSON><PERSON><PERSON>r extends Error {
    constructor(
        public type: string,
        public description: string
    ) {
        super(`${type}: description`);
        this.name = 'FederationError';
    }
}

/**
 * @returns federated dependencies from hercules context object
 */
const getFederatedDependencies = () => {
    const tenantFederatedDependencies = getPluginConfig('federatedDependencies');

    if (tenantFederatedDependencies && typeof tenantFederatedDependencies === 'object') {
        return Object.assign({}, FEDERATED_DEPENDENCIES, tenantFederatedDependencies);
    }

    return FEDERATED_DEPENDENCIES;
};

const initWebpackFederatedModule = async (scope: string, module: string) => {
    // Initializes the share scope. This fills it with known provided modules from this build and all remotes

    // Ensure Webpack Federation scope is initialized
    // @ts-ignore
    // await __webpack_init_sharing__('default');
    // @ts-ignore
    const container = window[scope]; // or get the container somewhere else
    if (!container) throw new Error(`Container ${scope} not found`);

    // @ts-ignore
    if (!window.__vite_share_scopes__) {
        // @ts-ignore
        window.__vite_share_scopes__ = {};
    }
    // Initialize the container, it may provide shared modules
    // @ts-ignore
    await container.init(__vite_share_scopes__.default);
    // @ts-ignore
    const factory = await container.get(module);
    return factory();
};

/**
 * For loading script whose url is passed as the parameter
 */
const loadScript = (url: string, scope: string) => {
    // @ts-ignore
    if (window[scope]) return; // Already loaded
    return new Promise((res, rej) => {
        if (document.querySelector(`script[src="${url}"]`)) {
            res(void 0);
            return;
        }

        const element = document.createElement('script');

        element.src = url;
        element.type = 'text/javascript';
        element.async = true;
        element.onload = () => {
            // @ts-ignore
            res(void 0);
        };
        element.onerror = () => {
            console.error(`Dynamic Script Error: ${url}`);
            element.remove();
            rej(
                new FederationError(
                    ErrorTypes.MODULE_SCRIPT_LOADING_ERROR,
                    `Dynamic script loading error: ${url}`
                )
            );
        };

        document.head.appendChild(element);
    });
};

async function waitForContainerInit(moduleId: string, timeout = 5000) {
    const start = Date.now();
    // @ts-ignore
    while (!window[moduleId]) {
        if (Date.now() - start > timeout)
            throw new Error(`Remote container ${moduleId} not found after waiting`);
        await new Promise((resolve) => setTimeout(resolve, 100));
    }
}

/**
 * load script using the federated module url then wait till container initialization
 * @param option
 * @returns all shared modules (federated modules)
 */
export const loadFederatedModule = async (option: {
    url: string;
    moduleId: string;
    exportName: string;
}) => {
    let module = null;
    try {
        // @ts-ignore
        await loadScript(option.url, option.moduleId);
        await waitForContainerInit(option.moduleId);
        // @ts-ignore
        const container = window[option.moduleId];
        if (!container) throw new Error(`Remote container ${option.moduleId} not found`);
        module = await initWebpackFederatedModule(option.moduleId, option.exportName);
    } catch (e) {
        console.error(e);
        throw e;
    }
    return module;
};

// check for '__BUSINESS_COMPONENTS_BASE_URL__' or '__HERCULES__.$assetsBaseUrl' on window
const getBaseUrl = () => {
    const key = GLOBAL_ASSETS_VARIABLES.find((key) => hasProperty(window, key));

    if (key) {
        return getProperty(window, key);
    }

    return new FederationError(
        ErrorTypes.ASSET_BASE_URL_NOT_FOUND_ERROR,
        'Asset base URL not found. It was checked under __BUSINESS_COMPONENTS_BASE_URL__ and __HERCULES__.$assetsBaseUrl global variables'
    );
};

/**
 *
 * @param moduleId same as appCode we use for Hercules application
 * @param version if the version is not passed, it will look for module version in federated dependencies inside package.json, if not found there as well, it will pick the latest version
 * @returns baseurl(fetched from window using getBaseUrl)/moduleId(passed as parameter)/version(check from federated dependencies declared in hercules context)/module.entry.js
 * @query need to update for module-legacy.entry.js
 */
function resolveUrl(moduleId: string, version?: string) {
    const baseUrl = getBaseUrl();

    if (!version) {
        const federatedDependencies = getFederatedDependencies();
        if (moduleId in federatedDependencies) {
            version = federatedDependencies[moduleId];
        }
    }
    if (baseUrl) {
        return [baseUrl, moduleId, version, 'module.entry.js'].filter(Boolean).join('/');
    }

    throw new FederationError(
        ErrorTypes.UNRESOLVED_ASSET_BASE_URL,
        `Unable to resolve assets url for ${moduleId}`
    );
}

/**
 * This function is used to load dynamic federated modules
 * @param importStr
 * @returns a promise with the module requested via moduleId (application code) and exportName (module name defined while exposing remotes)
 * @example importStr 'h-aphrodite/$store' i.e. modulesId 'h-aphrodite', exportName 'store'
 * will fetch the store for h-aphrodite application, store is remote entry exposed by default for all hercules application
 */
export const importModule = async (importStr: string, version?: string) => {
    const [moduleId, exportName] = importStr.split('/');

    return await loadFederatedModule({
        exportName: `./${exportName}`,
        moduleId: camelCase(moduleId),
        url: resolveUrl(moduleId, version)
    });
};
/**
 * This function is used to check if a remote module is exported for an application
 * @param importStr
 */
export const isModuleExisting = async (importStr: string) => {
    const [moduleId, exportName] = importStr.split('/');
    const hasModules = (await importModule(`${moduleId}/$hasInternalModules`)).default;
    return hasModules[exportName];
};
