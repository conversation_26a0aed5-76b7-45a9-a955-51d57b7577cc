import { getProperty, setProperty } from 'dot-prop';
import { DEFAULT_GRID_COLUMNS, FIELD_TYPES } from './constants';
import { executeTransformerFunction, flattenObject, hardDeepCopy, isValidJsonLogic } from './utils';
import { useDynamicComponent } from '@/composable';
import type {
    CONDITION,
    CUSTOM_CONFIG,
    SECTION_FIELD_ITEM,
    SECTION_ITEM,
    JSON_LOGIC_PARAMS,
    ALL_DATA,
    FormData,
    SectionConfig,
    ComponentMap,
    Context
} from '@/types/core';

const { generateDynamicComponent } = useDynamicComponent();

export const conditionList: CONDITION[] = [
    { rule: 'disabledIf', propToSet: 'isDisabled', defaultValue: false },
    { rule: 'visibleIf', propToSet: 'isVisible', defaultValue: true }
];

export function executeJsonLogic(
    params: JSON_LOGIC_PARAMS,
    context: any = {},
    data: ALL_DATA = {}
) {
    const { defaultValue, field, propToCheck } = params;
    if (field && field.condition && field.condition[propToCheck]) {
        return isValidJsonLogic(field.condition[propToCheck], context, {
            ...data
        });
    }
    return defaultValue;
}

export function conditionHandler(
    sectionList: SECTION_ITEM[] | SECTION_FIELD_ITEM[],
    context: any,
    additionalData: any = {}
) {
    sectionList.forEach((section) => {
        if (section?.props?.condition) {
            conditionList.forEach((condition) => {
                section.props[condition.propToSet] = executeJsonLogic(
                    {
                        field: section.props,
                        propToCheck: condition.rule,
                        defaultValue: condition.defaultValue
                    },
                    context,
                    additionalData
                );
            });
        }
        if (section?.sections) {
            conditionHandler(section.sections, context, additionalData);
        } else if (section?.fields) {
            conditionHandler(section.fields, context, additionalData);
        }
    });
}

export function getConditionalKeyValues(section: any, conditionList: CONDITION[], context: any) {
    return conditionList.reduce((accumulator: any, condition) => {
        accumulator[condition.propToSet] = executeJsonLogic(
            {
                field: section,
                propToCheck: condition.rule,
                defaultValue: condition.defaultValue
            },
            context
        );
        return accumulator;
    }, {});
}

export function parseSection(section: SectionConfig, componentMap: ComponentMap, context: Context) {
    const gridColumns =
        section.layoutType == 'grid'
            ? { gridColumns: section.gridColumns || DEFAULT_GRID_COLUMNS }
            : {};
    const newSectionItem: any = {
        id: section?.id || '',
        title: section?.title || '',
        subtitle: section?.subtitle || '',
        props: {
            condition: section?.condition || {},
            ...getConditionalKeyValues(section, conditionList, context)
        },

        // DEVNOTE: layoutType need to removed later, as we are using direction only.
        layoutType: section?.layoutType || 'vertical',
        class: section?.class || '',

        // DEVNOTE: columns  attribute define the size of this section
        columns: section.columns || 12,

        // DEVNOTE: Direction will define the flow of its child element (sections or fields), default will be vertical
        direction: ['vertical', 'horizontal'].includes(section?.direction)
            ? section?.direction
            : 'vertical',

        // DENOTE: An object to define the UI property of this section
        attributes: section.attributes || {},

        // DEVNOTE: Angelos is using header-attributes to pass GDS props of ZUtilityHeader from the config
        headerAttributes: section['header-attributes'] || section['headerAttributes'] || {}, // TODO: 'header-attributes' to be removed

        // DEVNOTE: Angelos is using accordion-attributes to pass GDS props of ZAccordion and ZAccordionItem from the config
        accordionAttributes:
            section['accordion-attributes'] || section['accordionAttributes'] || {}, // TODO: 'accordion-attributes' to be removed
        ...gridColumns
    };

    // DEVNOTE: In New Layout sections can contain fields and sections array
    // note: in sections, sections and fields CAN'T co-exist
    if (section.customField) {
        return handleCustomField(componentMap, section, newSectionItem);
    } else if (section.arrayField) {
        return handleArrayFiled(componentMap, section, newSectionItem);
    } else if (section.sections) {
        const subSectionsArray = section.sections;
        const newComponentMap = componentMap;

        const parseResult = subSectionsArray.map((section: any) => {
            return parseSection(section, newComponentMap, context);
        });
        newSectionItem.sections = parseResult;
    } else if (section.fields) {
        const newComponentMap = componentMap;
        newSectionItem.fields = parseSectionFields(section.fields, newComponentMap, context);
    }

    return newSectionItem;
}

function handleArrayFiled(componentMap: ComponentMap, section: SectionConfig, newSectionItem: any) {
    const newComponentMap = componentMap[section.arrayField]['fields'];
    newSectionItem.arrayField = section.arrayField;
    newSectionItem.itemTitleTransformer = section.itemTitleTransformer;
    newSectionItem.viewConfigSection = hardDeepCopy(section);
    newSectionItem.viewConfigFields = hardDeepCopy(
        componentMap?.[section.arrayField]?.['viewConfigFields'] || {}
    );
    delete newSectionItem.viewConfigSection.arrayField;
    newSectionItem.componentMap = newComponentMap;
    return newSectionItem;
}

function handleCustomField(
    componentMap: ComponentMap,
    section: SectionConfig,
    newSectionItem: any
) {
    const newComponentMap = componentMap[section.customField];
    newSectionItem.customField = section.customField;
    newSectionItem.itemTitleTransformer = section.itemTitleTransformer;
    newSectionItem.viewConfigSection = hardDeepCopy(section);
    newSectionItem.componentMap = newComponentMap;
    return newSectionItem;
}

export function parseSectionFields(fields: any[], componentMap: any, context: any) {
    const sectionFields: any[] = [];
    fields.forEach((field: any) => {
        const fieldKey = field['key'] || field['field_key'];
        // DEVNOTE new layout (angelos v2) supports fields as an array with field_key as a placeholder for field
        if (typeof fieldKey === 'string') {
            const fieldConfig = { ...(componentMap[fieldKey] || {}) };
            const component = JSON.parse(
                JSON.stringify(getProperty(fieldConfig, 'name', 'AngelosInput'))
            );

            fieldConfig.component = component;
            fieldConfig.fieldKey = fieldKey;
            fieldConfig.colspan = field.colspan;
            //DEVNOTE Currently its not being used, will add support for width in px and %
            fieldConfig.width = field.width;
            fieldConfig.id = field.id || '';
            fieldConfig.class = field.class;
            sectionFields.push(fieldConfig);
        }
        // DEVNOTE This condition is for old angelos schema
        else if (typeof field == 'string') {
            const fieldConfig = componentMap[field];
            const component = JSON.parse(
                JSON.stringify(getProperty(fieldConfig, 'name', 'AngelosInput'))
            );

            fieldConfig.component = component;
            fieldConfig.fieldKey = field;

            sectionFields.push(fieldConfig);
        } else {
            const fieldConfig = componentMap[field.arrayFieldKey];
            const fieldType = fieldConfig.type ? fieldConfig.type.toLowerCase() : '';
            if (fieldType === 'array') {
                const arrayFieldKey = field.arrayFieldKey;
                const arraySectionConfig = field;

                const sectionListItem: any = parseSection(arraySectionConfig, fieldConfig, context);

                sectionListItem.allFields = [[...sectionListItem.fields]];
                sectionListItem.sectionType = 'array-section';
                sectionListItem.fieldKey = arrayFieldKey;

                sectionFields.push(sectionListItem);
            } else {
                console.error('Invalid section config');
            }
        }
    });
    return sectionFields;
}

export function isDynamicComponent(handlerAttributePath: string, component: string) {
    const hasDynamicAttribute = ['value', 'template'].some(
        (attribute) =>
            handlerAttributePath.startsWith(attribute) ||
            handlerAttributePath.endsWith('.' + attribute)
    );
    return component === 'AngelosTemplate' && hasDynamicAttribute;
}

function getAllFieldsInArrayElement(sections: any) {
    let fieldsArray: any = {};
    sections.forEach((section: any) => {
        if (section.fields) {
            section.fields.forEach((field: any) => {
                fieldsArray[field.fieldKey] = field;
            });
        } else if (section.sections) {
            fieldsArray = { ...fieldsArray, ...getAllFieldsInArrayElement(section.sections) };
        } else if (section.fieldKey) {
            fieldsArray[section.fieldKey] = section;
        }
    });
    return fieldsArray;
}

export function getFieldPath(
    componentMap: any,
    fieldKey: string,
    handlerAttributePath: string
): string {
    if (componentMap[fieldKey]?.type === FIELD_TYPES.ARRAY) {
        const splitCurrentPath = handlerAttributePath.split('.');
        if (splitCurrentPath.length > 1) {
            const attribute = splitCurrentPath.pop();
            return `${splitCurrentPath.join('.')}.props.${attribute}`;
        }
        return `props.${handlerAttributePath}`;
    } else if (componentMap[fieldKey]?.type === FIELD_TYPES.CUSTOM) {
        const splitCurrentPath = handlerAttributePath.split('config.props.');
        if (splitCurrentPath.length > 1) {
            const attribute = splitCurrentPath.pop();
            return `compConfig.props.${attribute}`;
        }
        return `props.${handlerAttributePath}`;
    }
    return `props.${handlerAttributePath}`;
}

export function isValueHandler(handlerAttributePath: string) {
    return handlerAttributePath === 'value' || handlerAttributePath.endsWith('.value');
}

export function setFileUploadValue(fileUploaderFields: Map<any, any>, key: string, value: any) {
    if (fileUploaderFields.has(key)) {
        const currentValue = fileUploaderFields.get(key);
        fileUploaderFields.set(key, {
            ...currentValue,
            upload: value
        });
    }
}

export function dynamicFieldConfigHandlerForArray(
    formData: FormData,
    contextData: any = {},
    key?: string,
    additionalData: any = {},
    stateHelpers?: {
        getFormModelFromPath: (path: any, defaultValue?: any) => any;
        updateFormState: (key: any, payload: any) => void;
    }
) {
    const { componentMap, fileUploaderFields, formFields } = formData;
    const allFields = getAllFieldsInArrayElement(componentMap);
    Object.keys(formFields).forEach((fieldKey) => {
        const fieldConfig = formFields[fieldKey];
        const flattenFieldConfig = flattenObject(fieldConfig);
        const attributesWithHandler = Object.keys(flattenFieldConfig).filter((attributePath) =>
            attributePath.endsWith('.handler')
        );
        attributesWithHandler.forEach((handler) => {
            const handlerAttributePath = handler.slice(0, -'.handler'.length);
            const handlerValue = flattenFieldConfig[handler];
            if (isDynamicComponent(handlerAttributePath, (fieldConfig as any)?.component)) {
                const dynamicComponent = generateDynamicComponent(
                    handlerValue || '',
                    contextData,
                    []
                );
                getProperty(allFields[fieldKey].props, handlerAttributePath, dynamicComponent);
            } else {
                const calculatedValue = executeTransformerFunction(handlerValue, {
                    ...contextData,
                    ...additionalData
                });
                const fieldPropsPath = getFieldPath(allFields, fieldKey, handlerAttributePath);
                if (
                    allFields[fieldKey]?.name === 'AngelosArray' &&
                    isValueHandler(handlerAttributePath)
                ) {
                    if (
                        (
                            getProperty(allFields[fieldKey].props, handlerAttributePath) as any
                        )?.dependencies?.includes(key)
                    ) {
                        if (stateHelpers?.getFormModelFromPath) {
                            stateHelpers.getFormModelFromPath(fieldKey, calculatedValue);
                        }
                        // eventBus.$emit('update-form-model', formModel);
                    }
                } else {
                    setProperty(allFields[fieldKey], fieldPropsPath, calculatedValue);
                }
                /*  
                fileUploaderFields is a map that i.e set during form initialization. 
                We have to update the upload property of fileUploaderFields map when upload property is changed through handler.
                */
                if (fieldPropsPath.endsWith('upload')) {
                    setFileUploadValue(fileUploaderFields, fieldKey, calculatedValue);
                }
            }
        });
    });
}

export function dynamicFieldConfigHandler(
    formData: FormData,
    contextData: any = {},
    key?: string,
    additionalData: any = {},
    stateHelpers?: {
        getFormModelFromPath: (path: any, defaultValue?: any) => any;
        updateFormState: (key: any, payload: any) => void;
    }
) {
    const { componentMap, fileUploaderFields, formFields } = formData;
    /* Adding support for handler to write dynamicField configuration as it is more intuitive. dynamicFieldAttrs support will be slowly deprecated */
    Object.keys(formFields).forEach((fieldKey) => {
        // To access formFields we avoid dot prop as the formFields object directly picks the key values from field config without parsing the the key.
        const fieldConfig = formFields[fieldKey];
        const flattenFieldConfig = flattenObject(fieldConfig);
        const attributesWithHandler = Object.keys(flattenFieldConfig).filter((attributePath) =>
            attributePath.endsWith('.handler')
        );
        attributesWithHandler.forEach((handler) => {
            const handlerAttributePath = handler.slice(0, -'.handler'.length);
            const handlerValue = flattenFieldConfig[handler];
            /* In case of AngelosTemplate component, we will recompile template. */
            if (isDynamicComponent(handlerAttributePath, (fieldConfig as any)?.component)) {
                const dynamicComponent = generateDynamicComponent(
                    handlerValue || '',
                    contextData,
                    []
                );
                setProperty(componentMap[fieldKey].props, handlerAttributePath, dynamicComponent);
            } else {
                const calculatedValue = executeTransformerFunction(handlerValue, {
                    ...contextData,
                    ...additionalData
                });
                const fieldPropsPath = getFieldPath(componentMap, fieldKey, handlerAttributePath);
                if (
                    componentMap[fieldKey]?.name === 'AngelosArray' &&
                    isValueHandler(handlerAttributePath)
                ) {
                    if (
                        (
                            getProperty(componentMap[fieldKey].props, handlerAttributePath) as any
                        )?.dependencies?.includes(key)
                    ) {
                        if (stateHelpers?.updateFormState) {
                            stateHelpers.updateFormState('FORM_MODEL', {
                                path: fieldKey,
                                value: calculatedValue
                            });
                        }
                    }
                } else {
                    setProperty(componentMap[fieldKey], fieldPropsPath, calculatedValue);
                }
                /*  
                fileUploaderFields is a map that i.e set during form initialization. 
                We have to update the upload property of fileUploaderFields map when upload property is changed through handler.
                */
                if (fieldPropsPath.endsWith('upload')) {
                    setFileUploadValue(fileUploaderFields, fieldKey, calculatedValue);
                }
            }
        });
    });
}

export function validateCustomCompField(field: { config: CUSTOM_CONFIG }) {
    const erroMsgs: string[] = [];
    if (!field.config) {
        erroMsgs.push('Custom Component config is missing');
        return erroMsgs;
    }
    if (!field.config.name) {
        erroMsgs.push('Component name is missing');
    }
    if (!field.config.package) {
        erroMsgs.push('Package details is missing');
    } else {
        if (!field.config.package.name) {
            erroMsgs.push('Package name is missing');
        }
        if (!field.config.package.version) {
            erroMsgs.push('Package version is missing');
        }
    }

    return erroMsgs;
}
