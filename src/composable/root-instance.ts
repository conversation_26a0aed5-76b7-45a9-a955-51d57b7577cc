import { useCurrentElement } from '@vueuse/core';
import { isShadowRoot } from '../core/utils';
export const useRootInstance = (
    defaultValue: Document | ShadowRoot = document
): Document | ShadowRoot => {
    let shadowRoot: ShadowRoot | Document = defaultValue;

    const elementRef = useCurrentElement<HTMLElement>();
    if (elementRef.value) {
        const shadow = elementRef.value.getRootNode() as ShadowRoot;
        if (isShadowRoot(shadow)) {
            shadowRoot = shadow;
        }
    }
    return shadowRoot;
};
