import { computed, ref, type InjectionKey, type Ref } from 'vue';
import { getProperty, setProperty } from 'dot-prop';
import { executeTransformerFunction, hardDeepCopy } from '../core/utils';
import { convertPath } from '../core/path-utils';
import type { ErrorStructure } from '../types/form';
import { FORM_STATE_KEYS } from '../core/constants';
import type { ComponentType } from '@/types';

export type ComponentStore = ReturnType<typeof useComponentStore>;
export const StoreKey: InjectionKey<ComponentStore> = Symbol('useComponentStore');

/**
 * Composable to manage internal state of a form-driven component instance
 * Ensures complete state isolation across instances
 */
export function useComponentStore(
    entityID: string,
    tenantID: string,
    componentType: ComponentType
) {
    // ——— Reactive State ———
    const formModelState = ref<Record<string, any>>({});
    const prefillData = ref<Record<string, any>>({});
    const context = ref<Record<string, any>>({});
    const params = ref<Record<string, any>>({});
    const rules = ref<Record<string, any>>({});
    const fileUploadFields = ref(new Map<string, any>());
    const lastPropertyUpdated = ref('');
    const errorValidation = ref<ErrorStructure>([]);
    const componentTypeRef = ref(componentType);

    const entityId = ref(entityID);
    const tenantId = ref(tenantID);

    // ——— Computed Getters ———
    const formModel = computed(() => formModelState.value);
    const parsedFileUploadFields = computed(() => fileUploadFields.value);

    // ——— Actions ———
    function updateFormModel(path: string, value: any) {
        lastPropertyUpdated.value = path;
        setProperty(formModelState.value, convertPath(path), value);
    }

    function updatePrefillData(data: any) {
        prefillData.value = data;
    }

    function getFormModelFromPath(path: string, defaultValue: any = null) {
        return getProperty(formModelState.value, convertPath(path), defaultValue);
    }

    function clearValue() {
        formModelState.value = {};
    }

    function getInputParams() {
        return {
            tenantId: tenantId.value,
            entityId: entityId.value,
            params: { ...params.value },
            context: { ...context.value }
        };
    }

    function getContextualData() {
        return {
            ...getInputParams(),
            formModel: formModel.value,
            prefillData: prefillData.value
        };
    }

    function createInitialFormModel(viewConfig: any) {
        const fields = viewConfig?.value?.fields || {};

        Object.keys(fields).forEach((key) => {
            const field = hardDeepCopy(fields[key]);
            const sanitizedKey = convertPath(key);
            const fieldType = (field.type || '').toLowerCase();
            const componentName = getProperty(
                field,
                'component.name',
                field.component || 'AngelosInput'
            );
            const handler = getProperty(field, 'value.handler', '');
            const alreadySet = Object.prototype.hasOwnProperty.call(formModel.value, key);

            /*
                If key is not present in formModel, we know that it is being set for first time.
                If field config has a value.handler property, we can execute handler and set the initital value returned by handler. handler is used to provide dynamic content.
                If component is AngelosTemplate, then we don't have to set any value in formField as it is a component and it's value is null. Also before rendering the form, we always compile AngelosTemplate component. Look at parseFormFields() function
            */
            if (!alreadySet && handler && componentName !== 'AngelosTemplate') {
                const initialValue = executeTransformerFunction(handler, getContextualData());
                updateFormModel(key, initialValue);
            } else {
                const defaultValue = fieldType === 'array' ? [] : null;
                const value = hardDeepCopy(
                    getProperty(context.value, sanitizedKey) ??
                        getFormModelFromPath(key) ??
                        getProperty(prefillData.value, sanitizedKey) ??
                        field.value ??
                        defaultValue
                );

                if (fieldType === 'array' && !Array.isArray(value)) {
                    throw new Error(
                        `Array field "${key}" must receive an array. Got: ${typeof value}`
                    );
                }

                updateFormModel(key, value);
            }
        });
    }

    function updateRule(path: string, value: any) {
        setProperty(rules.value, convertPath(path), value);
    }

    function updateFormState(key: keyof typeof FORM_STATE_KEYS, payload: any) {
        switch (key) {
            case FORM_STATE_KEYS.PREFILL_DATA:
                updatePrefillData(payload);
                break;
            case FORM_STATE_KEYS.FORM_MODEL:
                updateFormModel(payload.path, payload.value);
                break;
            case FORM_STATE_KEYS.CONTEXT:
                context.value = payload;
                break;
            case FORM_STATE_KEYS.PARAMS:
                params.value = payload;
                break;
            case FORM_STATE_KEYS.RULES:
                updateRule(payload.path, payload.value);
                break;
            case FORM_STATE_KEYS.FILE_UPLOAD_FIELDS: {
                const { formFieldKey, files } = payload;
                const current = fileUploadFields.value.get(formFieldKey) || {};
                fileUploadFields.value.set(formFieldKey, { ...current, files });
                break;
            }
            case FORM_STATE_KEYS.ERROR_VALIDATION:
                errorValidation.value = payload;
                break;
        }
    }

    return {
        formModel,
        prefillData: computed(() => prefillData.value),
        rules,
        fileUploadFieldsMap: parsedFileUploadFields,
        lastPropertyUpdated,
        errorValidation,

        updateFormModel,
        getFormModelFromPath,
        clearValue,
        updateFormState,
        createInitialFormModel,
        getInputParams,
        getContextualData,
        componentTypeRef
    };
}
