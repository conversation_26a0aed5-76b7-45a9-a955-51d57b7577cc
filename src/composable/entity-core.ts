import { ref, onMounted, type ComponentInternalInstance } from 'vue';
import { getAuthToken } from '@/core/service';
import { useRootInstance } from './root-instance';
import { type ComponentStore } from './useComponentStore';
import type { ComponentType } from '@/types';

interface EntityCoreProps {
    context: string | object;
    params: string | object;
    tenantId: string;
    entityId: string;
    componentType: ComponentType;
    instance?: ComponentInternalInstance | null;
    store: ComponentStore;
}

export function useEntityCore({ context, params, store }: EntityCoreProps) {
    const shadowRoot = ref<ShadowRoot | null>(null);

    const { updateFormState, getInputParams } = store;

    const parsedContext = ref(parsedValue(context));
    updateFormState('CONTEXT', parsedContext.value);

    const parsedParams = ref(parsedValue(params));
    updateFormState('PARAMS', parsedParams.value);

    function parsedValue(value: string | object) {
        if (typeof value === 'object') {
            return value;
        } else if (typeof value === 'string') {
            try {
                return JSON.parse(value);
            } catch (error) {
                console.error(error);
                return {};
            }
        }
    }

    function exposeFunctionOnHost(name: string, callback: Function) {
        ((shadowRoot.value?.getRootNode() as any)?.host || {})[name] = callback;
    }

    function attachCss(cssString: string) {
        if (cssString && typeof cssString === 'string') {
            const style = document.createElement('style');
            style.innerText = cssString;
            shadowRoot.value?.appendChild(style);
        }
    }

    function getHarRequestParams() {
        return {
            authToken: getAuthToken(),
            ...getInputParams()
        };
    }

    onMounted(() => {
        shadowRoot.value = useRootInstance() as ShadowRoot;
    });

    return {
        parsedContext,
        parsedParams,
        attachCss,
        getInputParams,
        getHarRequestParams,
        exposeFunctionOnHost
    };
}
