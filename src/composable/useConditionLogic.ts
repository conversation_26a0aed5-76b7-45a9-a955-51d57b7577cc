import { ref, computed, type ComputedRef } from 'vue';

interface Condition {
    propToCheck: string;
    defaultValue: boolean;
}

export default function useConditionLogic(data: any) {
    const isLoading = ref(true);

    function executeJsonLogic(condition: Condition): ComputedRef<boolean> {
        return computed(() => {
            // Your JSON logic execution here
            // Use `data` to access context and form data
            // Example: return data.someProperty === someCondition;
            return true; // Placeholder return value
        });
    }

    function isFormButtonDisabled(formBtn: any): ComputedRef<boolean> {
        return computed(() => {
            const isDisabledByLogic = executeJsonLogic({
                propToCheck: 'disabledIf',
                defaultValue: false
            });
            return isDisabledByLogic.value;
        });
    }

    function isFormButtonVisible(formBtn: any): ComputedRef<boolean> {
        return computed(() => {
            const isVisibleByLogic = executeJsonLogic({
                propToCheck: 'visibleIf',
                defaultValue: true
            });
            return !isLoading.value && isVisibleByLogic.value;
        });
    }

    return {
        isLoading,
        executeJsonLogic,
        isFormButtonDisabled,
        isFormButtonVisible
    };
}
