import dayjs from 'dayjs';
import { type App, getCurrentInstance, onMounted, ref } from 'vue';

export function useDynamicComponent() {
    const appInstance = ref<App<any> | undefined>(undefined);
    const shadowRoot = ref<ShadowRoot | null>(null);

    const generateDynamicComponent = (
        template: string,
        data: object = {},
        props: string[] = []
    ) => {
        const name = `DynamicComponent${Math.random().toString().slice(-8)}`;

        // const compiledTemplate = compile(template);

        /* Add dayjs package support inside angelos template */
        Object.assign(data, { dayjs });

        const templateWithId = `<div id="${name}">${template}</div>`;

        appInstance.value?.component(name, {
            template: templateWithId,
            props,
            data: function () {
                return data;
            },
            methods: {
                // Method to emit event from a dynamic component
                handleClick(event: any) {
                    this.$emit('action-click', name, this.$el.innerHTML);
                }
            }
        });

        return appInstance.value?.component(name);
    };

    onMounted(() => {
        appInstance.value = getCurrentInstance()?.appContext.app;
        shadowRoot.value = getCurrentInstance()?.vnode?.el?.parentNode;
    });

    return {
        generateDynamicComponent
    };
}

export function useExternalComponent() {
    const appInstance = ref<App<any> | undefined>(undefined);

    const registerExternalComponent = (name: string, component: any) => {
        appInstance.value?.component(name, component);
    };

    onMounted(() => {
        appInstance.value = getCurrentInstance()?.appContext.app;
    });

    return {
        registerExternalComponent
    };
}
