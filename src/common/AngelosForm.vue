<template>
    <AngelosEntityConfigProvider
        :entity-id="entityId"
        :tenant-id="tenantId"
        :component-type="componentType"
        :context="context"
        @data="onData"
    >
        <!-- Loading State -->
        <div v-if="isLoading">
            <ZSkeleton class="card-skeleton" viewBox="0 0 250 230" :speed="3">
                <rect x="16" y="25" width="20" height="10" rx="2" />
                <rect x="16" y="40" width="48" height="10" rx="2" />
                <rect x="16" y="55" width="210" height="10" rx="2" />
                <rect x="16" y="70" width="210" height="10" rx="2" />
            </ZSkeleton>
        </div>

        <!-- Error State -->
        <div v-else-if="showErrorModal">
            <ZResult
                v-bind="prefillErrorAttrs"
                variant="image"
                :status="prefillErrorStatusCode.toString()"
            >
                <template #actions>
                    <ZButton variant="filled" color="primary" @click="getPrefillData()">
                        Try Again
                    </ZButton>
                </template>
            </ZResult>
        </div>

        <!-- Main Form Content -->
        <div v-else>
            <z-form
                ref="formInstRef"
                :class="componentType"
                :model="formModel"
                :rules="rules"
                @submit.prevent="handleSubmit"
            >
                <!-- TODO: Add submit -->
                <div class="container">
                    <!-- TODO: ZUtilityContainer from GDS-->
                    <AngelosSimpleFormSectionV2> </AngelosSimpleFormSectionV2>

                    <div class="footer" v-if="!hideFormActions">
                        <!-- TODO: footer -->
                        <AngelosFormFooter
                            :isFormButtonVisible="isFormButtonVisible"
                            :isFormButtonDisabled="isFormButtonDisabled"
                            :handleSubmit="handleSubmit"
                            :handleForm="handleForm"
                            :getFormButtonType="getFormButtonType"
                            :getFormButtonText="getFormButtonText"
                        />
                    </div>
                </div>
            </z-form>
        </div>
    </AngelosEntityConfigProvider>
</template>

<script setup lang="ts">
import { useDynamicComponent, useEntityCore } from '@/composable';
import AngelosEntityConfigProvider from '@/common/AngelosEntityConfigProvider.vue';
import AngelosSimpleFormSectionV2 from '@/common/form-sections-v2/AngelosSimpleFormSectionV2.vue';
import AngelosFormFooter from '@/common/form-sections-v2/AngelosFormFooter.vue';
import { onMounted, provide, ref, toRefs, watch } from 'vue';
import deepmerge from 'deepmerge';
import DOMPurify from 'dompurify';
import {
    ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS,
    FORM_STATE_KEYS,
    PROMISE_STATUS
} from '@/core/constants';
import {
    AUTH_TOKEN_KEY,
    DEFAULT_FORM_CONFIG,
    DEFAULT_MAX_FILE_SIZE,
    OMS_SERVICE_BASE_URL,
    FIELD_TYPES
} from '@/core/constants';
import {
    chainDataConfigs,
    coerceArray,
    executeTransformerFunction,
    filterDataConfig,
    hardDeepCopy,
    isArrayOfPrimitives,
    overwriteMerge,
    rulesToAsyncValidatorSchema,
    transformFormModelBeforeSubmit
} from '@/core/utils';
import { getProperty, hasProperty, setProperty } from 'dot-prop';
import {
    conditionHandler,
    conditionList,
    dynamicFieldConfigHandler,
    executeJsonLogic,
    parseSection
} from '@/core/section-utils';
import { getAuthToken } from '@/core/service';
import {
    ZButton,
    ZResult,
    useDialog,
    ZSkeleton,
    ZForm,
    type FormInst,
    type FormValidationError
} from '@zeta-gds/components';
import { StoreKey, useComponentStore } from '@/composable/useComponentStore';

// =============================================
// PROPS & EMITS
// =============================================
interface Props {
    entityId: string;
    tenantId: string;
    context?: string | object;
    params?: string | object;
    componentType: 'create-form' | 'update-form';
    externalSubmit?: (data: any) => void;
    appTimezone?: string;
    hideFormActions: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    context: '',
    params: ''
});

const emit = defineEmits([
    'prefill-success',
    'prefill-error',
    'after-file-select',
    'upload-error',
    'before-submit',
    'submit-success',
    'submit-error',
    'after-submit',
    'cancel-success',
    'reset-success'
]);

// =============================================
// REFS & REACTIVE STATE
// =============================================
const formInstRef = ref<FormInst | null>(null);
const { entityId, tenantId, componentType, context, params, appTimezone, hideFormActions } =
    toRefs(props);

// Form State Management
const store = useComponentStore(entityId.value, tenantId.value, componentType.value);
provide(StoreKey, store);
const { attachCss, getHarRequestParams, parsedContext, getInputParams, exposeFunctionOnHost } =
    useEntityCore({
        entityId: entityId.value,
        tenantId: tenantId.value,
        componentType: componentType.value,
        context: context.value || {},
        params: params.value || {},
        store
    });
const { generateDynamicComponent } = useDynamicComponent();

// UI State
const isLoading = ref(true);
const showErrorModal = ref(false);
const simpleFormSectionKey = ref(0);
const prefillErrorStatusCode = ref<any>(404);
const componentStore = ref({
    footerKey: 1,
    isSubmitting: false
});
const hasConditionsOnFooterButton = ref(false);
const confirmationProcessed = ref(false);
const isFileUploadSuccessful = ref(false);
const showConfirmationDialog = ref(false);
const showSubmitError = ref(false);

// Data State
const viewConfig = ref<any>({});
const dataConfig = ref({});
const prefilledData = ref(null);
const sectionList = ref<any[]>([]);
const customComponentList = ref<any>({});
let formData: any = null;
let formFields: any = ref({});
const {
    formModel,
    getFormModelFromPath,
    clearValue: clearFormModel,
    updateFormState,
    createInitialFormModel,
    rules,
    fileUploadFieldsMap,
    lastPropertyUpdated,
    getContextualData
} = store;

// Constants
const prefillErrorAttrs = {
    title: 'An error occurred',
    description: 'The information you are looking for is not available right now. please try again.'
};

// Provide
provide('viewConfig', viewConfig);
provide('dataConfig', dataConfig);
provide('prefilledData', prefilledData);
provide('componentStore', componentStore);
provide('sectionList', sectionList);
provide('customComponentList', customComponentList);

const dialog = useDialog();

// =============================================
// WATCHERS
// =============================================
watch(showConfirmationDialog, (newVal) => {
    if (newVal) {
        const confirmationDialogTemplate = getProperty(
            viewConfig.value,
            'form.confirmationDialog.template',
            null
        );
        const confirmationDialogButtons = getProperty(
            viewConfig.value,
            'form.confirmationDialog.buttons',
            []
        );
        const actionItems = confirmationDialogButtons?.map((button: any, index) => {
            return {
                id: index,
                label: button.text || button.label,
                type: button.type,
                callback: () => handleConfirmationDialogActions(button)
            };
        });

        if (confirmationDialogTemplate) {
            dialog.create({
                ...viewConfig.value.form.confirmationDialog,
                actionItems,
                events: {
                    close: () => {
                        showConfirmationDialog.value = false;
                    }
                },
                positiveText: 'Proceed',
                negativeText: 'Cancel',
                maskClosable: false,
                onClose: () => handleConfirmationDialogActions({ action: 'cancel' }),
                onPositiveClick: () => handleConfirmationDialogActions({ action: 'proceed' }),
                onNegativeClick: () => handleConfirmationDialogActions({ action: 'cancel' })
            });
        } else {
            console.error('Confirmation Dialog Template not defined');
        }
    } else {
        dialog.destroyAll();
    }
});

watch(
    formModel,
    () => {
        conditionHandler(sectionList.value, getContextualData());
        dynamicFieldConfigHandler(
            {
                componentMap: formFields.value,
                formModel: formModel.value,
                formFields: viewConfig.value.fields,
                dynamicFieldAttrs: viewConfig.value.dynamicFieldAttrs,
                fileUploaderFields: fileUploadFieldsMap.value
            },
            getContextualData(),
            lastPropertyUpdated.value,
            { arrayItemData: {} },
            { getFormModelFromPath, updateFormState }
        );
    },
    {
        deep: true
    }
);

// =============================================
// METHODS
// =============================================
// Form Submission & Validation
const handleSubmit = () => {
    formInstRef.value?.validate(async (errors) => {
        // validate custom components
        const customCompErrors = await validateCustomComponents();
        if (!errors && !customCompErrors.length) {
            updateFormState(FORM_STATE_KEYS.ERROR_VALIDATION, null);
            handleForm({ action: 'submit' });
        } else {
            updateFormState(
                FORM_STATE_KEYS.ERROR_VALIDATION,
                customCompErrors.concat(errors ?? [])
            );
        }
    });
};

/**
 * Any web component optionally can attach and expose methods to the host.
 * This function invokes the specified method on each custom component and returns the results.
 *
 * @param {string} methodName - The name of the method to invoke on each custom component.
 * @param {unknown} [args] - Optional arguments to pass to the method.
 * @returns {{ customComps: string[], methodPromises: Promise<unknown>[] }} - An object containing:
 *   - customComps: An array of custom component keys where the method was invoked.
 *   - methodPromises: An array of promises representing the results of the invoked methods.
 *
 * Example :
 *
 * Below should be done in the web component
 * 'callback' is the exposed method on web component
 * 'name' is the name of the method to be invoked
 * 'elementRef' is the reference to the web component
 * ((elementRef.value.getRootNode() as any)?.host || {})[name] = callback;
 *
 */
const invokeHostMethods = (methodName: string, args?: unknown) => {
    const methodPromises: Promise<unknown>[] = [];
    const customComps: string[] = [];
    if (Object.keys(customComponentList.value).length) {
        Object.keys(customComponentList.value).forEach((customCompKey: string) => {
            const customComp = customComponentList.value[customCompKey];
            if (customComp?.$el?.[methodName] && typeof customComp.$el[methodName] === 'function') {
                customComps.push(customCompKey);
                methodPromises.push(customComp.$el[methodName](customCompKey, args));
            }
        });
    }
    return { customComps, methodPromises };
};

/**
 * Validates custom components by invoking their validity check method @const {ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS.CHECK_VALIDITY}.
 *
 * This function collects validation errors from custom components and returns them.
 * It uses `invokeHostMethods` to call the `CHECK_VALIDITY` method on each custom component,
 * and processes the results of these method calls.
 *
 * @returns {Promise<FormValidationError[]>} A promise that resolves to an array of validation errors.
 *
 * @async
 * @function validateCustomComponents
 *
 * @example
 * const errors = await validateCustomComponents();
 * if (errors.length) {
 *     console.log('Validation errors:', errors);
 * }
 *
 * @typedef {Object} FormValidationError
 * @property {string} message - The error message.
 * @property {string} field - The field associated with the error.
 *
 * @typedef {PromiseSettledResult<unknown>} ValidationPromiseResult
 * @property {string} status - The status of the promise (fulfilled or rejected).
 * @property {any} value - The value returned if the promise is fulfilled.
 * @property {any} reason - The reason for rejection if the promise is rejected.
 */
const validateCustomComponents = async () => {
    let customCompErrors: FormValidationError[] = [];
    const validationsPromises = invokeHostMethods(
        ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS.CHECK_VALIDITY
    ).methodPromises;
    if (validationsPromises.length) {
        await Promise.allSettled(validationsPromises).then((results) => {
            results.forEach((result) => {
                if (
                    result.status === PROMISE_STATUS.REJECTED &&
                    Array.isArray((result as PromiseRejectedResult).reason) &&
                    (result as PromiseRejectedResult).reason.length
                ) {
                    // if the result is rejected, it means the custom component has validation errors directly thrown as exception
                    const errors = (result as PromiseRejectedResult).reason;
                    customCompErrors = customCompErrors.concat(errors);
                } else if (result.status === PROMISE_STATUS.FULFILLED) {
                    // if the result is fulfilled, it means the custom component has validation errors returned as object which will be decided based on isValid flag
                    const fulfilledResult = (result as PromiseFulfilledResult<unknown>).value as {
                        isValid: boolean;
                        errors?: [];
                    };
                    if (!fulfilledResult.isValid && fulfilledResult.errors?.length) {
                        customCompErrors = customCompErrors.concat(fulfilledResult.errors);
                    }
                }
            });
        });
    }
    return customCompErrors;
};

/**
 * Merges custom components data by invoking host method @const {ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS.GET_FORM_DATA} and updating the form state.
 *
 * This function performs the following steps:
 * 1. Invokes host methods to get form data for custom components.
 * 2. Waits for all data promises to settle.
 * 3. Iterates over the results of the settled promises.
 * 4. If a promise is fulfilled, updates the form state with the retrieved form model data.
 *
 * @async
 * @function mergeCustomComponentsData
 * @returns {Promise<void>} A promise that resolves when all data promises have been processed.
 *
 *  @typedef {Object} PromiseFulfilledResult
 * @property {CustomComponentData} value - The fulfilled result containing custom component data.
 *
 * @typedef {Object} CustomComponentData
 * @property {string} fieldKey - The key identifying the form field.
 * @property {unknown} [formModel] - The form model data for the custom component.
 *
 */
const mergeCustomComponentsData = async () => {
    const { methodPromises: dataPromises, customComps } = invokeHostMethods(
        ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS.GET_FORM_DATA
    );
    if (dataPromises.length) {
        await Promise.allSettled(dataPromises).then((results) => {
            results.forEach((result, index) => {
                if (result.status === PROMISE_STATUS.FULFILLED) {
                    // processing only the fulfilled promises assuming the rejected promises are handled in validateCustomComponents
                    const fulfilledResult = (result as PromiseFulfilledResult<unknown>).value as {
                        fieldKey: string;
                        formModel?: unknown;
                    };
                    updateFormState(FORM_STATE_KEYS.FORM_MODEL, {
                        path: customComps[index],
                        value: fulfilledResult.formModel ?? {}
                    });
                }
            });
        });
    }
};

// Form Actions
async function handleForm(formBtn: any) {
    if (formBtn.action == 'submit') {
        confirmationProcessed.value = false;
        if (typeof props.externalSubmit === 'function') {
            props.externalSubmit(getMergedModel());
        } else if (shouldUploadFiles()) {
            // When there are files to upload, uploadFiles will handle the submission
            // after all files are uploaded
            return await uploadFiles();
        } else {
            return await internalSubmit();
        }
    } else if (formBtn.action == 'cancel') {
        cancelForm();
    } else if (formBtn.action == 'reset') {
        resetForm();
    }
}

function handleConfirmationDialogActions(btn: any) {
    showConfirmationDialog.value = false;
    if (btn.action === 'proceed') {
        confirmationProcessed.value = true;
        internalSubmit();
    }
}

function getMergedModel(): any {
    /*
            This function will be called while submitting the form. In case of form containing arrays
            we can add/delete items from form. Values present in formModel should be considered as source of truth.
            Thais is why we are using overWriteMerge function to merge arrays.
        */
    const formData = deepmerge({ ...parsedContext.value }, formModel.value, {
        // FIXME: context.value or parsedContext.value
        arrayMerge: overwriteMerge
    });
    return formData;
}

function shouldUploadFiles() {
    // Check if there are any files to upload
    if (fileUploadFieldsMap.value.size === 0) {
        return false;
    }

    // Check if any of the file upload fields have files
    let hasFilesToUpload = false;
    fileUploadFieldsMap.value.forEach(({ files }) => {
        if (files && files.length > 0) {
            hasFilesToUpload = true;
        }
    });

    return hasFilesToUpload;
}

async function uploadFiles() {
    componentStore.value.isSubmitting = true;
    let totalFiles = 0;
    fileUploadFieldsMap.value.forEach(({ files }) => (totalFiles += files.length));

    // If there are no files to upload, mark as successful and return
    if (totalFiles === 0) {
        isFileUploadSuccessful.value = true;
        componentStore.value.isSubmitting = false;
        return await internalSubmit();
    }

    let filesUploaded = 0;
    isFileUploadSuccessful.value = false;

    fileUploadFieldsMap.value.forEach(async ({ files, upload }, fieldKey) => {
        for (let i = 0; i < files.length; i++) {
            if (files[i].constructor.name === 'Object' || upload === false) {
                // when initial file is itself being used
                totalFiles -= 1;
            }

            if (filesUploaded === totalFiles) {
                isFileUploadSuccessful.value = true;
                componentStore.value.isSubmitting = false;
                return await internalSubmit();
            }

            const baseUrl: string | undefined = getProperty(window, OMS_SERVICE_BASE_URL);
            if (!baseUrl) {
                console.error('Error while uploading file. OMS_BASE_URL is undefined');
                componentStore.value.isSubmitting = false;
                return;
            }

            const headers = new Headers();
            headers.set(
                'X-Zeta-AuthToken',
                `${getAuthToken() || localStorage.getItem(AUTH_TOKEN_KEY)}`
            );

            const maxFileSize =
                formFields.value[fieldKey].props?.maxFileSize || DEFAULT_MAX_FILE_SIZE;

            const urlType = formFields.value[fieldKey].props?.urlType;

            try {
                const fileData = files[i];
                const fileExtension =
                    fileData.name.split('.').pop() || fileData.type.split('/').pop();
                const urlTypeParam = urlType ? `&type=${urlType}` : '';
                const response = await fetch(
                    `${baseUrl}/locker/1.0/getAssetUploadEndpoint?maxUploadSize=${maxFileSize}&fileExtension=${fileExtension}${urlTypeParam}`,
                    { method: 'GET', headers: headers }
                );
                const data = await response.json();

                const formData = new FormData();
                formData.append('acl', data.acl);
                formData.append('key', data.key);
                formData.append('policy', data.policy);
                formData.append('success_action_status', data.successActionStatus);
                formData.append('x-amz-algorithm', data.xAmzAlgorithm);
                formData.append('x-amz-credential', data.xAmzCredential);
                formData.append('x-amz-date', data.xAmzDate);
                formData.append('x-amz-signature', data.xAmzSignature);
                formData.append('content-type', fileData.type);
                formData.append('file', fileData);

                const endpointResponse = await fetch(data.postAction, {
                    method: 'post',
                    headers,
                    body: formData
                });

                const endpoint = await endpointResponse.text();

                const xmlDoc = new DOMParser().parseFromString(endpoint, 'text/xml');
                const location = xmlDoc.getElementsByTagName('Location')[0].childNodes[0].nodeValue;
                const key = xmlDoc.getElementsByTagName('Key')[0].childNodes[0].nodeValue;
                const eTag = xmlDoc.getElementsByTagName('ETag')[0].childNodes[0].nodeValue;
                const url = location + '?key=' + key + '&etag=' + eTag?.replace(/"/g, '');

                updateFormState('FORM_MODEL', {
                    path: fieldKey,
                    value: { url, name: fileData.name }
                });
                filesUploaded += 1;

                if (filesUploaded === totalFiles) {
                    isFileUploadSuccessful.value = true;
                    componentStore.value.isSubmitting = false;
                    return await internalSubmit();
                }

                return url;
            } catch (e) {
                componentStore.value.isSubmitting = false;
                console.error('Error while uploading file', e);
                emit('upload-error', e);
            }
        }
    });
}

async function internalSubmit() {
    if (
        getProperty(viewConfig.value, 'form.confirmationDialog', null) &&
        !confirmationProcessed.value
    ) {
        showConfirmationDialog.value = checkDialogVisibility();
        if (showConfirmationDialog.value) return;
    }
    await mergeCustomComponentsData();
    const mergedModel = getMergedModel();
    const sanitizedFormModelState = sanitizeFormModel(mergedModel);
    const transformedFormModel = transformFormModelBeforeSubmit(
        { ...getContextualData(), formModel: sanitizedFormModelState },
        dataConfig.value
    );

    emit('before-submit', deepmerge({}, transformedFormModel));

    if (typeof dataConfig.value === 'object') {
        try {
            componentStore.value.isSubmitting = true;
            showSubmitError.value = false;

            const dataConfigsList = coerceArray(dataConfig.value);
            const submitDataRefs =
                viewConfig.value.submitDataRefIds &&
                Array.isArray(viewConfig.value.submitDataRefIds)
                    ? dataConfigsList.filter((dataConfig) =>
                          viewConfig.value.submitDataRefIds.includes(dataConfig.refId)
                      )
                    : filterDataConfig(dataConfigsList, null);

            const { response } = await chainDataConfigs({
                configList: submitDataRefs,
                context: getHarRequestParams(),
                requestData: { formModel: transformedFormModel, prefillData: prefilledData.value }
            });
            emit('submit-success', response);
            return response;
        } catch (error) {
            showSubmitError.value = true;
            prefillErrorStatusCode.value = (error as any)?.error?.status || 404;
            const errorResponse = { error, formModel: deepmerge({}, transformedFormModel) };
            emit('submit-error', errorResponse);
            return errorResponse;
        } finally {
            componentStore.value.isSubmitting = false;
            emit('after-submit', {});
        }
    } else {
        throw new ReferenceError(`httpRequest not defined`);
    }
}

function checkDialogVisibility() {
    const showConfirmationDialogHandler = getProperty(
        viewConfig.value,
        'form.confirmationDialog.isVisible.handler',
        null
    );

    if (showConfirmationDialogHandler) {
        return executeTransformerFunction(showConfirmationDialogHandler, getContextualData());
    }
    return getProperty(viewConfig.value, 'form.confirmationDialog.isVisible', true);
}

function sanitizeFormModel(formModel: any) {
    const sanitizedFormModel = {};
    Object.entries(formModel).forEach(([key, value]) => {
        if (
            value === undefined ||
            value === null ||
            typeof value === 'number' ||
            typeof value === 'boolean' ||
            value instanceof Date
        ) {
            Object.assign(sanitizedFormModel, { [key]: value });
            return;
        }

        let sanitizedValue: any;
        if (isArrayOfPrimitives(value)) {
            sanitizedValue = (value as any[]).map((item) => {
                return typeof item === 'string' ? DOMPurify.sanitize(item) : item;
            });
        } else if (Array.isArray(value)) {
            sanitizedValue = value.map((item) => sanitizeFormModel(item));
        } else if (typeof value === 'object') {
            sanitizedValue = sanitizeFormModel(value);
        } else {
            sanitizedValue = DOMPurify.sanitize(value);
        }

        Object.assign(sanitizedFormModel, { [key]: sanitizedValue });
    });

    return sanitizedFormModel;
}

function cancelForm() {
    emit('cancel-success', formModel.value);
}

function resetForm() {
    invokeHostMethods(ANGELOS_CUSTOM_COMPONENT_DEFAULT_METHODS.RESET);
    clearFormModel();
    onData(formData);
    emit('reset-success', formModel.value);
}

// Data Handling
async function onData(data: any) {
    formData = data;
    simpleFormSectionKey.value = simpleFormSectionKey.value + 1;

    const form: any = data.viewConfig;
    attachCss(data.viewConfig.css);

    viewConfig.value = deepmerge(deepmerge({}, DEFAULT_FORM_CONFIG), form, {
        arrayMerge: overwriteMerge
    });
    dataConfig.value = data.dataConfig;
    prefilledData.value = prefilledData.value || (await getPrefillData());
    if (hasProperty(viewConfig.value, 'confirmationDialog.template')) {
        setProperty(
            viewConfig.value,
            'confirmationDialog._component',
            generateDynamicComponent(
                viewConfig.value.confirmationDialog.template,
                { ...getInputParams() },
                ['formModel']
            )
        );
    }

    createInitialFormModel(viewConfig);
    const componentMap: any = parseFormFields(viewConfig.value);

    if (viewConfig.value.version === 'v2') {
        if (Array.isArray(viewConfig.value.form.sections)) {
            // DEVNOTE when layout contains sections array
            let sectionArrayList = viewConfig.value.form.sections.map((section: any) => {
                return parseSection(section, componentMap, getContextualData());
            });
            sectionList.value = sectionArrayList;
        }
    } else {
        if (Array.isArray(viewConfig.value.sections)) {
            sectionList.value = viewConfig.value.sections.map((section: any) =>
                parseSection(section, componentMap, getContextualData())
            );
        } else {
            const fields: any[] = [];
            for (const field in componentMap) {
                if (Object.prototype.hasOwnProperty.call(componentMap, field)) {
                    fields.push(componentMap[field]);
                }
            }

            sectionList.value = [
                {
                    title: viewConfig.value?.form?.title || viewConfig.value?.layout?.title || '',
                    subtitle:
                        viewConfig.value?.form?.subtitle ||
                        viewConfig.value?.layout?.subtitle ||
                        '',
                    fields: fields
                }
            ];
        }
    }

    hasConditionsOnFooterButton.value = checkConditionOnFooterButton(
        viewConfig.value?.form?.buttons
    );
    dynamicFieldConfigHandler(
        {
            componentMap,
            formModel: formModel.value,
            formFields: viewConfig.value.fields,
            dynamicFieldAttrs: viewConfig.value.dynamicFieldAttrs,
            fileUploaderFields: fileUploadFieldsMap.value
        },
        getContextualData(),
        '',
        { arrayItemData: {} },
        { getFormModelFromPath, updateFormState }
    );
}

async function getPrefillData() {
    try {
        const prefillDataConfigs = filterDataConfig(dataConfig.value, 'PREFILL');

        isLoading.value = true; // to disable submit button
        showErrorModal.value = false;

        const { response, errors } = await chainDataConfigs({
            configList: prefillDataConfigs,
            context: getHarRequestParams(),
            requestData: {},
            executeResponseTransformer: true
        });

        let result: any = {};
        prefillDataConfigs.forEach((data) => {
            // If a prefill api fails, response will not have its data. We continue checking for other apis if failed one needs to be ignored.
            if (data.ignoreFailure && !response[data.refId]) return;
            result = deepmerge(result, response[data.refId]);
            result[data.refId] = response[data.refId];
        });
        emit('prefill-success', result);
        updateFormState('PREFILL_DATA', result);

        if (errors.length) {
            /** in case of ignore failure
             * we got the errors.length greater than 0
             * so in that case we don't need to show the error msg
             * */
            // this.showErrorModal = true;
            emit('prefill-error', { errors, formModel: formModel.value, ignoreFailure: true });
        }
        return result;
    } catch (error) {
        showErrorModal.value = true;
        emit('prefill-error', { error, formModel: formModel.value });
    } finally {
        isLoading.value = false;
    }
}

function parseFormFields(viewConfig: any, nestedKey = '', rootPath: any = []) {
    const componentMap: any = {};

    Object.keys(viewConfig.fields).forEach((key) => {
        const field = hardDeepCopy(viewConfig.fields[key]);
        const fieldType = field.type ? field.type.toLowerCase() : '';
        const componentProps = getProperty(field, 'component.props', {});
        const conditionalKeyValues = conditionList.reduce((accumulator: any, condition) => {
            accumulator[condition.propToSet] = executeJsonLogic(
                {
                    field,
                    propToCheck: condition.rule,
                    defaultValue: condition.defaultValue
                },
                getContextualData()
            );
            return accumulator;
        }, {});

        if (fieldType === FIELD_TYPES.CUSTOM) {
            const compConfig = field.config ? hardDeepCopy(field.config) : field.config;
            componentMap[key] = {
                name: 'CustomComponent',
                compConfig: compConfig,
                props: {
                    type: fieldType,
                    className: field.className,
                    value: getFormModelFromPath(key),
                    rootPath: rootPath,
                    formFieldKey: key,
                    ...conditionalKeyValues,
                    formModel: formModel.value
                },
                type: fieldType
            };
        } else if (fieldType === FIELD_TYPES.ARRAY) {
            if (!field.fields) {
                throw new Error(
                    'Invalid view config. Field of type array must have their own field schema'
                );
            }
            const rootPathCopy = hardDeepCopy(rootPath);
            const viewConfigFields = hardDeepCopy(field);
            delete viewConfigFields.fields;
            rootPathCopy.push(key);
            const arrayNestedKey = nestedKey === '' ? `${key}.0.` : `${nestedKey}${key}.0.`;
            const arrayComponentMap = parseFormFields(field, arrayNestedKey, rootPathCopy);
            componentMap[key] = {
                name: 'AngelosArray',
                fields: arrayComponentMap,
                viewConfigFields: field.fields,
                props: {
                    ...viewConfigFields,
                    ...conditionalKeyValues,
                    type: fieldType,
                    rootPath: rootPathCopy,
                    formModel: formModel.value
                },
                type: fieldType
            };
        } else {
            const formFieldKey = key;
            let name = getProperty(field, 'component.name', field.component || 'AngelosInput');

            if (name == 'AngelosComboBox') {
                field.multiSelect = true;
                name = 'AngelosSelect';
                console.warn('AngelosComboBox is deprecated. Please use AngelosSelect instead');
            }
            const rules = getProperty(field, 'rules', undefined);
            if (rules && !nestedKey) {
                updateFormState('RULES', {
                    path: formFieldKey,
                    value: rulesToAsyncValidatorSchema(rules, field)
                });
            }

            if (name == 'AngelosTemplate') {
                updateFormState('FORM_MODEL', {
                    path: formFieldKey,
                    value: generateDynamicComponent(
                        field.template?.handler ||
                            field.value?.handler ||
                            field.template ||
                            field.value ||
                            '',
                        getContextualData(),
                        []
                    )
                });
            }

            const enumProps = getProperty(field, 'component.props.enums', field.enums);

            if (name === 'AngelosSelect') {
                let enumValues = enumProps;
                if (typeof enumProps === 'string') {
                    const dataEnums =
                        getProperty(getInputParams(), enumProps) ??
                        getProperty(prefilledData.value, enumProps);
                    enumValues = dataEnums;
                }

                setProperty(field, 'component.props.enums', enumValues);
                setProperty(field, 'enums', enumValues);
            }

            if (name === 'AngelosFileUpload' && nestedKey === '') {
                fileUploadFieldsMap.value.set(formFieldKey, {
                    files: [],
                    upload: field.upload
                });
            }

            componentMap[key] = {
                name,
                props: {
                    ...field,
                    ...componentProps,
                    value: getFormModelFromPath(formFieldKey),
                    rootPath: rootPath,
                    formFieldKey: key,
                    harRequestParams: getHarRequestParams(),
                    ...conditionalKeyValues,
                    formModel: formModel.value,
                    appTimezone: appTimezone?.value
                }
            };
        }
    });

    formFields.value = componentMap;
    return componentMap;
}

// Utility Functions
function checkConditionOnFooterButton(
    buttons: { type: string; action: string; outlined?: boolean; condition?: any }[] = []
) {
    return buttons.filter((button) => button.condition).length > 0;
}

function getFormButtonText(formBtn: any) {
    if (formBtn.text) return formBtn.text;
    const buttonText =
        formBtn.action === 'submit' ? 'Submit' : formBtn.action === 'cancel' ? 'Cancel' : 'Reset';
    return buttonText;
}

function getFormButtonType(formBtn: any) {
    switch (formBtn.action) {
        case 'submit':
            return {
                color: 'primary',
                variant: 'filled'
            };
        default:
            return {
                color: 'neutral',
                variant: 'outlined'
            };
    }
}

function isFormButtonDisabled(formBtn: any) {
    const isDisabledByLogic = executeJsonLogic(
        {
            field: formBtn,
            propToCheck: 'disabledIf',
            defaultValue: false
        },
        getContextualData()
    );
    return isDisabledByLogic;
}

function isFormButtonVisible(formBtn: any) {
    const isVisibleByLogic = executeJsonLogic(
        {
            field: formBtn,
            propToCheck: 'visibleIf',
            defaultValue: true
        },
        getContextualData()
    );
    return !isLoading.value && isVisibleByLogic;
}

// Host Functions
function attachFunctionToHost() {
    const handleValidation = (
        options: { silent: boolean },
        resolveValue: unknown,
        rejectFunction: (errors: any) => unknown
    ) => {
        return new Promise((resolve, reject) => {
            formInstRef.value?.validate(async (errors) => {
                const customCompErrors = await validateCustomComponents();
                if (errors || customCompErrors.length) {
                    reject(rejectFunction(customCompErrors.concat(errors ?? [])));
                    if (options.silent) {
                        formInstRef.value?.restoreValidation();
                    }
                } else {
                    resolve(resolveValue);
                }
            });
        });
    };

    exposeFunctionOnHost('submit', async (options: { silent: boolean }) => {
        const transformedFormData = transformFormModelBeforeSubmit(
            { ...getContextualData(), formModel: formModel.value },
            dataConfig.value
        );
        try {
            await handleValidation(options, null, (errors) => ({
                errors: errors,
                formModel: transformedFormData
            }));
            const response = await handleForm({ action: 'submit' });
            return { submitResponse: response, formModel: transformedFormData };
        } catch (result) {
            return result;
        }
    });

    exposeFunctionOnHost('checkValidity', (options: { silent: boolean }) => {
        return handleValidation(options, true, () => false)
            .then(() => true)
            .catch(() => false);
    });

    exposeFunctionOnHost('reportValidity', (options: { silent: boolean }) => {
        return handleValidation(options, true, (errors) => errors)
            .then(() => ({
                isValid: true,
                errors: []
            }))
            .catch((errors) => ({
                isValid: false,
                errors: errors
            }));
    });

    exposeFunctionOnHost('reset', () => {
        handleForm({ action: 'reset' });
        formInstRef.value?.restoreValidation();
    });
}

// =============================================
// LIFECYCLE HOOKS
// =============================================
onMounted(() => {
    attachFunctionToHost();
});
</script>
