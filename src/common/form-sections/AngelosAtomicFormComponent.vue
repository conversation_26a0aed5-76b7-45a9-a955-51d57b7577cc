<template>
    <ZFormItem
        :label="item.props.label"
        :path="item.props.formModelPath"
        :description="item.props.description"
        :help-message="item.props.tooltip"
        :show-indicator="handleShowRequiredIndicator()"
        :optional="handleShowOptionalIndicator()"
        :size="item.props.size"
        v-if="item.props && item.props.isVisible"
    >
        <component
            :is="item.name"
            v-bind="item.props"
            :disabled="item.props.disabled || item.props.isDisabled || componentStore.isSubmitting"
        >
        </component>
    </ZFormItem>
</template>

<script setup lang="ts">
import { StoreKey } from '@/composable/useComponentStore';
import { inject, ref, toRefs, watch } from 'vue';

interface Props {
    componentConfig: any;
    nestedSections: any;
}
const props = defineProps<Props>();
const { componentConfig, nestedSections } = toRefs(props);

const componentStore: any = inject('componentStore');

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { getFormModelFromPath } = store;

const item = ref<any>({});

watch(
    componentConfig,
    (newVal) => {
        item.value = newVal;
    },
    { immediate: true }
);

function setComponentFormModelKeyAndValue() {
    const formModelKey = getFormModelPath(componentConfig.value);
    if (item.value.props) {
        item.value.props.formModelPath = formModelKey;
    }
    if (item.value.props) {
        item.value.props.value = getFormModelFromPath(formModelKey, item.value.props.value);
    }
}

function getFormModelPath(item: any) {
    const rootPath = item.props?.rootPath || [];
    if (rootPath.length === 0 || !nestedSections.value) return item.props?.formFieldKey;
    else {
        let key = '';
        for (let i = 0; i < rootPath.length; i++) {
            key =
                key === ''
                    ? rootPath[i] + '.' + nestedSections.value[i]
                    : key + '.' + rootPath[i] + '.' + nestedSections.value[i];
        }
        key = `${key}.${item.props?.formFieldKey}`;
        return key;
    }
}

setComponentFormModelKeyAndValue();

const handleShowRequiredIndicator = () => {
    return item.value.props?.indicatorType === 'mandatory';
};

const handleShowOptionalIndicator = () => {
    return item.value.props?.indicatorType === 'optional';
};
</script>
