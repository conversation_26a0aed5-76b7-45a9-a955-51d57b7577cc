<template>
    <ZRow
        :gutter="12"
        :class="[
            directionClass,
            'field-container',
            `${classPrefixPath}-container`,
            { striped: isStripped }
        ]"
    >
        <ZCol
            v-for="(item, index) in fields"
            :id="item.id || item.fieldKey"
            :key="index"
            :span="getSpan(item)"
            :class="[`${classPrefixPath}-fields-${index}`, item.class || '']"
        >
            <AngelosAtomicFormComponent
                :nestedSections="nestedSections"
                :key="index"
                :component-config="item"
            >
            </AngelosAtomicFormComponent>
        </ZCol>
    </ZRow>
</template>

<script setup lang="ts">
import { computed, inject, toRefs } from 'vue';
import AngelosAtomicFormComponent from '../form-sections/AngelosAtomicFormComponent.vue';
import { ZCol, ZRow } from '@zeta-gds/components';
import type { Span } from '@zeta-gds/components/lib/row-column/src/interface';
import { StoreKey } from '@/composable/useComponentStore';

interface Props {
    section: any;
    nestedSections: any;
    classPrefixPath?: string;
}

const props = withDefaults(defineProps<Props>(), {
    classPrefixPath: ''
});

const { section, nestedSections, classPrefixPath } = toRefs(props);

// Inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormState } = store;

const fields = computed(() => {
    return section.value.fields;
});

const getSpan = (item: any) => {
    return isContainerVisible(item) ? ((item.colspan ? item.colspan * 2 : 24) as Span) : undefined;
};

const directionClass = computed(() => {
    const { direction } = section.value;
    const directionClass = direction === 'horizontal' ? 'flex-horizontal' : 'flex-vertical';
    return directionClass;
});

const isStripped = computed(() => {
    const { attributes } = section.value;
    return attributes?.striped || false;
});

function isContainerVisible(item: any) {
    if (!item?.props?.isVisible && item.props?.formModelPath) {
        updateFormState('FORM_MODEL', { path: item.props.formModelPath, value: undefined });
    }
    return item?.props?.isVisible;
}
</script>
