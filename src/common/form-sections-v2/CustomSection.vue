<template>
    <ZLayout>
        <SectionHeader :title="title" :subtitle="subtitle" :isBordered="isBordered" />
        <ZLayoutContent class="content">
            <CustomComponent
                v-if="!errorText"
                :package="compConfig.package.name"
                :component="compConfig.name"
                :version="compConfig.package.version"
                v-bind="compProps"
                v-on="compEvents"
                ref="customComponentRef"
            >
                <template #loading>
                    <ZSkeleton class="profile-resolver__loader" viewBox="0 0 180 30" :speed="3">
                        <rect x="0" y="0" rx="3" ry="3" width="180" height="30" />
                    </ZSkeleton>
                </template>
                <template #error>
                    <ZCard>
                        <ZResult
                            variant="image"
                            :status="`500`"
                            :title="`Unable to load the custom component: ${compConfig.name}`"
                            :description="`Package: ${compConfig.package.name} with version : ${compConfig.package.version}`"
                        >
                        </ZResult>
                    </ZCard>
                </template>
            </CustomComponent>
            <ZCard v-else>
                <ZResult
                    variant="image"
                    status="500"
                    title="Custom component config is invalid"
                    :description="errorText"
                >
                </ZResult>
            </ZCard>
        </ZLayoutContent>
    </ZLayout>
</template>
<script setup lang="ts">
import { computed, toRefs, ref, onMounted, inject } from 'vue';
import { ZLayout, ZLayoutContent, ZSkeleton, ZCard } from '@zeta-gds/components';
import CustomComponent from '@/components/atomic/custom-comp/CustomComponent.vue';
import SectionHeader from './SectionHeader.vue';
import { getProperty } from 'dot-prop';
import { executeEventFunction } from '@/core/utils';
import { FORM_STATE_KEYS } from '@/core/constants';
import { validateCustomCompField } from '@/core/section-utils';
import { StoreKey } from '@/composable/useComponentStore';

defineOptions({
    inheritAttrs: false
});

interface Props {
    section: any;
    idPrefixPath?: string;
    classPrefixPath?: string;
}
const customComponentList: any = inject('customComponentList');
const customComponentRef = ref<any | null>(null);
const props = withDefaults(defineProps<Props>(), {
    idPrefixPath: '',
    classPrefixPath: ''
});

// Inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { getContextualData, updateFormState } = store;

const { section } = toRefs(props);

const { title, subtitle, componentMap, headerAttributes } = section.value;

const isBordered = computed(() => {
    return headerAttributes?.isBordered || false;
});
const compConfig = computed(() => {
    return componentMap.compConfig;
});
const errorText = computed(() => {
    const validationMsgs = validateCustomCompField({ config: compConfig.value });
    if (validationMsgs?.length) {
        return validationMsgs.join(', ');
    }
    return '';
});
const compProps = computed(() => {
    //parse and evaluate custom field config props
    let customFieldProps = compConfig.value?.props || {};
    return Object.entries(customFieldProps).reduce(
        (evaluatedArgs, [key, value]) => {
            const valueSource = (value as { value: string })?.value;
            evaluatedArgs[key] = valueSource
                ? getProperty(getContextualData(), valueSource, valueSource)
                : value;
            return evaluatedArgs;
        },
        {} as Record<string, unknown>
    );
});
const compEvents = computed(() => {
    const customFieldEvents: { [k: string]: string } = compConfig.value?.events || {};
    return Object.entries(customFieldEvents).reduce(
        (eventBindings, [eventName, eventConfig]) => {
            eventBindings[eventName] = (event) => {
                let eventData = event?.detail?.[0] || {};
                if (eventConfig) {
                    const updatedFormModelKeys = executeEventFunction(eventConfig, {
                        eventData,
                        ...getContextualData()
                    });
                    updatedFormModelKeys &&
                        Object.keys(updatedFormModelKeys).forEach((fieldKey) => {
                            updateFormState(FORM_STATE_KEYS.FORM_MODEL, {
                                path: fieldKey,
                                value: updatedFormModelKeys[fieldKey]
                            });
                        });
                }
            };
            return eventBindings;
        },
        {} as Record<string, (event: CustomEvent) => void>
    );
});
const formFieldKey = computed(() => {
    return section.value.componentMap.props.formFieldKey;
});
onMounted(() => {
    customComponentList.value[formFieldKey.value] = customComponentRef;
});
</script>
<style scoped>
.header {
    padding: 16px;
}
.content {
    margin: 16px;
}
.header-title,
.header-description {
    margin: 0px;
    line-height: unset;
}
</style>
