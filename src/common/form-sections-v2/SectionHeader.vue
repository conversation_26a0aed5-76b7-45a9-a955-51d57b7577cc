<template>
    <ZLayoutHeader :class="`section-header`" v-if="title" :bordered="isBordered">
        <div class="section-header__center">
            <ZH3 class="section-header__title">
                {{ title }}
            </ZH3>
            <ZP class="section-header__description" v-if="subtitle">
                {{ subtitle }}
            </ZP>
        </div>
        <div class="section-header__right">
            <slot name="right-section"></slot>
        </div>
    </ZLayoutHeader>
</template>

<script setup lang="ts">
import { toRefs } from 'vue';
import { ZH3, ZLayoutHeader, ZP } from '@zeta-gds/components';

defineOptions({
    inheritAttrs: false
});

interface Props {
    title: string;
    subtitle: string;
    isBordered: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    isBordered: false
});

const { title, subtitle, isBordered } = toRefs(props);
</script>
<style scoped>
.section-header {
    padding: 16px;
    display: flex;
}
.section-header__title,
.section-header__description {
    margin: 0px;
    line-height: unset;
}
.section-header__center {
    display: flex;
    flex: 1;
    flex-direction: column;
}
.section-header__right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
</style>
