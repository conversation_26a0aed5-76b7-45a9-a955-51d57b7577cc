<template>
    <div id="angelos-v2-footer" :key="componentStore.footerKey" style="margin: 24px; display: flex">
        <z-space>
            <template v-for="formBtn in viewConfig.form.buttons" :key="formBtn.action">
                <z-button
                    :class="formBtn.class || 'mx-1'"
                    v-if="isFormButtonVisible(formBtn)"
                    :loading="formBtn.action === 'submit' ? componentStore.isSubmitting : false"
                    :disabled="isFormButtonDisabled(formBtn) || componentStore.isSubmitting"
                    @click="
                        (e: any) =>
                            formBtn.action === 'submit' ? handleSubmit() : handleForm(formBtn, e)
                    "
                    :expanded="formBtn.expanded"
                    :icon-left="formBtn.iconLeft"
                    :icon-right="formBtn.iconRight"
                    :color="getFormButtonType(formBtn).color"
                    :variant="getFormButtonType(formBtn).variant"
                >
                    {{ getFormButtonText(formBtn) }}
                </z-button>
            </template>
        </z-space>
    </div>
</template>

<script setup lang="ts">
import { inject, toRefs } from 'vue';
import { ZSpace, ZButton } from '@zeta-gds/components';

interface Props {
    isFormButtonVisible: any;
    isFormButtonDisabled: any;
    handleSubmit: any;
    handleForm: any;
    getFormButtonType: any;
    getFormButtonText: any;
}

const props = defineProps<Props>();

const {
    isFormButtonVisible,
    isFormButtonDisabled,
    handleSubmit,
    handleForm,
    getFormButtonType,
    getFormButtonText
} = toRefs(props);

const viewConfig: any = inject('viewConfig');
const componentStore: any = inject('componentStore');
</script>
