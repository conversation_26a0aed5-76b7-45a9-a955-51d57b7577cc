<template>
    <ZRow
        v-if="sections && sections.length"
        :class="[
            directionClass,
            'section-container',
            {
                striped: isStripped
            }
        ]"
        :gutter="12"
        style="flex-wrap: wrap"
    >
        <template v-for="(item, index) in sections" :key="index">
            <ZCol
                v-if="isContainerVisible(item)"
                :id="item.id || `${idPrefixPath}-section-${index}`"
                :class="[`${classPrefixPath}-sections`, item.class || '']"
                :span="getSpan(item)"
            >
                <SectionsContainer
                    :key="index"
                    :section="item"
                    :nestedSections="nestedSections"
                    :id-prefix-path="`${idPrefixPath}-sections-${index}`"
                    :class-prefix-path="`${classPrefixPath}-sections`"
                    @accordion-open-names="handleAccordionOpenNames"
                />
            </ZCol>
        </template>
    </ZRow>

    <div
        v-else-if="fields && fields.length"
        :id="`${idPrefixPath}-sections`"
        :class="[`${classPrefixPath}-field-wrapper`]"
    >
        <!-- Add Utility container -->
        <FieldsContainer
            :class-prefix-path="`${classPrefixPath}-sections`"
            :nestedSections="nestedSections"
            :section="section"
        />
    </div>
</template>
<script setup lang="ts">
import { computed, toRefs } from 'vue';
import { ZCol, ZRow } from '@zeta-gds/components';
import type { Span } from '@zeta-gds/components/lib/row-column/src/interface';
import SectionsContainer from './SectionsContainer.vue';
import FieldsContainer from './FieldsContainer.vue';

defineOptions({
    inheritAttrs: false
});
const emits = defineEmits(['accordion-open-names']);

interface SECTIONS {
    id: string;
    class: string;
    columns: number;
    props: {
        isVisible: boolean;
    };
    accordionAttributes: {
        multiple: boolean;
        bordered: boolean;
        'arrow-placement': 'start' | 'end';
    };
}

interface FIELDS {}

interface Props {
    idPrefixPath: string;
    classPrefixPath: string;
    nestedSections: any[];
    section: {
        sections?: SECTIONS[];
        fields?: FIELDS[];
        direction?: string;
        attributes?: any;
    };
}

const props = defineProps<Props>();

const { section, nestedSections, classPrefixPath, idPrefixPath } = toRefs(props);

const { sections, fields, direction, attributes } = section.value;

function handleAccordionOpenNames(names: string[]) {
    emits('accordion-open-names', names);
}

function isContainerVisible(item: any) {
    return item?.props?.isVisible;
}

// Need to check this
const getSpan = (item: any) => {
    return isContainerVisible(item) ? ((item.columns ? item.columns * 2 : 24) as Span) : undefined;
};

const directionClass = computed(() => {
    const directionClass = direction === 'horizontal' ? 'flex-horizontal' : 'flex-vertical';
    return directionClass;
});

const isStripped = computed(() => {
    return attributes?.striped || false;
});
</script>
