<template>
    <div class="form-sections-container">
        <template v-for="(section, index) in sectionList">
            <div
                v-if="isContainerVisible(section)"
                :id="(section && section.id) || 'angelos-form-section-container'"
                :key="index"
                :class="[section.class || '']"
                style="margin: 24px"
            >
                <SectionsContainer
                    :section="section"
                    id-prefix-path="angelos-form"
                    class-prefix-path="angelos-form"
                />
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { inject, watch } from 'vue';
import SectionsContainer from './SectionsContainer.vue';

const sectionList: any = inject('sectionList');

function isContainerVisible(item: any) {
    return item?.props?.isVisible;
}
</script>
