<template>
    <div v-if="customField">
        <CustomSection
            :section="section"
            :id-prefix-path="`${idPrefixPath}-custom-section`"
            :class-prefix-path="`${classPrefixPath}-custom-section`"
        />
    </div>
    <div v-else-if="arrayField">
        <!-- Add array section container component -->
        <ArrayContainer
            :section="section"
            :headerAttributes="headerAttributes"
            :nestedSections="nestedSections"
            :id-prefix-path="`${idPrefixPath}-array-section`"
            :class-prefix-path="`${classPrefixPath}-array-section`"
            @accordion-open-names="handleAccordionOpenNames"
        />
    </div>

    <div v-else-if="layoutType == 'accordion'" :class="`${classPrefixPath}-container`">
        <!-- Add accordion -->
        <ZAccordion
            display-directive="show"
            :expanded-names="expandedNames"
            :on-item-header-click="onAccordionItemClick"
        >
            <template v-for="(sectionItem, index) in sections" :key="index">
                <ZAccordionItem :title="sectionItem?.accordionAttributes?.title" :name="index">
                    <ZRow
                        :class="[
                            directionClass,
                            'section-container',
                            {
                                striped: isStripped
                            }
                        ]"
                        :gutter="12"
                        style="flex-wrap: wrap"
                    >
                        <ZCol
                            :class="[`${classPrefixPath}-sections`, sectionItem.class || '']"
                            :span="getSpan(sectionItem)"
                        >
                            <SectionsContainer
                                :key="index"
                                :section="sectionItem"
                                :nestedSections="nestedSections"
                                :id-prefix-path="`${idPrefixPath}-sections-${index}`"
                                :class-prefix-path="`${classPrefixPath}-sections`"
                            />
                        </ZCol>
                    </ZRow>
                </ZAccordionItem>
            </template>
        </ZAccordion>
    </div>

    <div v-else :class="`${classPrefixPath}-container`">
        <ZLayout>
            <SectionHeader :title="title" :subtitle="subtitle" :isBordered="isBordered" />
            <!-- <ZLayoutContent :class="{'content': (title || subtitle)}"> -->
            <ZLayoutContent :class="`content`">
                <SectionGrid
                    :section="section"
                    :direction="direction"
                    :nestedSections="nestedSections"
                    :id-prefix-path="idPrefixPath"
                    :class-prefix-path="classPrefixPath"
                />
            </ZLayoutContent>
        </ZLayout>
    </div>
</template>

<script setup lang="ts">
import { computed, inject, toRefs, watch, ref } from 'vue';
import {
    ZAccordion,
    ZAccordionItem,
    ZCol,
    ZLayout,
    ZLayoutContent,
    ZRow
} from '@zeta-gds/components';
import type { Span } from '@zeta-gds/components/lib/row-column/src/interface';
import SectionHeader from './SectionHeader.vue';
import SectionGrid from './SectionGrid.vue';
import ArrayContainer from './ArrayContainer.vue';
import CustomSection from './CustomSection.vue';
import type { ErrorStructure } from '../../types';
import { findErrorAccordions, updateExpandedNames } from '../../core/utils';
import { StoreKey } from '@/composable/useComponentStore';

const emit = defineEmits(['accordion-open-names']);

defineOptions({
    inheritAttrs: false
});

// =============================================
// PROPS & EMITS
// =============================================
interface Props {
    section: any;
    nestedSections?: any;
    idPrefixPath?: string;
    classPrefixPath?: string;
}

const props = withDefaults(defineProps<Props>(), {
    idPrefixPath: '',
    classPrefixPath: '',
    nestedSections: []
});

// =============================================
// REFS & REACTIVE STATE
// =============================================
const expandedNames = ref<number[]>([]);
const { section, nestedSections, idPrefixPath, classPrefixPath } = toRefs(props);

const {
    title,
    subtitle,
    direction,
    attributes,
    arrayField,
    customField,
    layoutType,
    sections,
    headerAttributes
} = section.value;

// Inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { errorValidation } = store;

/*
 * Watch for changes in the section value
 * and add names in the accordion whose indexes are there in error validation
 */
watch(errorValidation, () => {
    if (errorValidation.value) {
        const errorAccordionIndexes = findErrorAccordions(
            sections || [],
            errorValidation.value,
            extractIndex
        );
        updateExpandedNames(expandedNames, errorAccordionIndexes);
    }
});

function extractIndex(section: any, errorValidation: ErrorStructure, index: number): number | null {
    return findFieldInErrorValidation(section, errorValidation) ? index : null;
}

function handleAccordionOpenNames(names: string[]) {
    emit('accordion-open-names', names);
}

function findFieldInErrorValidation(section: any, errorValidation: ErrorStructure) {
    if (section?.fields) {
        return section.fields.some((field: any) => {
            return errorValidation?.some((errorDetailArray) =>
                errorDetailArray.some((errorDetail) => errorDetail.field === field.fieldKey)
            );
        });
    }
    if (section?.sections) {
        return section.sections.some((section: any) => {
            return findFieldInErrorValidation(section, errorValidation);
        });
    }
    return null;
}

const onAccordionItemClick = (data: { name: number; expanded: boolean; event: MouseEvent }) => {
    if (data.expanded) {
        expandedNames.value.push(data.name);
    } else {
        const index = expandedNames.value?.indexOf(data.name);
        if (index > -1) {
            expandedNames.value.splice(index, 1);
        }
    }
};

const isBordered = computed(() => {
    return headerAttributes?.isBordered || false;
});

//--------------------------------------
function isContainerVisible(item: any) {
    return item?.props?.isVisible;
}

// Need to check this
const getSpan = (item: any) => {
    return isContainerVisible(item) ? ((item.columns ? item.columns * 2 : 24) as Span) : undefined;
};

const directionClass = computed(() => {
    const directionClass = direction === 'horizontal' ? 'flex-horizontal' : 'flex-vertical';
    return directionClass;
});
const isStripped = computed(() => {
    return attributes?.striped || false;
});
//--------------------------------------
</script>
<style scoped>
.header {
    padding: 16px;
}
.content {
    margin: 16px;
}
.header-title,
.header-description {
    margin: 0px;
    line-height: unset;
}
</style>
