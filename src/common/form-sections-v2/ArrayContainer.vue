<template>
    <div :class="`${classPrefixPath}-array-container`">
        <ZLayout>
            <SectionHeader :title="title || ' '" :subtitle="subtitle" :isBordered="isBordered">
                <template #right-section>
                    <div
                        class="add-section-from-selection"
                        v-if="addFromSelectAttributes.component === 'AngelosSelect'"
                    >
                        <!-- TODO -> :label="'Select Label'" Check if this string needs to be fixed or variable-->
                        <Select
                            :label="'Select Label'"
                            v-bind="addFromSelectAttributes"
                            :disabled="!addSectionFromSelectionItems.length"
                            :values="addSectionFromSelectionItems"
                            :formModelPath="''"
                            :tooltip="addSectionSelectionTooltipText"
                            :showTooltip="
                                addFromSelectAttributes.showTooltip ||
                                !addSectionFromSelectionItems.length
                            "
                            @input="sectionSelectionHandler"
                        />
                    </div>
                    <ZButton
                        v-else-if="addSection"
                        @click="pushElement()"
                        :class="`${classPrefixPath}-add-button`"
                    >
                        <template #icon>
                            <z-icon>
                                <add-icon />
                            </z-icon>
                        </template>
                        {{ addButtonText }}
                    </ZButton>
                </template>
            </SectionHeader>
            <ZLayoutContent :class="`content`">
                <div v-if="!arrayElements.length" class="empty-state-container">
                    <ZEmpty
                        :title="emptyStateMessage"
                        :show-description="false"
                        :description="emptyStateDescription"
                    />
                </div>
                <template v-else>
                    <ZAccordion
                        :expanded-names="expandedNames"
                        :on-item-header-click="onAccordionItemClick"
                        display-directive="show"
                    >
                        <template v-for="(rowItem, index) in arrayElements" :key="rowItem.uuid">
                            <ZAccordionItem
                                :title="getElementTitle(rowItem, index)"
                                :name="rowItem.uuid"
                                display-directive="show"
                            >
                                <template #header-end>
                                    <ZButton
                                        v-if="deleteSection"
                                        size="small"
                                        circle
                                        @click="
                                            (event) => {
                                                deleteElement(index, event);
                                            }
                                        "
                                    >
                                        <template #icon>
                                            <z-icon>
                                                <delete-icon />
                                            </z-icon>
                                        </template>
                                    </ZButton>
                                </template>
                                <template v-if="nestedContentType === 'sections'">
                                    <template
                                        v-for="(item, itemIndex) in rowItem"
                                        :key="`${rowItem.uuid}-${itemIndex}`"
                                    >
                                        <SectionGrid
                                            :section="item"
                                            :direction="'direction'"
                                            :nestedSections="getNestedSections(index)"
                                            :id-prefix-path="idPrefixPath"
                                            :class-prefix-path="classPrefixPath"
                                            @accordion-open-names="
                                                handleAccordionOpenNames(rowItem.uuid)
                                            "
                                        />
                                    </template>
                                </template>
                                <template v-else-if="nestedContentType === 'fields'">
                                    <SectionGrid
                                        :section="{ fields: rowItem }"
                                        :nestedSections="getNestedSections(index)"
                                        :id-prefix-path="idPrefixPath"
                                        :class-prefix-path="classPrefixPath"
                                    />
                                </template>
                            </ZAccordionItem>
                        </template>
                    </ZAccordion>
                </template>
            </ZLayoutContent>
        </ZLayout>
    </div>
</template>
<script setup lang="ts">
import { computed, inject, onMounted, ref, toRefs, watch, type ComputedRef, type Ref } from 'vue';
import SectionHeader from './SectionHeader.vue';
import SectionGrid from './SectionGrid.vue';
import {
    ZButton,
    ZIcon,
    ZEmpty,
    ZAccordion,
    ZAccordionItem,
    ZLayout,
    ZLayoutContent
} from '@zeta-gds/components';
import Select from '@/components/atomic/form-controls/AngelosSelect/Select.vue';
import { AddFilled as AddIcon } from '@zeta/icons';
import { DeleteFilled as DeleteIcon } from '@zeta/icons';
import { singular } from 'pluralize';
import { parseSection, dynamicFieldConfigHandlerForArray } from '@/core/section-utils';
import {
    executeTransformerFunction,
    findErrorAccordions,
    hardDeepCopy,
    rulesToAsyncValidatorSchema,
    updateExpandedNames
} from '@/core/utils';
import { conditionHandler } from '@/core/section-utils';
import { getProperty, setProperty } from 'dot-prop';
import { convertPath } from '@/core/path-utils';
import { v4 as uuidv4 } from 'uuid';
import { isObject } from '@zeta/utils';
import type { ErrorStructure } from '@/types';
import { StoreKey } from '@/composable/useComponentStore';

defineOptions({
    inheritAttrs: false
});

const emit = defineEmits(['accordion-open-names']);

type AngelosSelectItemType = {
    label: string;
    value?: Record<string, string>;
    disabled?: boolean;
};

interface arrayAttributes {
    emptyStateMessage: string;
    addButtonText: string;
    sectionTitleTransformer: string;
    addSection: boolean;
    deleteSection: boolean;
    addSectionSelection: {
        uniqueKey: string;
        values: AngelosSelectItemType[] | { handler: string };
        tooltip: string;
        noResultsText: string;
    };
}

interface Props {
    section: {
        title: string;
        subtitle: string;
        arrayField?: string;
        headerAttributes: {
            isBordered: boolean;
        };
        attributes: arrayAttributes;
        viewConfigSection: any;
        componentMap: any;
        viewConfigFields: any;
    };
    nestedSections: any;
    idPrefixPath?: string;
    classPrefixPath?: string;
}

const props = withDefaults(defineProps<Props>(), {
    idPrefixPath: '',
    classPrefixPath: '',
    nestedSections: []
});

// Inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const {
    formModel,
    getFormModelFromPath,
    getInputParams,
    getContextualData,
    prefillData,
    updateFormState,
    fileUploadFieldsMap,
    errorValidation
} = store;

const arrayElements = ref<any[]>([]);

const { section, nestedSections, idPrefixPath, classPrefixPath } = toRefs(props);

const {
    title,
    subtitle,
    headerAttributes,
    attributes,
    arrayField,
    componentMap,
    viewConfigSection,
    viewConfigFields
} = section.value;

/*  Following is needed if user wants to add items from a list */
let addSectionFromSelectionItems = ref<any[]>([]);
let addFromSelectAttributes = ref<any>({});
/* ---- */

const addButtonText = computed(() => attributes?.addButtonText || defaultAddButtonText());
const addSection = computed(() => attributes?.addSection || true);
const deleteSection = computed(() => attributes?.deleteSection || true);
const sectionTitleTransformer = computed(() => attributes?.sectionTitleTransformer || '');
const emptyStateMessage = computed(
    () => attributes?.emptyStateMessage || `No ${title || 'data'} available`
);
const emptyStateDescription = `You can start by adding a ${singular(title)}`;
const parsedSection = parseSection(viewConfigSection, componentMap, {
    ...getInputParams(),
    formModel,
    prefillData
});

const expandedNames = ref<string[]>([]);

/*
 * Watch for changes in the error validation value
 * and add names in the accordion whose indexes are there in error validation
 */
watch(errorValidation, () => {
    if (errorValidation.value) {
        const errorAccordionNames = findErrorAccordions(
            arrayElements.value || [],
            errorValidation.value,
            extractName
        );
        // To open all the parent accordions because the error can be at nth nested level
        emit('accordion-open-names', errorAccordionNames);
        updateExpandedNames(expandedNames, errorAccordionNames);
    }
});

function extractName(arrElement: any, errorValidation: ErrorStructure): string | null {
    return findErrorID(arrElement, errorValidation);
}
/**
 * Finds the error UUID for a given array accordion of elements and error validation structure.
 * @param {Array} arrayElements - The array of elements to search through.
 * @param {ErrorStructure} errorValidation - The error validation structure to compare against.
 * @returns {string|null} - The error ID if found, or null if not found.
 */
function findErrorID(arrayElements: any, errorValidation: ErrorStructure): string | null {
    const errorFieldSet = generateErrorValidationSet(errorValidation);
    for (const element of arrayElements) {
        if (errorFieldSet.has(element?.props?.formModelPath)) {
            return arrayElements?.uuid;
        }
    }
    return null;
}

function generateErrorValidationSet(errorValidation: ErrorStructure) {
    const errorFieldSet = new Set<string>();
    errorValidation.forEach((errorDetail) => {
        errorDetail.forEach((error) => {
            const field = error?.field;
            if (field) {
                errorFieldSet.add(field);
            }
        });
    });
    return errorFieldSet;
}

function handleAccordionOpenNames(data: string) {
    expandedNames.value.push(data);
    emit('accordion-open-names', data);
}

let nestedContentType: 'sections' | 'fields' = 'fields';

const onAccordionItemClick = (data: { name: string; expanded: boolean; event: MouseEvent }) => {
    if (data.expanded) {
        expandedNames.value.push(data.name);
    } else {
        const index = expandedNames.value.indexOf(data.name);
        if (index > -1) {
            expandedNames.value.splice(index, 1);
        }
    }
};

/**
 * This calculates the right path to the array element
 * RootPath consists of path without the index eg [field1, field2, field3]
 * Nested section contains index of all the array elements eg [0, 2, 1]
 * These two are combined to form the key field1.0.field2.2.field3.1
 */
const sectionFormModelKey = computed(() => {
    const nestedSectionsValue = nestedSections.value;
    if (parsedSection?.fields) {
        const rootPath =
            parsedSection.fields.length > 0 ? parsedSection.fields[0].props.rootPath : [];
        let key = '';
        for (let i = 0; i < rootPath.length; i++) {
            if (i > 0) {
                key = key + '.' + nestedSectionsValue[i - 1] + '.' + rootPath[i];
            } else {
                key = key + rootPath[i];
            }
        }
        return key;
    }

    if (nestedSectionsValue && nestedSectionsValue.length) {
        return (nestedSectionsValue as string[]).reverse().join('.') + arrayField;
    }
    return arrayField;
});

const isBordered = computed(() => {
    return headerAttributes?.isBordered || false;
});

const defaultAddButtonText = () => {
    if (title) return 'Add ' + singular(title);
    return 'Add Item';
};

const constructFormModelStructure = (sectionConfig: any) => {
    const formModel: any = {};

    if (sectionConfig.sections) {
        let sectionFormModel = {};
        sectionConfig.sections.forEach((section: any) => {
            if (!section.arrayField) {
                const nestedFormModel = constructFormModelStructure(section);
                sectionFormModel = {
                    ...sectionFormModel,
                    ...nestedFormModel
                };
            } else {
                sectionFormModel = {
                    ...sectionFormModel,
                    [section.arrayField]: []
                };
            }
        });
        return sectionFormModel;
    } else if (sectionConfig.fields) {
        sectionConfig.fields.forEach((field: any) => {
            setProperty(formModel, convertPath(field.fieldKey), null);
        });
    }
    return formModel;
};

const getNestedSections = (index: any) => {
    if (!nestedSections.value.length) {
        return [index];
    } else {
        const nestedSectionCopy = hardDeepCopy(nestedSections.value);
        nestedSectionCopy.push(index);
        return nestedSectionCopy;
    }
};

const addElement = (atIndex: number, userSelectedItem: AngelosSelectItemType | null) => {
    const newElement = createNewElement();
    const arrayData = getArrayData();
    let newElementData = userSelectedItem
        ? userSelectedItem
        : constructFormModelStructure(parsedSection);
    arrayElements.value.splice(atIndex + 1, 0, newElement);
    updateRules();
    arrayData.splice(atIndex + 1, 0, newElementData);
};

const pushElement = (userSelectedItem: AngelosSelectItemType | null = null) => {
    addElement(arrayElements.value.length - 1, userSelectedItem);
};

const deleteElement = (atIndex: number, event: MouseEvent) => {
    event.stopPropagation();
    const arrayData = getArrayData();
    arrayElements.value.splice(atIndex, 1);
    updateRules();
    arrayData.splice(atIndex, 1);
};

const getElementTitle = (section: any, itemIndex: number) => {
    if (!sectionTitleTransformer.value) {
        return `${section.title ? singular(section.title) : 'Item'} - ${itemIndex}`;
    }
    const data = getFormModelFromPath(`${sectionFormModelKey.value}.${itemIndex}`);
    return executeTransformerFunction(sectionTitleTransformer.value, {
        formModel: formModel.value,
        index: itemIndex,
        data
    });
};

const createNewElement = () => {
    const newElement = hardDeepCopy(parsedSection[nestedContentType]);
    newElement.uuid = uuidv4();
    return newElement;
};

const getArrayData = () => {
    const key = sectionFormModelKey.value;
    return key ? (getFormModelFromPath(key, []) as any[]) : [];
};

const updateAllElements = (formModelPath = '') => {
    const arrayData = getArrayData();
    arrayElements.value.forEach((element, index) => {
        dynamicFieldConfigHandlerForArray(
            {
                componentMap: element,
                formModel: formModel,
                fileUploaderFields: fileUploadFieldsMap.value,
                formFields: viewConfigFields
            },
            getContextualData(),
            formModelPath,
            { arrayItemData: arrayData[index] },
            { getFormModelFromPath, updateFormState }
        );
        conditionHandler(element, getContextualData(), { arrayItemData: arrayData[index] });
    });
};

const getRulesObject = () => {
    const ruleObj = Object.keys(viewConfigFields).reduce((acc: any, fieldKey: any) => {
        const field = viewConfigFields[fieldKey];
        if (field?.rules) {
            setProperty(acc, fieldKey, rulesToAsyncValidatorSchema(field.rules, field));
        }
        return acc;
    }, {});
    return ruleObj;
};

const updateRules = () => {
    const ruleObject = getRulesObject();
    const ruleArray = [];
    for (let i = 0; i < arrayElements.value.length; i++) {
        ruleArray.push(hardDeepCopy(ruleObject));
    }
    updateFormState('RULES', {
        path: sectionFormModelKey.value,
        value: ruleArray
    });
};

const processAddSectionSelectionValues = () => {
    const { uniqueKey, values } = section.value.attributes.addSectionSelection ?? {};
    let transformedValues = [];
    if ((values as { handler: string })?.handler) {
        const handler = (values as { handler: string })?.handler;
        const transformationFromHandler = executeTransformerFunction(handler, {
            ...getContextualData()
        });
        transformedValues = Array.isArray(transformationFromHandler)
            ? transformationFromHandler
            : [];
    } else {
        if (Array.isArray(values)) {
            transformedValues = values;
        }
    }
    //if uniqueKey is present
    //will show only the results which are not already present/added in section
    if (uniqueKey && transformedValues.length) {
        transformedValues = transformedValues.map((item: AngelosSelectItemType) => {
            if (isObject(item.value)) {
                const formFieldRows: any = getProperty(
                    formModel.value,
                    sectionFormModelKey.value as string,
                    []
                );
                //returns true if the item is not found in existing added rows
                if (
                    item.value?.[uniqueKey] &&
                    formFieldRows.findIndex(
                        (data: any) => data[uniqueKey] === item.value?.[uniqueKey]
                    ) === -1
                ) {
                    item.disabled = false;
                } else {
                    item.disabled = true;
                }
                return item;
            }
        });
    }

    transformedValues.map((option) => {
        if (typeof option.value === 'object') option.value = JSON.stringify(option.value);
        return option;
    });

    addSectionFromSelectionItems.value = transformedValues;
};

//Only if user chooses to add section from select dropdown
watch(() => arrayElements.value.length, processAddSectionSelectionValues);

const addSectionSelectionTooltipText = () => {
    const { tooltip: tooltipGeneralText, noResultsText } =
        section.value.attributes?.addSectionSelection ?? {};
    //show noResultsText only if array of selection elements is empty else show
    return addSectionFromSelectionItems.value.length
        ? tooltipGeneralText
        : noResultsText || 'No items found';
};

const sectionSelectionHandler = (dataOutput: string) => {
    let actualDataOutput = JSON.parse(dataOutput);
    if (Array.isArray(actualDataOutput)) {
        //right now this support is not yet provided but still keeping this for future use cases
        actualDataOutput.forEach((item) => pushElement(item));
    } else {
        if (actualDataOutput) {
            pushElement(actualDataOutput);
        }
    }
};

watch(
    formModel,
    () => {
        updateAllElements();
    },
    {
        deep: true
    }
);

const rerenderArray = () => {
    const arrayValueInFormModel = getArrayData();
    arrayElements.value = [];
    for (let i = 0; i < arrayValueInFormModel.length; i++) {
        arrayElements.value.push(createNewElement());
    }
};

watch(
    () => formModel.value[sectionFormModelKey.value as string],
    () => {
        rerenderArray();
        updateAllElements();
    }
);

onMounted(() => {
    if (parsedSection.sections) nestedContentType = 'sections';
    rerenderArray();
    updateAllElements();
    /* If user wants to choose elements while adding from a list
    this.addFromSelectAttributes will contain attributes for addFromSelect
    respecting angelos-select component */
    let { ...addFromSelectAttributesObj } = section.value.attributes.addSectionSelection ?? {};
    addFromSelectAttributes.value = addFromSelectAttributesObj ?? {};
    processAddSectionSelectionValues();
    /* ========= */
});
</script>
<style scoped>
.add-section-from-selection {
    .z-select {
        width: 150px;
    }
}

.empty-state-container {
    padding: 1rem;
    background-color: var(--z-input-background-color);
}

.content {
    margin-left: 16px;
    margin-right: 16px;
}

.z-layout .z-layout-scroll-container {
    padding-right: 2px;
}
</style>
