<template>
    <AngelosWrapper>
        <div
            :class="[
                `angelos-entity__${entityId}`,
                `angelos-tenant__${tenantId}`,
                `angelos-component__${componentType}`
            ]"
        >
            <transition mode="out-in" name="fade">
                <div key="error" v-if="hasError && !isLoading" class="generic-error-box">
                    <ZResult
                        variant="image"
                        :status="getStatusCode(error?.status)"
                        :title="error?.title"
                        :description="error?.message"
                    >
                    </ZResult>
                </div>
                <div key="loader" v-else-if="isLoading && !hasError">
                    <slot name="loader"></slot>
                </div>
                <div key="component" v-else>
                    <slot></slot>
                </div>
            </transition>
        </div>
    </AngelosWrapper>
</template>

<script setup lang="ts">
import AngelosWrapper from './AngelosWrapper.vue';
import AngelosService from '../core/service';
import { computed, provide, ref, toRefs } from 'vue';
import { ZResult } from '@zeta-gds/components';

const props = defineProps<{
    entityId: string;
    tenantId: string;
    componentType: string;
}>();

const emit = defineEmits(['data']);

const { entityId, tenantId, componentType } = toRefs(props);

const error = ref<{ status?: number; title: string; message: string } | null>(null);
const viewConfig = ref(null);
const dataConfig = ref(null);
const isLoading = ref(true);

const hasError = computed(() => error.value !== null);

function emitDataEvent() {
    if (viewConfig.value && dataConfig) {
        emit('data', {
            viewConfig: viewConfig.value,
            dataConfig: dataConfig.value
        });
    }
}

function getStatusCode(status: number | undefined): any {
    if (!status) return 'error';
    if ([404, 500, 401].includes(status)) return status.toString();
    if (status >= 400 && status < 500) return '4xx';
    if (status >= 500 && status < 600) return '5xx';
    return 'error';
}

AngelosService.getViewConfig(tenantId.value, entityId.value, componentType.value)
    .then((response) => {
        viewConfig.value = response.data.viewConfig;
        dataConfig.value = response.data.dataConfig || {};
        emitDataEvent();
    })
    .catch((e) => {
        error.value = {
            status: 400,
            title: 'Something went wrong',
            message: 'Please try again later'
        };

        if (e.status === 404) {
            error.value = {
                title: `Requested resource doesn't exists`,
                message: 'Try again after creating the resource'
            };
        }

        if (e.status === 401) {
            error.value = {
                title: `Unable to resolve authentication details`,
                message: 'Please make sure you have setup authentication correctly'
            };
        }

        if (e.status === 500) {
            error.value = {
                title: `Sorry, It's not you. Its us`,
                message: `We're experiencing an internal server problem.<br>Please try again later`
            };
        }

        error.value.status = e.status;
    })
    .finally(() => {
        isLoading.value = false;
    });
</script>
