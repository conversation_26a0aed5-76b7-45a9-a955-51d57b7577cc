<template>
    <ThemeConfigProvider :shadow-mode="true">
        <ZDialogProvider>
            <z-message-provider>
                <slot></slot>
            </z-message-provider>
            <z-global-style />
        </ZDialogProvider>
    </ThemeConfigProvider>
</template>

<script lang="ts" setup>
import { ThemeConfigProvider } from '@zeta-gds/themes.aphrodite';
import { ZMessageProvider, ZGlobalStyle, ZDialogProvider } from '@zeta-gds/components';
</script>
