import type { DATA_TABLE_EVENTS } from '@/core/constants';
import type { VNodeChild } from 'vue';

export type TableAction = {
    callback: string;
    label: string;
    value: string;
    disabled: boolean;
    icon: string | null;
    'on-update:model-value': () => void;
};
export type CustomError = {
    error: {
        status: number;
    };
};

interface TableColumnComponent {
    template: string;
}

export interface TableColumn {
    field: string;
    type: string;
    multiple?: boolean;
    label: string;
    align: 'left' | 'right' | 'center';
    titleAlign: 'left' | 'right' | 'center';
    sortable?: boolean;
    width?: string;
    component?: TableColumnComponent;
    render?: string;
    pinned?: 'left' | 'right' | false;
    sortOrder: 'descend' | 'ascend' | false;
    maxWidth?: string;
    minWidth?: string;
    condition?: {
        visibleIf?: object;
    };
    order?: number;
    selectionHandler?: string;
}

interface TableConfig {
    sortIcon: string;
    columns: TableColumn[];
    detailed: boolean;
    ShowDetailIcon: boolean;
    hoverable: boolean;
    striped: boolean;
    checkable: boolean;
    stickyColumnActions: boolean;
    stickyFirstColumn: boolean;
}

export type PaginationConfig = {
    page: number;
    pageSize: number;
    showQuickJumper: boolean;
    showSizePicker: boolean;
    pageSizes: number[];
    itemCount?: number;
    prefix: (paginationItems: PaginationItems) => string;
};

export type PaginationItems = {
    startIndex: number;
    itemCount: number;
    endIndex: number;
};
interface FilterSelector {
    multiple?: boolean;
    searchable?: boolean;
    'show-indicator'?: boolean;
    options?: Array<{ label: string; value: string }>;
    type?: string;
    actions?: string[];
}

export interface FilterConfig {
    field: string;
    fixed?: boolean;
    label: string;
    multiple?: boolean;
    _state: 'init' | 'applied' | 'removed';
    searchable?: boolean;
    options?: Array<{ label: string; value: string }>;
    selectors: {
        'by-value'?: FilterSelector;
        'by-date'?: FilterSelector;
    };
}

export interface TableViewConfig {
    css: string;
    version: string;
    title: string;
    description: string;
    uiDataControl: boolean;
    tableConfig: TableConfig;
    tableActions: TableAction[];
    filterConfig: FilterConfig[];
    paginationConfig: PaginationConfig;
}

export interface DataTableGeneratedConfig {
    title?: string;
    description?: string;
    uiDataControl: boolean;
    filter?: FilterConfig[];
    tableConfig: {
        columns: GdsTableColumns[];
    };
    paginationConfig?: PaginationConfig;
    tableActions: TableAction[];
}
export interface GdsTableColumns {
    key: string;
    title: string;
    type: string;
    multiple?: boolean;
    align: 'left' | 'right' | 'center';
    titleAlign: 'left' | 'right' | 'center';
    sorter: boolean | ((a: any, b: any) => any) | 'default';
    sortOrder: 'ascend' | 'descend' | false;
    width?: number | string;
    minWidth?: number | string;
    maxWidth?: number | string;
    render?: (rowData: object, rowIndex: number) => VNodeChild;
    fixed?: 'left' | 'right' | false;
    selectionHandler?: string;
}

export type InputParams = {
    tenantId: string;
    entityId: string;
    params: {
        [k: string]: any;
    };
    context: {
        [k: string]: any;
    };
};
export type StatusCode =
    | 'error'
    | 'info'
    | 'success'
    | 'warning'
    | 401
    | 404
    | 403
    | 500
    | 418
    | '2xx'
    | '3xx'
    | '4xx'
    | '5xx';

export type TableErrorConfig = {
    statusCode: StatusCode;
    message: string;
    description: string;
};

export interface TableInputFilter {
    selectors: {
        [k: string]: {
            compareFn: string;
            placeholder: string;
            label: string;
        };
    };
}

export interface TableDataConfig {
    httpRequest: {
        url: string;
        method: string;
    };
    requestTransformer: {
        query: string;
        headers: string;
        body: string;
    };
    responseTransformer: string;
}

export type SearchConfig = {
    placeholder: string;
    searchKey: string;
    customSearchFn?: string;
};

// TopbarConfig type might need an update to reflect tableActions having 'key' and 'render'
export interface TopbarAction {
    key: string;
    render: string;
    // Add any other common properties if actions might have them, e.g., visibility conditions
    // condition?: string | object; // Example: for conditional rendering
}
export interface TopbarConfig {
    title?: string;
    description?: string;
    actions?: TopbarAction[];
}
export type EmitEvent = (typeof DATA_TABLE_EVENTS)[number];
export type EmitFunction = (event: EmitEvent, ...args: any[]) => void;
