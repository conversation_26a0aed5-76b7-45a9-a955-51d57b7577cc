// Define the type for the individual error object
export interface ErrorDetail {
    message: string;
    fieldValue: any;
    field: string;
}

export type ErrorDetailArray = ErrorDetail[];

export type ErrorStructure = ErrorDetailArray[];

/**
 * Supported validation rule types
 */
export type ValidationRuleType =
    | 'required'
    | 'email'
    | 'min'
    | 'min_value'
    | 'max'
    | 'max_value'
    | 'regex'
    | 'length';

/**
 * Rule configuration interface
 */
export interface RuleConfig {
    trigger: string[];
    type?: string;
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string | RegExp;
    len?: number;
    message?: string;
    [key: string]: any;
}

// Custom rule value format
export type RuleValue =
    | boolean
    | number
    | string
    | RegExp
    | {
          value?: boolean | number | string | RegExp;
          message?: string;
          [key: string]: any;
      };
