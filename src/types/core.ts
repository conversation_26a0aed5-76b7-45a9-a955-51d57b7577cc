export type CUSTOM_CONFIG = {
    name: string;
    package: {
        name: string;
        version: string;
    };
    props?: {
        [key: string]: string | { value: string };
    };
};
export type SECTION_FIELD_ITEM = {
    component: string;
    fieldKey: string;
    colspan: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    width: string;
    id: string;
    class: string;
    props?: any;
    allFields?: any[];
    sectionType?: 'array-section';
    gridColumns?: number;
    itemTitleTransformer?: string;
    viewConfigSection?: any;
    viewConfigFields?: any;
    componentMap?: any;
    sections?: SECTION_ITEM[];
    fields?: SECTION_FIELD_ITEM[];
};

export type SECTION_ITEM = {
    id: string;
    title: string;
    subtitle: string;
    props: any;
    // layoutType is for V1 and should be removed
    layoutType: 'accordion' | 'grid' | 'simple';
    class: string;
    columns: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    direction: 'vertical' | 'horizontal';
    attributes: any;
    ['header-attributes']: any;
    ['accordion-attributes']: any;
    // gridColumns is for V1 and should be removed
    gridColumns: number;
    arrayField?: string;
    customField?: string;
    itemTitleTransformer?: string;
    viewConfigSection?: any;
    viewConfigFields?: any;
    componentMap?: any;
    sections?: SECTION_ITEM[];
    fields?: SECTION_FIELD_ITEM[];
    config: CUSTOM_CONFIG;
};
export type CONDITION = {
    rule: string;
    propToSet: string;
    defaultValue: any;
};
export type JSON_LOGIC_PARAMS = {
    field: any;
    propToCheck: string;
    defaultValue: any;
};
export type ALL_DATA = {
    formModel?: any;
    prefilledData?: any;
    [key: string]: any;
};
export type FormData = {
    formModel: any;
    componentMap: Record<string, any>;
    fileUploaderFields: Map<any, any>;
    formFields: any;
    dynamicFieldAttrs?: string[];
};
export type SectionConfig = {
    layoutType: 'accordion' | 'grid' | 'simple';
    gridColumns: number;
    id: string;
    title: string;
    subtitle: string;
    condition: {};
    class: string;
    'header-attributes': unknown;
    headerAttributes: unknown;
    'accordion-attributes': unknown;
    accordionAttributes: unknown;
    columns: number;
    direction: 'vertical' | 'horizontal';
    attributes: {};
    customField: string;
    itemTitleTransformer: string;
    arrayField: string;
    sections: unknown[];
    fields: unknown[];
};
export type ComponentMap = { [k: string]: { fields?: unknown[]; viewConfigFields?: unknown[] } };
export type Context = {};
export type ComponentType =
    | 'create-form'
    | 'update-form'
    | 'data-table'
    | 'details-view'
    | 'dynamic-view';
