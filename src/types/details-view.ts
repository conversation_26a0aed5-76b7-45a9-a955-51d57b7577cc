import type { DataTableCreateSummary } from '@zeta-gds/components';

export type FIELD = {
    label: string;
    value: string;
    attributes: any;
    type: string;
    field: any;
    tooltip: string;
    visible: boolean;
    section: SECTION;
    condition: {
        [key: string]: any;
    };
};
export type SECTION = {
    label: string;
    attributes: any;
    fields: FIELD[];
    key: string;
    tooltip: string;
    type: 'header' | 'accordion';
    isJson: boolean;
    data: any;
    labelTransformer: string | undefined;
    accordionAttributes: any;
    condition: {
        [key: string]: any;
    };
    maxNestedLevel: number;
};

export type TABLE_SECTION = {
    isTable: boolean;
    tableConfig: {
        columns: COLUMN[];
    };
    isPaginated: boolean;
    paginationConfig: any;
    data: any;
    condition: {
        [key: string]: any;
    };
    footerTemplate: string;
    hasStickyFooter: boolean;
};

export type CUSTOM_COMP_SECTION = {
    isCustomComponent: boolean;
    key: string;
    type: string;
    className: string;
    config: {
        name: string;
        package: {
            name: string;
            version: string;
        };
        props?: [{ key: string; value: string }];
        events?: { [key: string]: string };
    };
    condition: {
        [key: string]: any;
    };
};

export type KEY_VALUE_VIEW = {
    header: any;
    container: any;
    sections: SECTION[];
    heightOverflowType: string;
    showMoreHeightThreshold: number;
    condition: {
        [key: string]: any;
    };
    tab?: Omit<TAB_ITEM, 'value'>;
    class?: string;
};

export type TABLE_VIEW = {
    header: any;
    sections: TABLE_SECTION;
    container: any;
    condition: {
        [key: string]: any;
    };
    tab?: Omit<TAB_ITEM, 'value'>;
    class?: string;
};

export type GROUPED_SECTION = {
    type: GROUP_TYPE;
    items: (KEY_VALUE_VIEW | TABLE_VIEW)[];
    attributes: GROUPED_VIEW_ATTRIBUTES;
};

export type GROUPED_VIEW = {
    sections: GROUPED_SECTION;
    header: any;
    container: any;
    class?: string;
};
export type CUSTOM_COMP_VIEW = {
    sections: CUSTOM_COMP_SECTION;
    header: any;
    container: any;
    class?: string;
};

export type VIEW = KEY_VALUE_VIEW | TABLE_VIEW | GROUPED_VIEW;

export type DETAILS_VIEW_CONFIG = {
    headerProps: Partial<{
        title: string;
        description: string;
        type: string;
        paddingless: boolean;
        size: string;
    }>;
    containerProps: any;
    viewData: SECTION_CONFIG[] | DATA_TABLE_CONFIG;
    heightOverflowType?: string;
    showMoreHeightThreshold?: number;
    tabs?: DETAILS_VIEW_CONFIG[];
    tabItems?: TAB_ITEM[];
    activeTab?: number;
    commonTabsAttributes?: TABS_ATTRIBUTES;
    class?: string;
};

export type COLUMN = {
    title: string;
    key: string;
    template?: string;
    sortable?: boolean;
};

export type PAGINATION_CONFIG = {
    currentPage: number;
    currentPerPage: number;
    perPageItems: number[];
};

export type DATA_TABLE_CONFIG = {
    isTable: boolean;
    columns: COLUMN[];
    data: any;
    isPaginated: boolean;
    paginationConfig: PAGINATION_CONFIG;
    hasStickyFooter: boolean;
    summary?: DataTableCreateSummary;
};

export type SECTION_CONFIG = {
    key: string | 'noHeader';
    props: any;
    type: 'text' | 'none' | 'link' | 'status' | 'avatar' | 'header' | 'accordion';
    child: any[];
    tooltipContent?: string;
    accordionProps?: any;
};

export type FIELD_CONFIG = {
    key: string;
    value?: any;
    props?: any;
    type?: string;
    keySlotName?: string; // Adding these on own to avoid taking input from users
    valueSlotName?: string;
    field: any;
    tooltipContent: string;
};

export type TAB_ITEM = {
    label: string;
    value: number;
    icon?: string; //Don't have exhaustive list of icons
};

export type TABS_ATTRIBUTES = {
    size: 'is-medium' | 'is-small' | 'is-large';
    position: 'is-centered' | 'is-right' | 'is-left';
    vertical: boolean;
};

export type GROUP_TYPE = 'tab'; // Possibilty of adding more types in future

export type GROUPED_VIEW_ATTRIBUTES = TABS_ATTRIBUTES;
