/**
 * Test entry point - Simple component to test Vue rendering
 */

import { defineCustomElement, ref } from 'vue';

console.log('🔄 Loading simple test component...');

// Create a very simple test component
const SimpleTestComponent = defineCustomElement({
    setup() {
        console.log('🏗️ SimpleTestComponent setup() called');
        const message = ref('🎉 Simple Vue Component is Working!');
        const count = ref(0);
        
        const increment = () => {
            count.value++;
            console.log('Button clicked, count:', count.value);
        };
        
        return { message, count, increment };
    },
    template: `
        <div style="padding: 20px; border: 2px solid blue; background: #e3f2fd; margin: 10px;">
            <h3>{{ message }}</h3>
            <p>Count: {{ count }}</p>
            <button @click="increment" style="padding: 8px 16px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Click me! ({{ count }})
            </button>
            <p style="color: #1976d2; font-weight: bold;">✨ Vue reactive rendering works!</p>
        </div>
    `,
    mounted() {
        console.log('🔌 SimpleTestComponent mounted');
    }
});

console.log('🏗️ SimpleTestComponent created, defining custom element...');

// Define the test custom element
try {
    customElements.define('zwe-test-simple-component', SimpleTestComponent);
    console.log('✅ Simple test component defined successfully');
} catch (error) {
    console.error('❌ Error defining simple test component:', error);
}

export { SimpleTestComponent };
