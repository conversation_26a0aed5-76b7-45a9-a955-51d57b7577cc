/**
 * Forms Entry Point - Lazy loaded when form components are needed
 * Contains: AngelosCreateForm, AngelosUpdateForm
 */

import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosCreateForm from '../components/entity/AngelosCreateForm.ce.vue';
import AngelosUpdateForm from '../components/entity/AngelosUpdateForm.ce.vue';
import loadComponents from '../plugins/components-loader';
import { createOptimizedIconLoader } from '../core/icon-lazy-loader';

console.log('🔄 Loading Forms components...');

const defineWebComponent = (component: any) => {
    console.log('🏗️ Creating web component wrapper for:', component.name || 'Unknown');

    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            console.log('🔧 Web component setup() called with props:', props);

            try {
                const app = createApp({});
                const inst = getCurrentInstance() as ComponentInternalInstance;
                Object.assign(inst.appContext, app._context);
                Object.assign(inst.appContext.provides, app._context.provides);

                console.log('🔧 Vue app context set up');

                // Register components
                console.log('🔧 Loading components...');
                loadComponents(inst.appContext.app);
                console.log('✅ Components loaded');

                // Create optimized icon loader and preload common icons
                console.log('🔧 Setting up icon loader...');
                const iconLoader = createOptimizedIconLoader(inst.appContext.app);
                iconLoader.preloadCommon(); // Load common icons used in forms
                console.log('✅ Icon loader set up');

                const events = Object.fromEntries(
                    (component.emits || []).map((event: string) => {
                        return [
                            `on${event[0].toUpperCase()}${event.slice(1)}`,
                            (payload: unknown) => emit(event, payload)
                        ];
                    })
                );

                console.log('🔧 Events set up:', Object.keys(events));

                return () => {
                    console.log('🎨 Rendering component with props:', props);
                    return h(component, { ...props, ...events });
                };
            } catch (error) {
                console.error('❌ Error in web component setup:', error);
                throw error;
            }
        }
    });
};

// Define custom elements
console.log('🔧 Defining custom elements...');

try {
    if (!customElements.get('zwe-angelos-create-form-v3')) {
        customElements.define('zwe-angelos-create-form-v3', defineWebComponent(AngelosCreateForm));
        console.log('✅ Defined zwe-angelos-create-form-v3');
    } else {
        console.log('⚠️ zwe-angelos-create-form-v3 already defined');
    }

    if (!customElements.get('zwe-angelos-update-form-v3')) {
        customElements.define('zwe-angelos-update-form-v3', defineWebComponent(AngelosUpdateForm));
        console.log('✅ Defined zwe-angelos-update-form-v3');
    } else {
        console.log('⚠️ zwe-angelos-update-form-v3 already defined');
    }

    console.log('✅ Forms components loaded and registered');

    // Force upgrade any existing elements
    setTimeout(() => {
        const createFormElements = document.querySelectorAll('zwe-angelos-create-form-v3');
        const updateFormElements = document.querySelectorAll('zwe-angelos-update-form-v3');

        console.log(`🔧 Found ${createFormElements.length} create form elements to upgrade`);
        console.log(`🔧 Found ${updateFormElements.length} update form elements to upgrade`);

        createFormElements.forEach((el) => customElements.upgrade(el));
        updateFormElements.forEach((el) => customElements.upgrade(el));
    }, 100);
} catch (error) {
    console.error('❌ Error defining custom elements:', error);
}

// Export for potential programmatic access
export { AngelosCreateForm, AngelosUpdateForm };
