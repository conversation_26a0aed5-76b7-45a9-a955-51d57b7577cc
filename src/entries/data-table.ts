/**
 * Data Table Entry Point - Lazy loaded when data table components are needed
 * Contains: AngelosDataTable
 */

import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosDataTable from '../components/entity/AngelosDataTable.ce.vue';
import loadComponents from '../plugins/components-loader';
import { createOptimizedIconLoader } from '../core/icon-lazy-loader';

console.log('🔄 Loading Data Table component...');

const defineWebComponent = (component: any) => {
    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            const app = createApp({});
            const inst = getCurrentInstance() as ComponentInternalInstance;
            Object.assign(inst.appContext, app._context);
            Object.assign(inst.appContext.provides, app._context.provides);

            // Register components
            loadComponents(inst.appContext.app);

            // Create optimized icon loader and preload common icons
            const iconLoader = createOptimizedIconLoader(inst.appContext.app);
            iconLoader.preloadCommon(); // Load common icons used in data tables

            const events = Object.fromEntries(
                (component.emits || []).map((event: string) => {
                    return [
                        `on${event[0].toUpperCase()}${event.slice(1)}`,
                        (payload: unknown) => emit(event, payload)
                    ];
                })
            );

            return () => h(component, { ...props, ...events });
        }
    });
};

// Define custom elements
customElements.define('zwe-angelos-data-table-v3', defineWebComponent(AngelosDataTable));

console.log('✅ Data Table component loaded and registered');

// Export for potential programmatic access
export { AngelosDataTable };
