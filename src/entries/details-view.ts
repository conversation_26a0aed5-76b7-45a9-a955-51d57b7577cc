/**
 * Details View Entry Point - Lazy loaded when details view components are needed
 * Contains: AngelosDetailsView
 */

import {
    createApp,
    defineCustomElement,
    getCurrentInstance,
    type ComponentInternalInstance,
    h
} from 'vue';
import AngelosDetailsView from '../components/entity/details-view/AngelosDetailsView.ce.vue';
import loadComponents from '../plugins/components-loader';
import * as icons from '@zeta/icons';

console.log('🔄 Loading Details View component...');

const defineWebComponent = (component: any) => {
    return defineCustomElement({
        emits: component.emits,
        setup(props: any, { emit }) {
            const app = createApp({});
            const inst = getCurrentInstance() as ComponentInternalInstance;
            Object.assign(inst.appContext, app._context);
            Object.assign(inst.appContext.provides, app._context.provides);

            // Register components
            loadComponents(inst.appContext.app);

            // Register icons - TODO: Optimize to load only needed icons
            for (const [name, icon] of Object.entries(icons)) {
                app.component(name, icon);
            }

            const events = Object.fromEntries(
                (component.emits || []).map((event: string) => {
                    return [
                        `on${event[0].toUpperCase()}${event.slice(1)}`,
                        (payload: unknown) => emit(event, payload)
                    ];
                })
            );

            return () => h(component, { ...props, ...events });
        }
    });
};

// Define custom elements
customElements.define('zwe-angelos-details-view-v3', defineWebComponent(AngelosDetailsView));

console.log('✅ Details View component loaded and registered');

// Export for potential programmatic access
export { AngelosDetailsView };
