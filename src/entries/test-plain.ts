/**
 * Test entry point - Plain JavaScript custom element (no Vue)
 */

// Create a simple native custom element to test if custom elements work at all
class PlainTestComponent extends HTMLElement {
    constructor() {
        super();
        console.log('🏗️ PlainTestComponent constructor called');
    }
    
    connectedCallback() {
        console.log('🔌 PlainTestComponent connected to DOM');
        this.render();
    }
    
    render() {
        console.log('🎨 PlainTestComponent rendering...');
        this.innerHTML = `
            <div style="padding: 20px; border: 2px solid blue; background: #e3f2fd; margin: 10px;">
                <h3>🚀 Plain Custom Element Working!</h3>
                <p>This is a native custom element (no Vue)</p>
                <button onclick="this.parentElement.querySelector('span').textContent = 'Button clicked at ' + new Date().toLocaleTimeString()">
                    Click me!
                </button>
                <br><br>
                <span>Not clicked yet</span>
            </div>
        `;
        console.log('✅ PlainTestComponent rendered successfully');
    }
}

// Define the plain custom element
customElements.define('zwe-plain-test-component', PlainTestComponent);
console.log('✅ Plain test component defined');

export { PlainTestComponent };
