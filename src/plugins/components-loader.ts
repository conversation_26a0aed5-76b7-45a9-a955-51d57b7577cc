import { install } from '@zeta-gds/components';
import type { App } from 'vue';

const modules = import.meta.glob('@/components/**/*.vue', { eager: true });

export default function loadComponents(app: App<any>) {
    for (const path in modules) {
        const componentFileName = path.split('/').reverse()[0];
        const splitArray = componentFileName.split('.');
        const componentName = splitArray[0];
        if (splitArray[splitArray.length - 1]?.trim().toLowerCase() === 'vue') {
            app.component(`Angelos${componentName}`, (modules[path] as any).default);
        }

        install(app);
    }
}
