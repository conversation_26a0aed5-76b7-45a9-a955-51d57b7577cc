<template>
    <div ref="codeEditor" :style="style"></div>
</template>

<script setup lang="ts">
import { computed, inject, onBeforeUnmount, onMounted, ref, toRefs, useAttrs, watch } from 'vue';
import { createMonacoEditor } from '@/core/monaco-lazy-loader';
import { StoreKey } from '@/composable/useComponentStore';

console.log('🎨 CodeEditor component loaded - Monaco will load lazily');

interface Props {
    value: any;
    rules?: any;
}

const props = defineProps<Props>();
const { value } = toRefs(props);
defineEmits(['input']);
const attrs = useAttrs();

type MonacoTheme = 'vs' | 'vs-dark' | 'hc-black';

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const innerValue = ref(value.value || '');

const style = computed(() => {
    const { height = '300px', width = '100%' } = attrs;
    return { height: height as string, width: width as string };
});

watch(
    value,
    (newValue) => {
        innerValue.value = newValue;
    },
    { immediate: true }
);

watch(innerValue, (newValue) => {
    updateFormModel(attrs.formModelPath as string, newValue);
});

const codeEditor = ref();
let editor: any; // Monaco editor instance - type will be available after lazy loading

let isInitialFormatDone = false;

onMounted(async () => {
    const { theme = 'vs', language = 'javascript', isEditable = true, attributes } = attrs;

    console.log(`🎨 Creating Monaco Editor with language: ${language}`);

    try {
        editor = await createMonacoEditor(codeEditor.value as HTMLElement, {
            language: language as string,
            theme: theme as MonacoTheme,
            readOnly: !isEditable,
            formatOnPaste: true,
            formatOnType: true,
            /**
             * DEVNOTE: automaticLayout will resize the editor when the
             * window size changes make codeEditor responsive
             */
            automaticLayout: true,
            minimap: {
                enabled: false
            },
            ...(attributes as unknown as object)
        });

        console.log('✅ Monaco Editor created successfully');
    } catch (error) {
        console.error('❌ Failed to create Monaco Editor:', error);
        return;
    }

    const isJsonObject = language === 'json' && typeof value.value === 'object';

    editor.onDidChangeModelContent(() => {
        /* This is to run initial format on the value provided. We set the editor's value
             using setValue and set a timeout function which runs the format Document action.
             */
        if (!isInitialFormatDone) {
            setTimeout(() => {
                editor?.getAction('editor.action.formatDocument').run();
                isInitialFormatDone = true; // This is set to false so that we don't set timeout again as this is used only for initial format
            }, 100);
        }

        let updatedValue = editor?.getValue();
        if (isJsonObject) {
            try {
                updatedValue = JSON.parse(updatedValue as string);
            } catch {
                console.warn('Invalid JSON');
            }
        }

        updateFormModel(attrs.formModelPath as string, updatedValue);
    });

    /* monaco-editor is adding this container when used in web-component to the document's body.
         This shows texts related to the action being done for 11ty purpose.
         This needs to be removed as it will cause UI issues. */
    Array.from(document.getElementsByClassName('monaco-aria-container')).forEach((item) =>
        item.remove()
    );

    editor.setValue(isJsonObject ? JSON.stringify(value.value) : value.value);
});

onBeforeUnmount(() => {
    editor?.dispose();
});
</script>

<style lang="css">
/* Monaco Editor CSS is loaded dynamically with the editor */
</style>
