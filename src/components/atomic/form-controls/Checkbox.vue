<template>
    <z-checkbox-group
        v-bind="attrs"
        v-model="innerValue"
        :name="attrs.name || ''"
        :placeholder="attrs.placeholder"
    >
        <z-space>
            <z-checkbox
                v-for="(item, index) in attrs.values"
                v-bind="item"
                :value="item.value || ''"
                size="small"
                :key="`${item.value || ''}-${index}`"
            >
                {{ item.label }}
            </z-checkbox>
        </z-space>
    </z-checkbox-group>
</template>

<script setup lang="ts">
import { ref, toRefs, useAttrs, watch, inject } from 'vue';
import { ZCheckbox, ZCheckboxGroup, ZSpace } from '@zeta-gds/components';
import { StoreKey } from '@/composable/useComponentStore';
defineOptions({
    inheritAttrs: false,
    name: 'AngelosCheckbox'
});

interface Props {
    rules?: any;
    value: any;
}

const props = defineProps<Props>();
defineEmits<{
    (e: 'input', value: any, formModelPath: string): void;
}>();

const { value } = toRefs(props);

//inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const attrs: Record<string, any> = useAttrs();

const innerValue = ref(value.value);

watch(innerValue, (newVal: any) => {
    updateFormModel(attrs.formModelPath as string, newVal);
});
</script>
