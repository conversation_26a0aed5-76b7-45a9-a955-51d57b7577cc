<template>
    <z-upload
        v-model:file-list="fileList"
        :default-upload="false"
        :accept="attrs.accept"
        :max="maxFiles"
        @change="handleUploadChange"
        @before-upload="beforeUpload"
    >
        <template v-if="draggable">
            <z-upload-dragger>
                <div style="margin-bottom: 12px">
                    <z-icon size="48" :depth="3">
                        <Upload />
                    </z-icon>
                </div>
                <z-text class="z-upload__info">
                    <z-a> Click to upload </z-a>
                    or drag and drop
                </z-text>
                <z-p v-if="uploadMessage" depth="3" class="z-upload__description">
                    {{ uploadMessage }}
                </z-p>
            </z-upload-dragger>
        </template>
        <template v-else>
            <z-button justify="space-evenly" icon-placement="start">
                {{ uploadMessage || 'Select a file to upload' }}
                <template #start>
                    <z-icon size="20">
                        <Upload />
                    </z-icon>
                </template>
            </z-button>
        </template>
    </z-upload>
</template>

<script setup lang="ts">
import { computed, inject, ref, toRefs, useAttrs, watch } from 'vue';
import {
    ZUpload,
    ZUploadDragger,
    type UploadFileInfo,
    ZButton,
    useMessage,
    ZIcon,
    ZText,
    ZA,
    ZP
} from '@zeta-gds/components';
import { FILE_TYPES, DEFAULT_MAX_FILE_SIZE } from '../../../core/constants';
import { executeTransformerFunction } from '../../../core/utils';
// Icon will be loaded dynamically
const Upload = () => import('@zeta/icons').then((m) => m.Upload);
import { StoreKey } from '@/composable/useComponentStore';

interface Props {
    value: any;
    rules?: string;
    maxFileSize?: number;
    draggable?: boolean;
    uploadMessage?: string;
    // DEVNOTE: Deprecated props, to be removed in future versions
    message?: string; // === uploadMessage
    isRich?: boolean; // === draggable
}

const props = withDefaults(defineProps<Props>(), {
    maxFileSize: DEFAULT_MAX_FILE_SIZE,
    draggable: false,
    uploadMessage: ''
});

const { value, maxFileSize, message, isRich } = toRefs(props);
const attrs: Record<string, any> = useAttrs();

// Show warning for deprecated props when they're explicitly set
//
const draggable = computed(() => {
    // First check if isRich is defined (deprecated prop)
    if (isRich.value !== undefined && isRich.value !== false) {
        // Show warning when the prop is explicitly set
        if ('isRich' in props) {
            console.warn(
                'FileUpload: "isRich" prop is deprecated. Please use "draggable" instead.'
            );
        }
        return isRich.value;
    }
    // Finally use the props.draggable value
    return props.draggable;
});

// Use the deprecated props as aliases for the current props
// Also check for message in attrs which could come from YAML configuration
const uploadMessage = computed(() => {
    // First check if message is defined (deprecated prop)
    if (message.value !== undefined && message.value !== '') {
        // Show warning when the prop is explicitly set
        if ('message' in props) {
            console.warn(
                'FileUpload: "message" prop is deprecated. Please use "uploadMessage" instead.'
            );
        }
        return message.value;
    }

    // Then check if message is in attrs (from YAML config)
    if (attrs.message !== undefined && attrs.message !== '') {
        return String(attrs.message);
    }

    // Then check if uploadMessage is in attrs (from YAML config)
    if (attrs.uploadMessage !== undefined && attrs.uploadMessage !== '') {
        return String(attrs.uploadMessage);
    }

    // Finally use the props.uploadMessage value
    return props.uploadMessage || 'Select a file to upload';
});

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel, formModel, updateFormState } = store;

const messageInfo = useMessage();

const innerValue = ref(value.value || '');

const fileListRef = ref<UploadFileInfo[]>([]);

const fileList = fileListRef;

watch(
    value,
    (newValue) => {
        innerValue.value = newValue;
    },
    { immediate: true }
);

watch(innerValue, (newValue) => {
    updateFormModel(attrs.formModelPath as string, newValue);
});

const allowedFileSize = computed(() => {
    return maxFileSize.value / (1024 * 1024);
});

const maxFiles = computed(() => {
    const hasNoLimit = attrs.multiple && attrs.maxFiles === undefined;
    return attrs.maxFiles || hasNoLimit ? undefined : 1;
});

async function beforeUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
    const uploadedFile = data.file.file as File;
    if (uploadedFile.size > maxFileSize.value) {
        messageInfo.error(`File size exceeds ${allowedFileSize.value} MB`);
        return false;
    }

    const validateResult = await validateFile(uploadedFile);
    return (validateResult as string[]).length === 0;
}

function validateFile(uploadedFile: File) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const result = e.target?.result;
            const { validator = '', harRequestParams } = attrs;
            if (validator) {
                const validationResult = executeTransformerFunction(validator as string, {
                    file: result,
                    ...(harRequestParams as object),
                    formModel: formModel.value
                }) || { errors: [] };
                const errors = validationResult.errors || [];

                if (errors.length) {
                    messageInfo.error(errors[0]);
                    reject(errors);
                } else {
                    resolve([]);
                }
            } else {
                resolve([]);
            }
        };

        if (uploadedFile.type === FILE_TYPES.CSV) {
            reader.readAsText(uploadedFile);
        } else {
            reader.readAsArrayBuffer(uploadedFile);
        }
    });
}

function handleUploadChange(data: { fileList: UploadFileInfo[]; file: UploadFileInfo }) {
    const uploadedFile = data.file.file as File;
    if (uploadedFile.constructor.name !== 'File') return; // When placeholder file is present

    const files = data.fileList.map((file) => file.file as File);

    updateFormModel(attrs.formModelPath as string, files);
    updateFormState('FILE_UPLOAD_FIELDS', { formFieldKey: attrs.formModelPath, files });
}
</script>

<style scoped>
.z-upload__info {
    font-size: 16px;
    color: #434b79;
}

.z-upload__description {
    margin-top: 8px;
    font-size: 14px;
}
</style>
