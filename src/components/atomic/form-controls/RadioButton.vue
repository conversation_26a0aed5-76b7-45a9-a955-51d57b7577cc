<template>
    <z-radio-group
        v-bind="attrs"
        v-model="innerValue"
        :name="attrs.name || ''"
        :placeholder="attrs.placeholder"
    >
        <template v-if="type === 'button'">
            <z-radio-button
                v-for="(item, index) in attrs.values"
                v-bind="item"
                :value="item.value || ''"
                size="small"
                :key="`${item.value || ''}-${index}`"
            >
                {{ item.label }}
            </z-radio-button>
        </template>
        <z-space v-else>
            <z-radio
                v-for="(item, index) in attrs.values"
                v-bind="item"
                :value="item.value || ''"
                size="small"
                :key="`${item.value || ''}-${index}`"
            >
                {{ item.label }}
            </z-radio>
        </z-space>
    </z-radio-group>
</template>

<script setup lang="ts">
import { inject, ref, toRefs, useAttrs, watch } from 'vue';
import { ZRadio, ZRadioGroup, ZSpace, ZRadioButton } from '@zeta-gds/components';
import { StoreKey } from '@/composable/useComponentStore';
interface Props {
    rules?: any;
    value: any;
    formFieldKey: string;
    formModelPath: string;
    type?: string;
}

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'input', value: any, formModelPath: string): void;
}>();

const { rules, value, formFieldKey, formModelPath } = toRefs(props);

const attrs: Record<string, any> = useAttrs();
const innerValue = ref(value.value);

watch(innerValue, (newVal: any) => {
    updateFormModel(formModelPath.value as string, newVal);
});
</script>
