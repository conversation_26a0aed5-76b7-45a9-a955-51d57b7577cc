<template>
    <template v-if="multiple"> </template>
    <ZInput
        v-bind="attrs"
        v-model="innerValue"
        :type="attrs.type"
        :placeholder="attrs.placeholder"
        :name="formFieldKey"
    >
        <!-- Support prefix as a prop -->
        <template v-if="prefix" #prefix>
            <span>{{ prefix }}</span>
        </template>

        <!-- Support suffix as a prop -->
        <template v-if="suffix" #suffix>
            <span>{{ suffix }}</span>
        </template>
    </ZInput>
</template>

<script setup lang="ts">
defineOptions({
    inheritAttrs: false,
    name: 'AngelosInput'
});

import { ref, toRefs, useAttrs, watch, inject } from 'vue';
import { ZInput } from '@zeta-gds/components';
import { StoreKey } from '@/composable/useComponentStore';

interface Props {
    rules?: any;
    value: any;
    multiple?: boolean;
    formFieldKey: string;
    formModelPath: string;
    prefix?: string;
    suffix?: string;
}

const props = defineProps<Props>();
const { value, multiple, formFieldKey, formModelPath, prefix, suffix } = toRefs(props);

const attrs: Record<string, any> = useAttrs();

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const innerValue = ref(value.value);
const innerMultipleValue = ref(value.value ? [...value.value] : []);
// private getFieldCustomMessages = getFieldCustomMessages; // TODO: Check implementation in latest gds

watch(
    value,
    (newValue) => {
        if (multiple.value && Array.isArray(newValue)) {
            innerMultipleValue.value = [...value.value];
        } else {
            innerValue.value = newValue;
        }
    },
    { immediate: true }
);

watch(innerValue, (newValue) => {
    updateFormModel(formModelPath.value as string, newValue);
});

watch(innerMultipleValue, (newValue) => {
    updateFormModel(formModelPath.value as string, newValue);
});
</script>

<style scoped>
.error-message {
    margin-top: 8px;
}
</style>
