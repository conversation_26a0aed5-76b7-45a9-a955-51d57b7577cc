<template>
    <div class="lazy-code-editor">
        <div v-if="loading" class="loading-placeholder">
            Loading code editor...
        </div>
        <CodeEditor
            v-else
            v-bind="$attrs"
            :model-value="modelValue"
            @update:model-value="$emit('update:modelValue', $event)"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
    modelValue?: string;
}

defineProps<Props>();
defineEmits<{
    'update:modelValue': [value: string];
}>();

const loading = ref(true);
const CodeEditor = ref<any>(null);

onMounted(async () => {
    try {
        // Load the CodeEditor component (Monaco will be loaded when needed)
        const module = await import('./CodeEditor.vue');
        CodeEditor.value = module.default;
        
        loading.value = false;
    } catch (error) {
        console.error('Failed to load code editor:', error);
        loading.value = false;
    }
});
</script>

<style scoped>
.lazy-code-editor {
    min-height: 200px;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #666;
}
</style>
