<template>
    <ZInput type="textarea" v-model="innerValue" :placeholder="attrs.placeholder" />
</template>

<script setup lang="ts">
import { inject, ref, toRefs, useAttrs, watch } from 'vue';
import { ZInput } from '@zeta-gds/components';
import { StoreKey } from '@/composable/useComponentStore';

interface Props {
    rules?: any;
    value: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'input', value: any, formModelPath: string): void;
}>();

const { rules, value } = toRefs(props);

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const attrs: Record<string, any> = useAttrs();

const innerValue = ref(value.value);

watch(innerValue, (newVal: any) => {
    updateFormModel(attrs.formModelPath as string, newVal);
});
</script>
