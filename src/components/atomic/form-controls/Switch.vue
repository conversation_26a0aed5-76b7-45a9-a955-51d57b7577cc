<template>
    <ZSpace>
        <ZSwitch
            v-bind="attrs"
            v-model="innerValue"
            :name="attrs.name || ''"
            :placeholder="attrs.placeholder"
        >
        </ZSwitch>
        <ZText class="switch-text">{{ innerText }}</ZText>
    </ZSpace>
</template>

<script setup lang="ts">
import { computed, inject, ref, toRefs, useAttrs, watch } from 'vue';
import { ZSwitch, ZText, ZSpace } from '@zeta-gds/components';
import { StoreKey } from '@/composable/useComponentStore';
defineOptions({
    inheritAttrs: false,
    name: 'AngelosSwitch'
});

interface Props {
    rules?: any;
    value: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'input', value: any, formModelPath: string): void;
}>();

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const { rules, value } = toRefs(props);

const attrs: Record<string, any> = useAttrs();

const innerText = computed(() => {
    const trueLabel = attrs['trueLabel'] || attrs['true-label'];
    const falseLabel = attrs['falseLabel'] || attrs['false-label'];
    if (attrs['true-label'] && attrs['false-label']) {
        // @deprecated
        console.warn(
            'true-label and false-label are deprecated. Use `trueLabel` and `falseLabel` instead.'
        );
    }
    if (trueLabel && falseLabel) {
        return innerValue.value === true ? trueLabel : falseLabel;
    }

    return attrs['text'] || '';
});

const innerValue = ref(value.value);

watch(innerValue, (newVal: any) => {
    updateFormModel(attrs.formModelPath as string, newVal);
});
</script>
