<template>
    <ZSelect
        type="textarea"
        v-model="innerValue"
        :placeholder="placeholder"
        :options="values"
        :disabled="disabled"
        :multiple="multiSelect"
        :filterable="filterable"
        :render-option-label="renderOptionLabel"
        :max-tag-count="maxTagCount"
        :render-option-tag="getRenderOptionFunction()"
        :clearable="clearable"
        :placement="placement"
        @update:model-value="(event) => $emit('input', event, formModelPath || '')"
    />
</template>

<script setup lang="ts">
import { toRefs, watch, h, ref, inject } from 'vue';
import { ZSelect, type SelectOption, ZText, ZTag, ZEllipsis } from '@zeta-gds/components';
import type { SelectBaseOption } from '@zeta-gds/components/lib/select/src/interface';
import type { AngelosSelectProps } from './select.types';
import { StoreKey } from '@/composable/useComponentStore';
defineOptions({
    inheritAttrs: false,
    name: 'AngelosSelect'
});

interface FieldProps {
    label: string;
    tooltip: string;
    rules?: any;
    indicatorType: 'mandatory' | 'optional';
    description: string;
    formFieldKey: string;
    formModelPath: string;
}

interface Props extends AngelosSelectProps, Partial<FieldProps> {}

const props = defineProps<Props>();
defineEmits<{
    (e: 'input', value: any, formModelPath: string): void;
}>();

const {
    value,
    values,
    filterable,
    disabled = ref(false),
    multiSelect = ref(false),
    placeholder,
    maxTagCount,
    clearable,
    placement,
    formModelPath
} = toRefs(props);

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const innerValue = ref(value.value);

const getRenderOptionFunction = () => {
    return multiSelect.value ? renderMultipleSelectTag : renderSingleSelect;
};

const renderOptionLabel = (option: SelectBaseOption) => {
    return h(
        ZEllipsis,
        {
            style: {
                display: 'flex'
            }
        },
        {
            default: () =>
                h(
                    'div',
                    {
                        class: 'select-option-label'
                    },
                    [
                        h('div', null, [option.label as string]),
                        h(
                            ZText,
                            { depth: 3, tag: 'div' },
                            {
                                default: () => option.description
                            }
                        )
                    ]
                ),
            tooltip: () =>
                h('div', null, [
                    h('div', null, [option.label as string]),
                    h('div', null, [option.description as string])
                ])
        }
    );
};

const renderSingleSelect = ({ option }: { option: SelectOption }) => {
    return h('div', [option.label as string]);
};

const renderMultipleSelectTag = ({
    option,
    handleClose
}: {
    option: SelectOption;
    handleClose: () => void;
}) => {
    return h(
        ZTag,
        {
            round: true,
            closable: true,
            onClose: (e: any) => {
                e.stopPropagation();
                handleClose();
            }
        },
        {
            default: () => h('div', [option.label as string])
        }
    );
};

watch(innerValue, (newVal: any) => {
    updateFormModel(formModelPath?.value as string, newVal);
});
</script>
<style>
.select-option-label {
    padding: 4px 0;
}
</style>
