export interface AngelosSelectProps {
    value?: any;
    values: { label: string; value: any; description?: string; class?: string }[];
    disabled?: boolean;
    multiSelect?: boolean;
    filterable?: boolean;
    placeholder?: string;
    size?: 'small' | 'medium' | 'large';
    maxTagCount?: number | 'responsive';
    clearable?: boolean;
    placement?:
        | 'top-start'
        | 'top'
        | 'top-end'
        | 'right-start'
        | 'right'
        | 'right-end'
        | 'bottom-start'
        | 'bottom'
        | 'bottom-end'
        | 'left-start'
        | 'left'
        | 'left-end';
}
