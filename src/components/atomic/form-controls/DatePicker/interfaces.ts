type TIMEZONE = 'UTC' | 'PRESERVE' | 'SYSTEM' | 'DEFAULT';
type DATE_PICKER_TYPE = 'date' | 'datetime' | 'daterange' | 'datetimerange' | 'time';
type POSITION_TYPE =
    | 'top-start'
    | 'top'
    | 'top-end'
    | 'right-start'
    | 'right'
    | 'right-end'
    | 'bottom-start'
    | 'bottom'
    | 'bottom-end'
    | 'left-start'
    | 'left'
    | 'left-end';
type ACTION_TYPE = 'confirm' | 'clear';

export interface FieldProps {
    label?: string;
    tooltip?: string;
    rules?: string;
    indicatorType?: 'mandatory' | 'optional';
    description?: string;
    size?: 'small' | 'medium' | 'large';
}
export interface AngelosDatePickerProps {
    appTimezone?: string;
    value?: string | Date | [string | Date, string | Date];
    disabled?: boolean;
    placeholder?: string;
    inline?: boolean;
    type?: DATE_PICKER_TYPE;
    dateFormat?: string;
    outputDateFormat?: string;
    timezone?: TIMEZONE;
    showOffsetPrefix?: boolean;
    minDate?: string;
    maxDate?: string;
    format?: string;
    actions?: Array<ACTION_TYPE> | null;
    placement?: POSITION_TYPE;
}
