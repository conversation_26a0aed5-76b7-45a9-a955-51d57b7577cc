<template>
    <ZInputGroup>
        <ZInputGroupLabel v-if="getPrepend()" class="prepend-container">{{
            getPrepend()
        }}</ZInputGroupLabel>
        <ZDatePicker
            v-if="inputType !== 'time' && innerValue !== undefined"
            v-model="innerValue"
            :panel="inline"
            :type="inputType"
            :is-date-disabled="dateDisabled"
            :placeholder="placeholder"
            :disabled="disabled"
            :size="size"
            :timezone="undefined"
            :format="inputFormat"
            :time-picker-props="timePickerFormat"
            :actions="actions"
            :placement="placement"
        >
        </ZDatePicker>
        <ZTimePicker
            v-if="inputType === 'time' && innerValue !== undefined"
            v-model="innerValue"
            :size="size"
            :placeholder="placeholder"
            :timezone="undefined"
            :format="inputFormat"
            :disabled="disabled"
            :placement="placement"
        >
        </ZTimePicker>
    </ZInputGroup>
</template>

<script setup lang="ts">
import { inject, onMounted, ref, toRefs, useAttrs, watch } from 'vue';
import { ZInputGroup, ZInputGroupLabel, ZDatePicker, ZTimePicker } from '@zeta-gds/components';
import {
    getTimezoneOffsetInMinutes,
    getType,
    getFormat,
    daysjsToFnsMapper,
    offsetString,
    valueErrorHandler
} from './utils';
import { TIMEZONE, DAY_IN_MILLISECONDS } from './constants';
import { dayjs } from '../../../../core/dayjs-extended';
import type { FieldProps, AngelosDatePickerProps } from './interfaces';
import { StoreKey } from '@/composable/useComponentStore';

interface Props extends FieldProps, AngelosDatePickerProps {}

// inject
const store = inject(StoreKey);
if (!store) throw new Error('Store not provided');
const { updateFormModel } = store;

const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'input', value: any, formModelPath: string): void;
}>();

const {
    rules,
    value,
    appTimezone,
    disabled,
    placeholder,
    tooltip,
    label,
    indicatorType,
    description,
    size,
    inline,
    type,
    dateFormat,
    outputDateFormat,
    timezone,
    minDate,
    maxDate,
    showOffsetPrefix,
    format,
    actions,
    placement
} = toRefs(props);

let offset = 0;
let systemOffset = 0;

const innerValue = ref();
const inputFormat = ref();
const timePickerFormat = ref();
const uncontrolledFormat = ref(format?.value);
const inputType = ref(type?.value);
const attrs: any = useAttrs();

const inputValue = ref(value?.value);

watch(innerValue, (newVal: any) => {
    if (newVal) {
        emit(
            'input',
            Array.isArray(newVal)
                ? [modifyDate(dateTransformer(newVal[0])), modifyDate(dateTransformer(newVal[1]))]
                : modifyDate(dateTransformer(newVal)),
            attrs.formModelPath as string
        );
        updateFormModel(
            attrs.formModelPath as string,
            Array.isArray(newVal)
                ? [modifyDate(dateTransformer(newVal[0])), modifyDate(dateTransformer(newVal[1]))]
                : modifyDate(dateTransformer(newVal))
        );
    }
});

const dateDisabled = (date: number) => {
    const minimumDate = minDate?.value ? dateTransformer(minDate.value) - DAY_IN_MILLISECONDS : 0;
    const maximumDate = maxDate?.value ? dateTransformer(maxDate.value) : Infinity;
    return date < minimumDate || date > maximumDate;
};

const getPrepend = () => {
    if (timezone?.value) return offsetString(offset);
    if (showOffsetPrefix.value) return offsetString(offset);
    return null;
};

// transforms the date string to number which is passed on to gds component.
const dateTransformer = (date: any) => {
    if (date) {
        if (Array.isArray(date)) {
            return [
                date[0] ? dayjs(date[0]).toDate().getTime() : null,
                date[1] ? dayjs(date[1]).toDate().getTime() : null
            ];
        } else if (typeof date !== 'string' && typeof date !== 'object') {
            return date;
        } else {
            return dayjs(date).toDate().getTime();
        }
    }
    return null;
};

// gets the selected timezone
const getSelectedTimeZone = () => {
    return timezone?.value || (appTimezone?.value ? TIMEZONE.APP : TIMEZONE.SYSTEM);
};

// sets the offset on the basis of selected timezone
const setOffset = (dateString: string | Date | [string | Date, string | Date]) => {
    const selectedTimezone = getSelectedTimeZone();
    switch (selectedTimezone) {
        case TIMEZONE.SYSTEM:
            offset = systemOffset;
            break;
        case TIMEZONE.UTC:
            offset = 0;
            break;
        case TIMEZONE.PRESERVE:
            {
                let offsetInMinutes = getTimezoneOffsetInMinutes(
                    Array.isArray(dateString) ? dateString[0].toString() : dateString.toString()
                );
                if (typeof offsetInMinutes === 'number') {
                    offset = offsetInMinutes;
                } else {
                    offset = systemOffset;
                }
            }
            break;
        case TIMEZONE.APP:
            offset = dayjs.tz(dateString as string, appTimezone?.value).utcOffset();
            break;
        default:
            console.error(
                'Pre-configured Timezone(timezone) should be of value SYSTEM, UTC or PRESERVE'
            );
            break;
    }
};

// gets the date object on the basis of selected timezone
const getDateObject = (dateString: string | Date): string | Date => {
    const selectedTimezone = getSelectedTimeZone();
    switch (selectedTimezone) {
        case TIMEZONE.SYSTEM:
            return dayjs(dateString, dateFormat?.value).toDate();
        case TIMEZONE.UTC:
        case TIMEZONE.PRESERVE:
            return dayjs(
                dayjs(dateString, dateFormat?.value)
                    .utcOffset(offset)
                    .utcOffset(systemOffset, true)
                    .format()
            ).toDate();
        case TIMEZONE.APP:
            return dayjs(
                dayjs(dateString).tz(appTimezone?.value).utcOffset(systemOffset, true).format()
            ).toDate();
        default:
            console.error(
                'Pre-configured Timezone(timezone) should be of value SYSTEM, UTC or PRESERVE'
            );
            return '';
    }
};

// modifies the date to be sent as an output to the user on the basis of outputDateFormat prop.
const modifyDate = (date: Date) => {
    const selectedTimezone = getSelectedTimeZone();
    switch (selectedTimezone) {
        case TIMEZONE.SYSTEM:
            return dayjs(date).format(outputDateFormat?.value);
        case TIMEZONE.UTC:
        case TIMEZONE.PRESERVE:
            return dayjs(date).utcOffset(offset, true).format(outputDateFormat?.value);
        case TIMEZONE.APP:
            return dayjs(date).utcOffset(offset, true).format(outputDateFormat?.value);
        default:
            console.error(
                'Pre-configured Timezone(timezone) should be of value SYSTEM, UTC, CUSTOM or PRESERVE'
            );
            return;
    }
};

// sets the correct type and also format as required to be passed to gds date picker component if both these props are not provided.
const setFormat = (): void => {
    if (!inputType.value && !uncontrolledFormat.value) {
        const providedDateFormat = dateFormat?.value || 'DD/MM/YYYY';
        const showDate = /\b(DD|YYYY|YY|MM)\b/g.test(providedDateFormat);
        const showTime = /(h|hh|H|HH)/gi.test(providedDateFormat);
        const showMins = /(m|mm)/g.test(providedDateFormat);
        const showSecs = /(s|ss)/g.test(providedDateFormat);
        inputType.value = getType(showDate, showTime, value?.value);
        uncontrolledFormat.value = getFormat(showDate, showTime, showMins, showSecs).trim();
    }
    inputFormat.value = daysjsToFnsMapper(uncontrolledFormat.value);
    if (inputFormat.value) {
        timePickerFormat.value = {
            format: inputFormat.value.split(' ')[1] ?? undefined
        };
    }
};

onMounted(() => {
    systemOffset = dayjs().utcOffset();
    if (inputValue.value) {
        setOffset(inputValue.value);
        inputValue.value = !Array.isArray(inputValue.value)
            ? getDateObject(inputValue.value).toString()
            : [
                  getDateObject(inputValue.value[0]).toString(),
                  getDateObject(inputValue.value[1]).toString()
              ];
    }
    innerValue.value = dateTransformer(valueErrorHandler(inputValue.value));
    setFormat();
});
</script>

<style scoped>
.prepend-container {
    background: #e6e7f2;
}
</style>
