import { offsetRegex } from './constants';
export function getTimezoneOffset(dateStr: string): string | null {
    const match = offsetRegex.exec(dateStr);
    return match ? match[0] : null;
}

export function getTimezoneOffsetInMinutes(offsetString: string = '') {
    const regexPattern = /([+-])(\d{2}):(\d{2})/; // Match the "+HH:mm" pattern

    const match = offsetString.match(regexPattern);

    if (match !== null) {
        const sign = match[1] === '-' ? -1 : 1;
        const hours = parseInt(match[2]);
        const minutes = parseInt(match[3]);
        const totalMinutes = sign * (hours * 60 + minutes);

        return totalMinutes;
    } else {
        return null;
    }
}

// Source for offsets -> https://en.wikipedia.org/wiki/List_of_UTC_offsets
const offsetStringList = [
    '-12:00',
    '-11:00',
    '-10:00',
    '-09:30',
    '-09:00',
    '-08:00',
    '-07:00',
    '-06:00',
    '-05:00',
    '-04:00',
    '-03:30',
    '-03:00',
    '-02:00',
    '-01:00',
    '+00:00',
    '+01:00',
    '+02:00',
    '+03:00',
    '+03:30',
    '+04:00',
    '+04:30',
    '+05:00',
    '+05:30',
    '+05:45',
    '+06:00',
    '+06:30',
    '+07:00',
    '+08:00',
    '+08:45',
    '+09:00',
    '+09:30',
    '+10:00',
    '+10:30',
    '+11:00',
    '+12:00',
    '+12:45',
    '+13:00',
    '+14:00'
];

export const OFFSET_LIST = offsetStringList.map((offsetString) => ({
    name: `(UTC) ${offsetString}`,
    id: getTimezoneOffsetInMinutes(offsetString)
}));

// simple mapper to convert dayjs to datefns mapper incase someone uses format prop with dayjs format. ( format prop has not been used till now but the handling is done )
export const daysjsToFnsMapper = (format: string | undefined): string | undefined => {
    let updatedFnsFormat = format;
    if (format) {
        updatedFnsFormat = format.replace('DD', 'dd').replace('YYYY', 'yyyy').replace('YY', 'yy');
    }
    return updatedFnsFormat;
};

// sets the correct type on the basis of whether date and time is present or not. Also checks if input is an array, sets the range picker accordingly.
export const getType = (hasDate: boolean, hasTime: boolean, value: any) => {
    let type: any = 'date';
    if (hasDate) {
        type = hasTime ? 'datetime' : 'date';
    } else {
        if (hasTime) {
            type = 'time';
        }
    }
    if (value && Array.isArray(value)) {
        type = type + 'range';
    }
    return type;
};

export const offsetString = (offset: number) => {
    return OFFSET_LIST.find((item) => item.id === offset)?.name || '';
};

export const getFormat = (
    hasDate: boolean,
    hasTime: boolean,
    hasMins: boolean,
    hasSecs: boolean
) => {
    let dateFormat = '';
    let timeFormat = '';
    if (hasDate) {
        dateFormat = 'YYYY-MM-DD';
    }
    if (hasTime) {
        timeFormat = ' HH:mm:ss';
        if (!hasMins) {
            timeFormat = timeFormat.replace(':mm', '').replace(':m', '');
        }
        if (!hasSecs) {
            timeFormat = timeFormat.replace(':ss', '').replace(':s', '');
        }
    }
    return dateFormat + timeFormat;
};

// error handling to prevent GDS component from breaking
export const valueErrorHandler = (value: any) => {
    if (Array.isArray(value) && value[0] == '' && value[1] == '') {
        return null;
    } else {
        return value == '' ? null : value;
    }
};
