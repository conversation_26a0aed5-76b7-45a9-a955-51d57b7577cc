<script lang="ts">
/*eslint-disable */
import { defineComponent, computed, h, ref } from 'vue';
import { PACKAGE_EXPOSED_WITH, PACKAGE_PREFIX } from '@/core/constants';
import { importModule } from '@/core/vite-mf';
import camelcase from 'camelcase';
export default defineComponent({
    name: 'CustomComponent',
    props: {
        component: {
            type: String,
            required: true
        },
        package: {
            type: String,
            required: true
        },
        version: {
            type: String,
            required: true
        }
    },
    setup(props) {
        let element = null;
        let error = ref(true);
        let isLoading = ref(true);
        const exposes = PACKAGE_EXPOSED_WITH;
        const prefixWith = PACKAGE_PREFIX;
        const scopeName = computed(() => {
            return `${prefixWith}-${props.package}`;
        });
        const capitalize = (str: string) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
        };
        type ImportFn = { default: { load: (componentName: string) => Promise<void> } };
        const loadModule = async (importedModule: ImportFn) => {
            let componentName = `${props.component}`;
            componentName = capitalize(camelcase(componentName));
            await importedModule.default.load(componentName);
        };
        const importFn = async () => {
            try {
                isLoading.value = true;
                error.value = false;
                const importedModule = await importModule(
                    `${scopeName.value}/${exposes}`,
                    props.version
                );
                await loadModule(importedModule);
            } catch (err) {
                console.error('CustomComponent error', err);
                error.value = true;
            } finally {
                isLoading.value = false;
            }
        };
        importFn();
        return {
            isLoading,
            error
        };
    },
    render() {
        if (this.isLoading && this.$slots.loading) {
            return h(
                'div',
                this.$slots.loading({
                    isLoading: this.isLoading
                })
            );
        } else if (this.error && this.$slots.error) {
            return h(
                'div',
                this.$slots.error({
                    reason: 'some reason'
                })
            );
        } else {
            return h(
                `zwe-${this.$props.component}`,
                {
                    ...this.$props,
                    ...this.$attrs
                },
                {
                    ...this.$slots
                }
            );
        }
    }
});
</script>
@/core/vite-mf
