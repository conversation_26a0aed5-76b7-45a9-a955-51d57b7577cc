<template>
    <z-skeleton viewBox="0 0 340 84" :speed="2">
        <rect x="0" y="0" rx="3" ry="3" width="245" height="12" />
        <rect
            v-for="(_, idx) in 10"
            :key="idx"
            x="0"
            :y="14 + idx * 8"
            rx="3"
            ry="3"
            width="120"
            height="6"
        />
        <rect
            v-for="(_, idx) in 10"
            :key="idx"
            x="125"
            :y="14 + idx * 8"
            rx="3"
            ry="3"
            width="120"
            height="6"
        />
    </z-skeleton>
</template>
