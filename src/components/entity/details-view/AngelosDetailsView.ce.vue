<template>
    <GdsRootComponent>
        <AngelosEntityConfigProvider
            :entityId="entityId"
            :tenantId="tenantId"
            :componentType="componentTypeRef"
            @data="onConfigData"
        >
            <div class="details-view-wrapper">
                <div v-if="!isLoading">
                    <div
                        v-for="(view, index) in detailsViewData"
                        :key="index"
                        :class="['view', `${view.class || ''}`]"
                    >
                        <div v-if="view.tabs">
                            <z-tabs
                                :variant="view.tabsAttributes.variant"
                                :placement="view.tabsAttributes.placement"
                                :animated="view.tabsAttributes.animated"
                            >
                                <z-tab-pane
                                    v-for="(item, index) in view.tabs"
                                    :key="index"
                                    :name="index"
                                >
                                    <template #tab>
                                        <div class="tab-label">
                                            {{ item.tab.label }}
                                        </div>
                                    </template>
                                    <z-layout class="view-gap">
                                        <z-layout-header
                                            :class="`header && header--${item.headerProps.type}`"
                                            v-if="item.headerProps.title"
                                        >
                                            <z-h3 class="header-title">
                                                {{ item.headerProps.title }}
                                            </z-h3>
                                            <z-p
                                                class="header-description"
                                                v-if="item.headerProps.description"
                                            >
                                                {{ item.headerProps.description }}
                                            </z-p>
                                        </z-layout-header>
                                        <z-layout-content>
                                            <DataView :view="item" />
                                        </z-layout-content>
                                    </z-layout>
                                </z-tab-pane>
                            </z-tabs>
                        </div>
                        <div v-else>
                            <z-layout class="view-gap">
                                <z-layout-header
                                    :class="`header && header--${view.headerProps.type}`"
                                    v-if="view.headerProps.title"
                                >
                                    <z-h3 class="header-title">
                                        {{ view.headerProps.title }}
                                    </z-h3>
                                    <z-p
                                        class="header-description"
                                        v-if="view.headerProps.description"
                                    >
                                        {{ view.headerProps.description }}
                                    </z-p>
                                </z-layout-header>
                                <z-layout-content>
                                    <DataView :view="view" />
                                </z-layout-content>
                            </z-layout>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <DetailsViewSkeleton />
                </div>
            </div>

            <template #loader>
                <DetailsViewSkeleton />
            </template>
        </AngelosEntityConfigProvider>
    </GdsRootComponent>
</template>

<script setup lang="ts">
import { h, ref, toRefs, reactive, provide } from 'vue';
import deepmerge from 'deepmerge';
import { template } from 'lodash';
import { getProperty, setProperty } from 'dot-prop';

import AngelosEntityConfigProvider from '@/common/AngelosEntityConfigProvider.vue';
import {
    chainDataConfigs,
    coerceArray,
    executeJsonLogic,
    executeTransformerFunction,
    executeEventFunction,
    flattenObject
} from '@/core/utils';
import { useEntityCore, useDynamicComponent } from '@/composable';
import type {
    CONDITION,
    DATA_TABLE_CONFIG,
    DETAILS_VIEW_CONFIG,
    FIELD,
    FIELD_CONFIG,
    GROUPED_SECTION,
    KEY_VALUE_VIEW,
    SECTION,
    SECTION_CONFIG,
    TABLE_SECTION,
    TABLE_VIEW,
    VIEW,
    CUSTOM_COMP_SECTION,
    CUSTOM_CONFIG
} from '@/types';
import {
    ANGELOS_COMPONENT_TYPES,
    FIELD_TYPES,
    GROUP_TYPE,
    MAX_NESTED_LEVEL
} from '@/core/constants';
import {
    type DetailsViewData,
    ZTag,
    ZJsonViewer,
    ZLink,
    type DataTableCreateSummary
} from '@zeta-gds/components';
import GdsRootComponent from '@/common/GdsRootComponent.vue';
import DataView from './DataView.vue';
import type { RowData } from '@zeta-gds/components/lib/data-table/src/interface';
import DetailsViewSkeleton from './DetailsViewSkeleton.vue';
import { validateCustomCompField } from '@/core/section-utils';
import { StoreKey, useComponentStore } from '@/composable/useComponentStore';
const props = defineProps<{
    entityId: string;
    tenantId: string;
    context?: string | object;
    params?: string | object;
}>();

const { entityId, tenantId, context, params } = toRefs(props);

const configData = ref(null);
const viewConfig: any = ref({});
const isLoading = ref(true);
const entityData: any = ref({});
const mergedEntityData: any = ref({});
const detailsViewData = ref<any[]>([]);
const prefillData = ref<any>({});

const emit = defineEmits(['data-error', 'entity-data', 'action-click']);

const componentTypeRef = ref(ANGELOS_COMPONENT_TYPES.DETAILS_VIEW);
const store = useComponentStore(entityId.value, tenantId.value, componentTypeRef.value);
provide(StoreKey, store);

const { parsedContext, parsedParams, attachCss, getInputParams, getHarRequestParams } =
    useEntityCore({
        context: context?.value || {},
        params: params?.value || {},
        tenantId: tenantId?.value,
        entityId: entityId?.value,
        componentType: componentTypeRef.value,
        store
    });
const { generateDynamicComponent } = useDynamicComponent();
/**
 *  The defaultHeaderProps are added so that the header complies
 *  with the ViewContent header as shown in the GDS 2.0 doc.
 *  These can be overidden as any other prop through config.
 */
const defaultHeaderProps = {
    type: 'colorless',
    paddingless: true,
    size: 'small'
};

async function getResponseData(dataConfigs: any[]) {
    try {
        isLoading.value = true;
        const { response } = await chainDataConfigs({
            configList: dataConfigs,
            context: getHarRequestParams(),
            requestData: {},
            executeResponseTransformer: true
        });
        return response;
    } catch (error) {
        emit('data-error', { error });
        return {};
    } finally {
        isLoading.value = false;
    }
}

function getMergedApisData(dataConfigs: any[], response: any) {
    const mergedResponse = {};
    dataConfigs.forEach((item) => {
        Object.assign(mergedResponse, response[item.refId]);
    });
    return mergedResponse;
}

const onConfigData = async (data: any) => {
    configData.value = data;

    viewConfig.value = data.viewConfig;

    attachCss(data.viewConfig.css || '.custom_class { color: red; }');

    if (viewConfig.value.sections?.length && viewConfig.value.views?.length) {
        isLoading.value = false;
        console.error(
            'View config cannot have both sections and views. Kindly use either sections or views.'
        );
        throw new Error('View config contains both sections and views');
    }

    const dataConfigs = Array.isArray(data.dataConfig) ? data.dataConfig : [data.dataConfig];
    prefillData.value = await getResponseData(dataConfigs);

    const contextData = parsedContext.value;
    entityData.value = deepmerge(
        prefillData.value,
        { context: contextData },
        {
            arrayMerge: (_, s) => s
        }
    );

    // Merged apis data which will be used to map data based on keys provided to sections and fields
    mergedEntityData.value = deepmerge(
        getMergedApisData(dataConfigs, prefillData.value),
        contextData,
        {
            arrayMerge: (_, s) => s
        }
    );
    detailsViewData.value = transformViewConfig(viewConfig.value);
    emit('entity-data', deepmerge({}, entityData));
};

function getValueFromData(value: any, data = entityData.value) {
    if (value === undefined) return '-';
    return getProperty(data, value, value);
}

function getTabItems(tabs: (KEY_VALUE_VIEW | TABLE_VIEW)[]) {
    return tabs.map((tabItem: KEY_VALUE_VIEW | TABLE_VIEW, index: number) => {
        return {
            label: getValueFromData(tabItem.tab?.label || `tab_${index}`)
        };
    });
}

function evaluate(argument: Record<string, any>) {
    if (argument === undefined || argument === null) return {};
    const evaluatedArgs = {};
    Object.entries(argument).forEach(([key, value]) => {
        const valueSource = argument[key].value;
        if (valueSource) {
            Object.assign(evaluatedArgs, { [key]: getValueFromData(valueSource) });
            return;
        }
        Object.assign(evaluatedArgs, { [key]: value });
    });
    return evaluatedArgs;
}

const conditionList: CONDITION[] = [
    { rule: 'visibleIf', propToSet: 'isVisible', defaultValue: true }
];

function getConditions(field: SECTION | FIELD | VIEW | TABLE_SECTION | CUSTOM_COMP_SECTION) {
    let conditions: any = {};
    conditionList.forEach((condition) => {
        conditions[condition.propToSet] = executeJsonLogic(
            field,
            condition.rule,
            getInputParams(),
            condition.defaultValue,
            { prefillData: prefillData.value }
        );
    });
    return conditions;
}

function transformViewConfig(viewConfig: any) {
    let views: VIEW[] = [];
    if (viewConfig.sections) {
        const {
            sections,
            header,
            container,
            heightOverflowType,
            showMoreHeightThreshold,
            condition
        } = viewConfig;
        views[0] = {
            sections,
            header,
            container,
            heightOverflowType,
            showMoreHeightThreshold,
            condition
        };
    } else {
        views = viewConfig.value?.views || viewConfig.views;
    }

    let transformedConfig: any[] = [];

    views.forEach((view, index) => {
        const conditions = getConditions(view);
        if (!conditions.isVisible) return;

        const { sections, header, container } = view;
        const detailsViewConfig: DETAILS_VIEW_CONFIG = {
            headerProps: {
                ...defaultHeaderProps,
                ...evaluate(header)
            },
            containerProps: evaluate(container),
            viewData: [],
            class: view.class
        };

        let transformedViewConfig: DATA_TABLE_CONFIG | SECTION_CONFIG[] | undefined;

        if ((sections as GROUPED_SECTION).type == GROUP_TYPE.TAB) {
            // Tab logic
            const { items, attributes } = sections as GROUPED_SECTION;
            const tabbedViewConfig = transformViewConfig({ views: items });
            const tabItems = getTabItems(items);
            const defaultTabsAttributes = { variant: 'line', placement: 'top', animated: false };
            transformedConfig.push({
                tabs: tabbedViewConfig.map((view, index) => {
                    return {
                        ...view,
                        tab: {
                            label: tabItems[index].label
                        }
                    };
                }),
                tabsAttributes: { ...defaultTabsAttributes, ...attributes },
                ...detailsViewConfig
            });
        } else if ((sections as TABLE_SECTION).tableConfig) {
            transformedViewConfig = parseTableConfig(sections as TABLE_SECTION);
            if (transformedViewConfig) {
                detailsViewConfig.viewData = transformedViewConfig;
                transformedConfig.push(detailsViewConfig);
            }
        } else {
            const { transformedViewConfig, overrides, options } = parseViewContentConfig(
                sections as (SECTION | CUSTOM_COMP_SECTION)[]
            );
            if (transformedViewConfig.length) {
                // const { heightOverflowType, showMoreHeightThreshold } = view as KEY_VALUE_VIEW;
                Object.assign(detailsViewConfig, {
                    viewData: transformedViewConfig,
                    overrides,
                    options
                });
                transformedConfig.push(detailsViewConfig);
            }
        }
    });

    return transformedConfig;
}

function parseViewContentConfig(sections: (SECTION | CUSTOM_COMP_SECTION)[]) {
    let transformedViewConfig: any[] = [];

    const overrides = {};

    const options = {
        accordionLevelLimit: 5,
        jsonViewerLevelOffset: 5,
        arrayDirection: 'horizontal',
        accordionProps: { bordered: false }
    };

    sections.forEach((section) => {
        const conditions = getConditions(section);
        if (!conditions.isVisible) return;

        if ((section as CUSTOM_COMP_SECTION).type === FIELD_TYPES.CUSTOM) {
            const { config, className } = section as CUSTOM_COMP_SECTION;
            let componentProps = {},
                eventBindings = {};
            const errorText = validateCustomCompField({ config: config as CUSTOM_CONFIG }).join(
                ','
            );
            if (config) {
                const { props, events } = config;
                if (props) {
                    const flattenProps = flattenObject(props);
                    const propsWithHandler = Object.keys(flattenProps).filter((attributePath) =>
                        attributePath.endsWith('.handler')
                    );
                    propsWithHandler.forEach((handler) => {
                        const handlerAttributePath = handler.slice(0, -'.handler'.length);
                        const handlerValue = flattenProps[handler];
                        const calculatedValue = executeTransformerFunction(handlerValue, {
                            ...entityData.value
                        });
                        setProperty(props, handlerAttributePath, calculatedValue);
                    });
                    componentProps = props ? evaluate(props) : {};
                }
                eventBindings = events
                    ? Object.entries(events).reduce(
                          (acc, [eventName, eventConfig]) => {
                              acc[eventName] = (event) => {
                                  let eventData = event?.detail?.[0] || {};
                                  if (eventConfig) {
                                      executeEventFunction(eventConfig as string, {
                                          eventData
                                      });
                                  }
                              };
                              return acc;
                          },
                          {} as Record<string, (event: CustomEvent) => void>
                      )
                    : {};
            }
            const componentConfig = {
                isCustomComponent: true,
                props: componentProps,
                eventsMap: eventBindings,
                className,
                errorText,
                config: config || {}
            };
            transformedViewConfig.push(componentConfig);
        } else {
            const {
                label,
                attributes = {},
                fields,
                key: sectionKey,
                tooltip,
                type,
                isJson = false,
                data = {},
                labelTransformer,
                maxNestedLevel = MAX_NESTED_LEVEL
            } = section as SECTION;

            let sectionConfig = {};
            let rootLabel = label;

            if (isJson) {
                const jsonData = getValueFromData(data);
                const coercedData = coerceArray(jsonData);
                transformedViewConfig.push(transformLabel(labelTransformer, coercedData));
            } else {
                fields.forEach((item) => {
                    const conditions = getConditions(item);
                    if (!conditions.isVisible) return;

                    if (item.section) {
                        const { overrides: nestedOverrides, ...nestedSectionConfig } =
                            parseNestedSection({
                                section: item.section,
                                path: sectionKey,
                                currentJsonPath: rootLabel,
                                overrides
                            }) || {};
                        if (nestedSectionConfig) Object.assign(sectionConfig, nestedSectionConfig);
                    } else {
                        // simple field - key,value pair
                        const {
                            label,
                            value,
                            attributes = {},
                            type = 'text',
                            field,
                            tooltip,
                            visible = true
                        } = item;

                        const isVisible = getValueFromData(visible);

                        if (!isVisible) {
                            return;
                        }

                        let fieldConfig: Partial<FIELD_CONFIG> = {};

                        fieldConfig = {
                            key: label && getValueFromData(label),
                            value: getFieldValue(`${sectionKey}.${field}`, value, type),
                            props: evaluate(attributes),
                            type,
                            tooltipContent: tooltip && getValueFromData(tooltip)
                        };

                        const key = fieldConfig.key as string;
                        Object.assign(sectionConfig, { [key]: fieldConfig.value });

                        const finalKey = rootLabel ? `${rootLabel}.${key}` : key;

                        if (['status', 'json', 'link', 'template'].includes(type)) {
                            Object.assign(
                                overrides,
                                getOverride(type, finalKey, fieldConfig.props)
                            );
                        }

                        addTooltip(fieldConfig.tooltipContent, overrides, finalKey);
                    }
                });

                const finalConfig = label ? { [label]: sectionConfig } : sectionConfig;
                transformedViewConfig.push(finalConfig);
            }

            Object.assign(options, attributes, { renderType: type });
        }
    });

    return { transformedViewConfig, overrides, options };
}

function transformLabel(labelTransformer: string | undefined, coercedData: any[]) {
    let result = coercedData;
    if (labelTransformer) {
        result = coercedData.reduce((acc, item, index) => {
            const transformedLabel = executeTransformerFunction(labelTransformer, {
                data: item,
                index
            });
            return { ...acc, [transformedLabel]: item };
        }, {});
    }
    return result;
}

function getOverride(type: string, key: string, payload: Record<string, any>) {
    switch (type) {
        case 'status':
            return {
                [`$.${key}`]: {
                    renderValue: function (value: DetailsViewData) {
                        return h(
                            ZTag,
                            {
                                color: payload.type
                            },
                            {
                                default: () => payload.label // TODO: As per v2, can be re-checked
                            }
                        );
                    }
                }
            };
        case 'json':
            return {
                [`$.${key}`]: {
                    renderValue: function (value: DetailsViewData) {
                        return h(
                            ZJsonViewer,
                            {
                                data: value,
                                copyable: true
                            },
                            {
                                default: () => value
                            }
                        );
                    }
                }
            };
        case 'link':
            return {
                [`$.${key}`]: {
                    renderValue: function (value: DetailsViewData) {
                        return h(
                            ZLink,
                            {
                                href: payload.href
                            },
                            {
                                default: () => value
                            }
                        );
                    }
                }
            };
        case 'template':
            // TODO: Support for gds component inside the template
            return {
                [`$.${key}`]: {
                    renderValue: function (value: DetailsViewData) {
                        const component =
                            generateDynamicComponent(value, {
                                ...entityData.value,
                                context: parsedContext.value
                            }) || '';
                        return h(component, {
                            onActionClick: (id: string, innerHTML: string) =>
                                handleClickInTemplate(id, innerHTML)
                        });
                    }
                }
            };
        default:
            return {};
    }
}

function handleClickInTemplate(id: string, innerHTML: string) {
    emit('action-click', { id, innerHTML });
}

function parseTableConfig(section: TABLE_SECTION) {
    const conditions = getConditions(section);
    if (!conditions.isVisible) return;

    let transformedViewConfig: DATA_TABLE_CONFIG;
    const {
        data,
        tableConfig,
        isPaginated,
        paginationConfig,
        footerTemplate,
        hasStickyFooter = false
    } = section;
    let tableData = [];

    if (data?.handler && typeof data.handler === 'string') {
        tableData = executeTransformerFunction(data.handler, {
            ...entityData.value
        });
    } else {
        tableData = getValueFromData(data);
    }

    // For backward compatibility, Deprecated
    const defaultPaginationConfig = {
        page: paginationConfig?.currentPage || 1,
        pageSize: paginationConfig?.currentPerPage || 10,
        pageSizes: paginationConfig?.perPageItems || [5, 10, 25, 50],
        showSizePicker: true
    };

    const computedPaginationConfig = {
        ...defaultPaginationConfig,
        ...paginationConfig,
        onChange: (page: number) => {
            paginationReactive.page = page;
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize;
            paginationReactive.page = 1;
        }
    };

    const paginationReactive = reactive(computedPaginationConfig);

    const isPaginationHidden =
        paginationConfig?.removeOnLessData && tableData.length < paginationConfig?.currentPerPage;
    const isPaginationRequired = isPaginated && !isPaginationHidden;

    const columns = tableConfig.columns.map((column: any) => {
        const { field, label, sortable, template } = column;

        return {
            title: label,
            key: field,
            sorter: sortable ? 'default' : false,
            render(row: Record<string, any>) {
                if (!template) {
                    return row[field];
                }

                // data is being referred currently as data.row.<key> and data.column in the schema
                const component =
                    generateDynamicComponent(template, { data: { row, column: row[field] } }) || '';
                return h(component);
            }
        };
    });

    let summary: DataTableCreateSummary | undefined;
    if (footerTemplate) {
        const footerComponent =
            generateDynamicComponent(footerTemplate, {
                ...entityData,
                context: parsedContext.value
            }) || '';

        summary = (pageData: RowData[]) => {
            return {
                [columns[0].key]: {
                    value: h(footerComponent),
                    colSpan: 3
                }
            };
        };
    }

    transformedViewConfig = {
        columns,
        data: tableData,
        isTable: true,
        isPaginated: isPaginationRequired,
        paginationConfig: paginationReactive,
        hasStickyFooter,
        summary
    };
    return transformedViewConfig;
}

function parseNestedSection({
    section,
    path,
    currentJsonPath = '',
    overrides = {}
}: {
    section: SECTION;
    path: string;
    currentJsonPath?: string;
    overrides: Record<string, any>;
}) {
    const conditions = getConditions(section);
    if (!conditions.isVisible) return;

    const {
        label,
        accordionAttributes = {},
        attributes = {},
        key,
        tooltip,
        isJson = false,
        data = [],
        labelTransformer = '',
        maxNestedLevel = MAX_NESTED_LEVEL
    } = section;
    const config = {};

    const parentKey = label;
    const calculatedKey = currentJsonPath ? `${currentJsonPath}.${parentKey}` : parentKey;

    if (isJson) {
        const jsonData = getValueFromData(data);
        const coercedData = coerceArray(jsonData);
        Object.assign(config, transformLabel(labelTransformer, coercedData));
    } else {
        section.fields.forEach((item) => {
            const conditions = getConditions(item);
            if (!conditions.isVisible) return;

            if (item.section) {
                const { overrides: nestedOverrides, ...nestedConfig } =
                    parseNestedSection({
                        section: item.section,
                        path: `${path}.${key}`,
                        currentJsonPath: calculatedKey,
                        overrides
                    }) || {};
                if (nestedConfig) {
                    Object.assign(config, nestedConfig);
                    Object.assign(overrides, nestedOverrides);
                }
            } else {
                const {
                    label,
                    value,
                    attributes = {},
                    type = 'text',
                    field,
                    tooltip,
                    visible = true
                } = item;
                const isVisible = getValueFromData(visible);

                if (!isVisible) {
                    return;
                }
                let fieldConfig: Partial<FIELD_CONFIG> = {};

                fieldConfig = {
                    key: label && getValueFromData(label),
                    value: getFieldValue(`${path}.${key}.${field}`, value, type),
                    props: evaluate(attributes),
                    type,
                    tooltipContent: tooltip && getValueFromData(tooltip),
                    field
                };

                const finalKey = `${calculatedKey}.${fieldConfig.key}`;

                if (['status', 'json', 'link', 'template'].includes(type)) {
                    Object.assign(overrides, getOverride(type, finalKey, fieldConfig.props));
                }

                addTooltip(fieldConfig.tooltipContent, overrides, finalKey);

                Object.assign(config, { [fieldConfig.key as string]: fieldConfig.value });
            }
        });
    }
    return { [label]: config, overrides };
}

function addTooltip(tooltip: string | undefined, overrides: Record<string, any>, finalKey: string) {
    if (tooltip) {
        Object.assign(overrides, {
            [`$.${finalKey}`]: {
                options: {
                    tooltip: () => tooltip
                }
            }
        });
    }
}

function getFieldValue(path: string, value: any, type: string) {
    if (type === 'template') {
        return template(value)({
            ...entityData.value,
            params: parsedParams.value,
            entityId: props.entityId,
            tenantId: props.tenantId
        });
    }

    return getProperty(mergedEntityData.value, path, getValueFromData(value));
}
</script>

<style src="vue-json-pretty/lib/styles.css" />
<style scoped>
.header {
    padding: 16px;
}
.header-title,
.header-description {
    margin: 0px;
    line-height: unset;
}
.header--neutral {
    background-color: #f6f7fa;
}

.header--colored {
    background-color: #ffefdf;
}

.header--colorless {
    background: transparent;
}

.view-gap {
    margin-bottom: 16px;
}
</style>
