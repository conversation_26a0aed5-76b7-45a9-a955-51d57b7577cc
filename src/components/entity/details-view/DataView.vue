<template>
    <div class="view-container">
        <z-data-table
            v-if="view.viewData.isTable"
            :columns="view.viewData.columns"
            :data="view.viewData.data"
            :pagination="paginationConfig"
            :summary="view.viewData.summary"
            :bordered="false"
        />
        <template v-for="(viewData, index) in view.viewData">
            <div
                v-if="viewData && viewData.isCustomComponent"
                :key="`custom_comp-${index}`"
                :class="viewData.className"
            >
                <custom-component
                    v-if="!viewData.errorText"
                    :package="viewData.config.package.name"
                    :component="viewData.config.name"
                    :version="viewData.config.package.version"
                    v-bind="viewData.props"
                    v-on="viewData.eventsMap"
                >
                    <template #loading>
                        <z-skeleton
                            class="profile-resolver__loader"
                            viewBox="0 0 180 30"
                            :speed="3"
                        >
                            <rect x="0" y="0" rx="3" ry="3" width="180" height="30" />
                        </z-skeleton>
                    </template>
                    <template #error>
                        <z-card>
                            <z-result
                                variant="image"
                                :status="`500`"
                                :title="`Unable to load the custom component: ${viewData.config.name}`"
                                :description="`Package: ${viewData.config.package.name} with version : ${viewData.config.package.version}`"
                            >
                            </z-result>
                        </z-card>
                    </template>
                </custom-component>
                <z-card v-else>
                    <z-result
                        variant="image"
                        status="500"
                        title="Custom component config is invalid"
                        :description="viewData.errorText"
                    >
                    </z-result>
                </z-card>
            </div>
            <z-card v-else :key="`details-view-${index}`">
                <z-details-view
                    :data="viewData"
                    :overrides="view.overrides"
                    :options="view.options"
                    :divider="view.options.hasBottomDivider"
                />
            </z-card>
        </template>
    </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue';
import CustomComponent from '@/components/atomic/custom-comp/CustomComponent.vue';
const props = defineProps<{
    view: any; //TODO: Add type
}>();

const { view } = toRefs(props);
const paginationConfig = computed(() => {
    if (view?.value?.viewData?.isPaginated === false) {
        return false;
    } else {
        return view.value.viewData.paginationConfig;
    }
});
</script>

<style scoped>
.view-container {
    margin: 1rem;
}
</style>
