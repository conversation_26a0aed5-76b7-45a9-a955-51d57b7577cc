<template>
    <gds-root-component>
        <angelos-entity-config-provider
            :entity-id="entityId"
            :tenant-id="tenantId"
            :componentType="componentTypeRef"
            @data="onData"
        >
            <z-layout-header class="topbar">
                <slot name="topbar-action" />
                <z-space class="header" v-if="topbarConfig">
                    <z-space vertical :size="0">
                        <z-h3 class="header__title">
                            {{ topbarConfig.title }}
                        </z-h3>
                        <z-p class="header__description">
                            {{ topbarConfig.description }}
                        </z-p>
                    </z-space>
                    <z-space>
                        <!-- Slot for header action -->
                    </z-space>
                </z-space>
                <z-space class="filter" justify="space-between">
                    <z-space v-if="filterConfig?.length">
                        <z-input
                            v-if="isSearchEnabled"
                            v-model="searchQuery"
                            :placeholder="searchConfig?.placeholder || 'Search'"
                            class="filter-search-input"
                            @update:model-value="handleSearchInput"
                        >
                            <template #prefix>
                                <z-icon><Search /></z-icon>
                            </template>
                            <template #suffix>
                                <z-icon
                                    v-if="searchQuery"
                                    class="filter-search-input__close-icon"
                                    @click="handleCancelSearch"
                                    ><Cancel
                                /></z-icon>
                            </template>
                        </z-input>
                        <z-divider
                            v-if="isSearchEnabled && filterConfig?.length && isFilterConfigUpdated"
                            vertical
                            class="filter-divider"
                        />
                        <template v-if="isFilterConfigUpdated">
                            <z-filter
                                v-if="uiDataControl === true"
                                v-model="filters"
                                :onUpdate:data="onDataUpdatedByFilter"
                                :filters="filterConfig"
                            />
                            <z-filter
                                v-else
                                v-model="filters"
                                :filters="filterConfig"
                                :on-reset="onFilterReset"
                            />
                        </template>
                    </z-space>
                    <z-space class="actions">
                        <!-- Loop iterates over topbarConfig.tableActions -->
                        <template v-for="action in topbarConfig?.actions" :key="action.key">
                            <component :is="renderTableActionComponent(action)" />
                        </template>
                    </z-space>
                </z-space>
            </z-layout-header>
            <z-data-table
                class="angelos-table"
                :loading="isLoading"
                :remote="uiDataControl === false"
                :columns="viewConfig.tableConfig.columns"
                :data="data"
                :pagination="currentPaginationData"
                :row-props="rowProps"
                :striped="viewConfig.tableConfig.striped"
                @update:sorter="sortingUpdated"
                @update:checked-row-keys="handleCheckedRowKeysChange"
                :row-key="getRowKey"
                :has-error="hasError"
                :checked-row-keys="preselectedRowKeys"
            >
                <template #error>
                    <z-result
                        variant="image"
                        :status="errorObj.statusCode"
                        :title="errorObj.message"
                        :description="errorObj.description"
                    />
                </template>
            </z-data-table>
        </angelos-entity-config-provider>
    </gds-root-component>
</template>

<script setup lang="ts">
import { cloneDeep, debounce, isArray, isEmpty, isNil } from 'lodash-es';
import AngelosEntityConfigProvider from '@/common/AngelosEntityConfigProvider.vue';
import GdsRootComponent from '@/common/GdsRootComponent.vue';
import { defineComponent, h, ref, toRefs, computed, provide } from 'vue';
import { gdsDataGridContractMapper, getRenderTableActionContext } from '@/core/gdsConfigMapper';
import { useEntityCore } from '@/composable';
import {
    flattenObject,
    executeTransformerFunction,
    harRequestWithLruCache,
    getErrorDetails,
    createInputFilterSelector,
    useDataStore
} from '@/core/utils';
import {
    ZDataTable,
    ZFilter,
    ZResult,
    type DataTableSortState,
    ZLayoutHeader,
    ZH3,
    ZP,
    ZIcon,
    ZInput,
    ZDivider,
    type PaginationProps
} from '@zeta-gds/components';
import type {
    CustomError,
    TableErrorConfig,
    StatusCode,
    TableInputFilter,
    TableDataConfig,
    SearchConfig,
    TopbarConfig,
    TopbarAction,
    TableColumn
} from '@/types';
import type { RowData } from '@zeta-gds/components/lib/data-table/src/interface';
import type { DataFilterItemConfig, Option } from '@zeta-gds/components/lib/filter/src/interface';
import { v4 as uuid } from 'uuid';
import {
    ANGELOS_COMPONENT_TYPES,
    BODY_SUPPORTED_REST_METHODS,
    DATA_TABLE_EVENTS,
    DEBOUNCE_TIME
} from '@/core/constants';
import { StoreKey, useComponentStore } from '@/composable/useComponentStore';
interface Props {
    entityId: string;
    tenantId: string;
    context?: string | object;
    params?: string | object;
    // componentType: 'create-form' | 'update-form' | 'data-table' | 'details-view';
    externalSubmit?: (data: any) => void;
}

const props = withDefaults(defineProps<Props>(), {
    context: '',
    params: ''
});

const componentTypeRef = ref(ANGELOS_COMPONENT_TYPES.DATA_TABLE);
const store = useComponentStore(props.entityId, props.tenantId, componentTypeRef.value);
provide(StoreKey, store);

const hasError = ref(false);
const errorObj = ref<TableErrorConfig>({
    statusCode: 'error',
    message: '',
    description: ''
});

const { entityId, tenantId, context, params } = toRefs(props);

const viewConfig = ref<any>({});
const dataConfig = ref<TableDataConfig | null>(null);
const baseUrl = ref('');
const uiDataControl = ref(false);
const currentPaginationData = ref<PaginationProps & { hasNext: boolean }>();
const searchQuery = ref('');
const searchConfig = ref<SearchConfig | null>(null);
const searchKeyList = ref([]);
const topbarConfig = ref<TopbarConfig>();

const isMultiSelect = ref(true);
const globallySelectedRows = ref<Map<string, RowData>>(new Map());
const preselectedRowKeys = computed(() => Array.from(globallySelectedRows.value.keys()));

const isSearchEnabled = computed(() => {
    return !!searchConfig.value?.searchKey;
});

const { attachCss, getInputParams, getHarRequestParams } = useEntityCore({
    entityId: entityId.value,
    tenantId: tenantId.value,
    componentType: componentTypeRef.value,
    context: context.value || {},
    params: params.value || {},
    store
});

const filters = ref<Record<string, any>>({});
const pagination = ref(false);
const isFilterConfigUpdated = ref(false);
const filterConditons = ref(new Map());
const sortingState = ref<{ key: string; order: 'descend' | 'ascend' | false } | null>(null); //State to store the current sort params
const lru = ref(new Map());
const isLoading = ref(false);
const sourceData = ref([]);
const data = ref<any>([]);
const clientFilterData = ref([]);
const filterConfig = ref<DataFilterItemConfig[]>([]);
const dataStore = useDataStore();
const defaultPaginationConfig = {
    page: 1,
    pageSize: 5,
    showSizePicker: true,
    pageSizes: [5, 10, 20]
};

const rowProps = (row: RowData) => {
    return {
        style: 'cursor: pointer;',
        onClick: () => {
            if (row) {
                emit('item-clicked', row);
            }
        }
    };
};

const emit = defineEmits(DATA_TABLE_EVENTS);

function getOptionsFromData<T extends Record<string, any>>(data: T[] | undefined, field: keyof T) {
    if (!Array.isArray(data)) return [];

    return data
        .filter((item) => item && field in item)
        .map((item) => ({
            label: String(item[field]),
            value: item[field]
        })) as Option[];
}

// To get unique key for each row
const getRowKey = (row: RowData) => {
    return row.__table_row_id;
};

/**
 * Removes the internal __table_row_id property from a row or an array of rows before emitting.
 * @param data A single row object or an array of row objects.
 * @returns The cleaned data without the __table_row_id property.
 */
const removeTableRowId = (data: RowData | RowData[] | null | undefined): any => {
    if (!data) {
        return data;
    }
    if (Array.isArray(data)) {
        return data.map((row) => {
            const newRow = { ...row };
            delete newRow.__table_row_id;
            return newRow;
        });
    }
    const newRow = { ...data };
    delete newRow.__table_row_id;
    return newRow;
};

/**
 * Handles row selection events
 * This function manages both single and multi-select modes.
 * For multi-select, it maintains an array of selected rows in selectedRows.value
 * For single-select, it tracks the current selection in selectedRow.value
 * Each row has a unique ID (__table_row_id) that ensures stable identification
 * across sorting, filtering, and pagination operations.
 */
const handleCheckedRowKeysChange = (
    keys: (string | number)[],
    rows: RowData[],
    meta: { row: RowData | undefined; action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }
) => {
    if (isMultiSelect.value) {
        // We now update the global map based on the specific action,
        // which prevents wiping the state during pagination.
        switch (meta.action) {
            case 'check':
                if (meta.row) {
                    globallySelectedRows.value.set(meta.row.__table_row_id, meta.row);
                }
                break;
            case 'uncheck':
                if (meta.row) {
                    globallySelectedRows.value.delete(meta.row.__table_row_id);
                }
                break;
            case 'checkAll':
                // `rows` contains all rows selected on the current page.
                // By using data.value, we ensure all items on the current page are selected.
                data.value.forEach((row: RowData) => {
                    globallySelectedRows.value.set(row.__table_row_id, row);
                });
                break;
            case 'uncheckAll':
                // We need to find all rows on the current page and remove them from the global selection.
                data.value.forEach((row: RowData) => {
                    globallySelectedRows.value.delete(row.__table_row_id);
                });
                break;
        }
        emit('selection-update', removeTableRowId(Array.from(globallySelectedRows.value.values())));
    } else {
        // For single select mode, we clear and set the new row.
        if (meta.action === 'check' && meta.row) {
            globallySelectedRows.value.clear();
            globallySelectedRows.value.set(meta.row.__table_row_id, meta.row);
            emit('selection-update', removeTableRowId(meta.row));
        } else {
            globallySelectedRows.value.clear();
            emit('selection-update', null);
        }
    }
};

function createFilterConfig(filterConfig: DataFilterItemConfig[]) {
    // Add warning for unsupported selectors when uiDataControl is true
    if (uiDataControl.value === true) {
        const supportedSelectors = ['by-value', 'by-input'];
        const unsupportedSelectors: string[] = [];

        for (let filter of filterConfig) {
            if (filter.selectors) {
                const selectorKeys = Object.keys(filter.selectors);
                const unsupportedInThisFilter = selectorKeys.filter(
                    (key) => !supportedSelectors.includes(key)
                );
                unsupportedSelectors.push(...unsupportedInThisFilter);
            }
        }

        if (unsupportedSelectors.length > 0) {
            console.warn(
                `[AngelosDataTable] When uiDataControl is true, only 'by-value' and 'by-input' selectors are currently supported. ` +
                    `Found unsupported selectors: ${[...new Set(unsupportedSelectors)].join(', ')}. ` +
                    `These filters may not work as expected in client-side filtering mode.`
            );
        } else {
            console.info(
                `[AngelosDataTable] uiDataControl is enabled. Client-side filtering supports 'by-value' and 'by-input' selectors.`
            );
        }
    }
    for (let filter of filterConfig) {
        // first step is to check if we have a selector for the filter or not
        /**
         * If yes, then we will use the selectors and only fill in the check for the options ( if it is not already filled )
         * If no, then by default we will construct the 'by-value' selector
         */
        if (isEmpty(filter.selectors)) {
            filter.selectors = {
                'by-value': {
                    type: 'value',
                    value: '',
                    options: getOptionsFromData(data.value, filter.field)
                }
            };
        } else {
            if (filter.selectors['by-value'] && !filter.selectors['by-value'].options) {
                filter.selectors['by-value'] = {
                    ...filter.selectors['by-value'],
                    options: getOptionsFromData(data.value, filter.field)
                };
            }
            // We will provide a new option (not available in gds by default), filter by input. Since this is a custom filter, we will add the component to the filter
            if (filter.selectors['by-input']) {
                // making the key unique to avoid any conflicts with other filters
                const key = `${uuid()}-${filter.field}-custom-input-selector`;
                filter.selectors[key] = filter.selectors['by-input'];
                // delete the by-input selector as it will be also added as a custom input selector if not removed
                delete filter.selectors['by-input'];
                createInputFilterSelector(filter as unknown as TableInputFilter, key);
            }
        }
        // If BE Pagination is opted then we will just call the api when filter is applied
        if (uiDataControl.value !== true) {
            filter.onApply = () => {
                resetPageNumberAndData();
                getData();
            };
            filter.onReset = () => {
                for (let key in filters.value) {
                    if (key === filter.field) {
                        delete filters.value[key];
                    }
                }
                resetPageNumberAndData();
                getData();
            };
            filter.onCancel = () => {
                // TODO: add the cancel functionality once supported by GDS
            };
        }
    }
    isFilterConfigUpdated.value = true;
    return filterConfig;
}
/**
 * Function to handle filter reset
 */
function onFilterReset() {
    filters.value = {};
    resetPageNumberAndData();
    getData();
}

const debouncedGetData = debounce(getData, DEBOUNCE_TIME);

/**
 * Unified function to apply both search and filter together
 * This ensures search and filter work in combination when uiDataControl is true
 */
function applySearchAndFilter() {
    if (uiDataControl.value !== true) return;

    let filteredData = cloneDeep(sourceData.value);

    // Apply search first if we have a search query
    if (searchQuery.value && searchQuery.value.trim() && searchConfig.value) {
        const query = searchQuery.value.toLowerCase();
        const searchKey = searchConfig.value.searchKey;

        if (searchConfig.value.customSearchFn) {
            filteredData = filteredData.filter((item: any) => {
                try {
                    return executeTransformerFunction(searchConfig?.value?.customSearchFn || '', {
                        item,
                        query
                    });
                } catch (error) {
                    console.error('Error executing custom search function:', error);
                    return false;
                }
            });
        } else {
            filteredData = filteredData.filter((item: any) => {
                const fieldValue = item[searchKey];
                return !isNil(fieldValue) && String(fieldValue).toLowerCase().includes(query);
            });
        }
    }

    // Apply filters on the search results
    if (Object.keys(filters.value).length > 0) {
        const activeFilters = getFilterConfig();

        for (const [filterKey, filterValue] of Object.entries(
            activeFilters as Record<string, any>
        )) {
            const filterConfigItem = filterConfig.value.find((f) => f.field === filterKey);
            if (
                filterConfigItem &&
                filterValue.value !== undefined &&
                filterValue.value !== null &&
                filterValue.value !== ''
            ) {
                filteredData = filteredData.filter((item: any) => {
                    const itemValue = item[filterKey];
                    const selectedValue = filterValue.value;
                    const activeSelectorKey = filterValue.selector;

                    if (activeSelectorKey && filterConfigItem.selectors?.[activeSelectorKey]) {
                        const selectorConfig = filterConfigItem.selectors[activeSelectorKey];

                        if (activeSelectorKey === 'by-value') {
                            if (selectorConfig.multiple) {
                                return (
                                    Array.isArray(selectedValue) &&
                                    selectedValue.includes(itemValue)
                                );
                            }
                        }
                    }
                    return String(itemValue)
                        .toLowerCase()
                        .includes(String(selectedValue).toLowerCase());
                });
            }
        }
    }

    // Update the data
    data.value = filteredData;

    // Update pagination count if pagination is active
    if (pagination.value && currentPaginationData.value) {
        currentPaginationData.value.itemCount = filteredData.length;
        // Reset to first page when search/filter changes
        if (currentPaginationData.value.page !== 1) {
            currentPaginationData.value.page = 1;
        }
    }
}

// Debounce the unified function
const debouncedApplySearchAndFilter = debounce(applySearchAndFilter, DEBOUNCE_TIME);

// Update handleSearchInput to use the unified approach
function handleSearchInput() {
    if (uiDataControl.value === true) {
        debouncedApplySearchAndFilter();
    } else {
        // When uiDataControl is false, trigger backend search
        resetPageNumberAndData(); // Reset pagination for new backend search
        debouncedGetData();
    }
}

/**
 * Function to handle filter data update
 * This would be called only when uiDataControl is true
 */
function onDataUpdatedByFilter() {
    if (uiDataControl.value === true) {
        // Apply both search and filter together
        applySearchAndFilter();
    } else {
        resetPageNumberAndData();
    }
}

// Set the page number to the first page
function resetPageNumberAndData() {
    if (currentPaginationData.value) {
        currentPaginationData.value.page = 1;
        dataStore.clearData();
    }
}

function updateContext() {
    const obj: any = { filter: {}, pagination: {} };
    for (const [key, value] of filterConditons.value as any) {
        const filter = filterConfig.value?.[key];
        obj.filter[filter.field] = value;
    }
    obj.pagination = currentPaginationData;
    return obj;
}
function handleDynamicContent() {
    const flattenFieldConfig = flattenObject(viewConfig.value);
    const attributesWithHandler = Object.keys(flattenFieldConfig).filter((attributePath) =>
        attributePath.endsWith('.handler')
    );
    attributesWithHandler.forEach((handler) => {
        const handlerValue = flattenFieldConfig[handler];
        executeTransformerFunction(handlerValue, {
            ...getInputParams()
        });
        // TODO: fix the dynamic tranformation function to be able to set the value in the viewConfig
        const currentViewConfig = cloneDeep(viewConfig.value);
        // setProperty(currentViewConfig, handlerAttributePath, calculatedValue);
        viewConfig.value = currentViewConfig;
    });
}

/**
 * Function to render the table action component
 */
const renderTableActionComponent = (actionConfig: TopbarAction) => {
    if (!actionConfig || !actionConfig.render) {
        console.warn('Topbar action is missing or has no render function:', actionConfig);
        return null;
    }
    return defineComponent({
        // props: { action: Object as () => TableAction }, // Could pass action as prop
        render() {
            try {
                return executeTransformerFunction(
                    actionConfig.render || '',
                    getRenderTableActionContext(getInputParams(), emit)
                );
            } catch (error) {
                console.error(
                    'Error executing topbar action render function:',
                    error,
                    actionConfig
                );
                return null;
            }
        }
    });
};

function onData(config: any) {
    dataConfig.value = config.dataConfig;
    viewConfig.value = cloneDeep(config.viewConfig);
    viewConfig.value = gdsDataGridContractMapper(viewConfig.value, getInputParams(), emit);
    handleDynamicContent();
    attachCss(viewConfig.value.css);
    baseUrl.value = dataConfig.value?.httpRequest?.url ?? '';
    // Set isMultiSelect based on table configuration
    if (viewConfig.value.tableConfig?.columns) {
        // Find the selection column if it exists
        const selectionColumn = viewConfig?.value?.tableConfig?.columns?.find(
            (column: TableColumn) => column.type === 'selection'
        );
        // If a selection column exists and has multiple=false, use single-select mode
        isMultiSelect.value = !(selectionColumn && selectionColumn.multiple === false);
    }
    // Do we have pagination available ?
    pagination.value = Boolean(viewConfig.value.paginationConfig);
    /**
     * Do we have ui pagination or be pagination ?
     * If the pagination params are not supplied, we can assume that it is frontend pagination
     */
    uiDataControl.value = viewConfig.value.uiDataControl;
    // If pagination is available then we will create the pagination config
    if (pagination.value) {
        currentPaginationData.value = {
            ...(viewConfig.value.paginationConfig ?? defaultPaginationConfig),
            onChange: (page: number) => {
                if (currentPaginationData.value) {
                    currentPaginationData.value.page = page;
                }
                // for BE pagination we set the data from store or fetch
                if (uiDataControl.value !== true) {
                    if (currentPaginationData.value) {
                        // check if data is in data store for tha page, if not fetch the data and add it
                        const paginatedData = dataStore.getDataForPage(page);
                        if (paginatedData) {
                            if (isArray(paginatedData)) {
                                data.value = [...paginatedData];
                            } else {
                                data.value = paginatedData.data;
                                if ('hasNext' in paginatedData) {
                                    currentPaginationData.value.hasNext = Boolean(
                                        paginatedData.hasNext
                                    );
                                }
                            }
                        } else {
                            getData();
                        }
                    }
                }
            },
            onUpdatePageSize: (pageSize: number) => {
                if (currentPaginationData.value) {
                    currentPaginationData.value.pageSize = pageSize;
                    currentPaginationData.value.page = 1;
                }
                // for be pagination
                if (uiDataControl.value !== true) {
                    resetPageNumberAndData();
                    getData();
                }
            },
            hasNext: true,
            /**
             * We are adding an overwrite for prefix to ignore the prefix key  if it is available in view config
             * Reason - We will support a fix prefix format which will only show when we have frontend pagination or be pagination with total count
             * any random usage of prefix by view config can break the gds table. This would mitigate that
             */
            prefix: () => {
                return ``; // Overwrite added so that if prefix is
            }
        };
    }
    searchQuery.value = '';
    searchConfig.value = viewConfig.value.searchConfig || null;

    searchKeyList.value = viewConfig.value.search?.searchKeyList;
    topbarConfig.value = {
        title: viewConfig.value.topbarConfig?.title || viewConfig.value.title || '',
        description:
            viewConfig.value.topbarConfig?.description || viewConfig.value.description || '',
        actions: viewConfig.value.topbarConfig?.actions || []
    };
    filterConfig.value = cloneDeep(viewConfig.value.filterConfig);
    getData();
}
/**
 * Function to create the config and fetching the data for the api
 */
async function getData() {
    try {
        if (!baseUrl.value) return;
        isLoading.value = true;
        hasError.value = false;
        const method = (dataConfig.value as any)?.httpRequest?.method;
        // Make the HTTP request
        await fetchAndProcessData(method);
    } catch (e) {
        handleError(e as CustomError);
    }
}

function getFilterConfig() {
    if (!Object.keys(filters.value).length) return {};
    // Clean out filter items with no value
    let filterItemsWithValues: { [key: string]: { value: string } } = {};
    for (const key in filters.value) {
        const filterValue = filters.value[key]?.value;
        if (filterValue !== undefined && filterValue !== null && filterValue !== '') {
            filterItemsWithValues[key] = filters.value[key];
        }
    }
    return filterItemsWithValues;
}
async function fetchAndProcessData(method: string) {
    let httpRequest = (dataConfig.value as any).httpRequest;
    //context data will hold the filter and pagination data for the api
    const contextData = updateContext();
    const inputParams = getHarRequestParams();
    let params: {
        name: string;
        value: any;
    }[] = [];
    let body = {};
    const requestTransformer = dataConfig.value?.requestTransformer;
    // Transform the headers
    if (requestTransformer?.headers) {
        httpRequest = {
            ...dataConfig.value?.httpRequest,
            headers: executeTransformerFunction(requestTransformer?.headers, {
                ...inputParams
            })
        };
    }
    const transformationParams = {
        ...inputParams,
        pagination: {
            pageSize: currentPaginationData.value?.pageSize,
            pageNumber: currentPaginationData.value?.page
        },
        filter: getFilterConfig(),
        search: {
            searchKeyList: searchKeyList.value,
            searchValue: searchQuery.value,
            searchKey: searchConfig.value?.searchKey
        },
        sort: {
            sortKey: sortingState.value?.key,
            sortOrder: sortingState.value?.order
        },
        data: dataStore.getAllData()
    };
    // Transform the query
    if (requestTransformer?.query) {
        const query = executeTransformerFunction(requestTransformer?.query, transformationParams);
        Object.keys(query).forEach((key) => {
            params.push({
                name: key,
                value: query[key]
            });
        });
    }
    // Transform the body
    if (requestTransformer?.body) {
        body = executeTransformerFunction(requestTransformer?.body, transformationParams);
    }

    if (method && BODY_SUPPORTED_REST_METHODS.includes(method)) {
        httpRequest.postData = {
            mimeType: 'application/json',
            text: `${JSON.stringify({ ...body })}`
        };
    }
    httpRequest.queryString = [...params];

    body = {
        ...body,
        inputParams,
        contextData: JSON.stringify(contextData)
    };
    const lruKey = BODY_SUPPORTED_REST_METHODS.includes(method)
        ? `${JSON.stringify(body)}${JSON.stringify(params)}`
        : JSON.stringify(params);
    (dataConfig.value as any).httpRequest = httpRequest;
    let localData: any = [];

    localData = await harRequestWithLruCache(
        dataConfig.value,
        getHarRequestParams,
        lruKey,
        lru.value,
        contextData
    );
    /**
     * Assign a unique ID to each row in the dataset
     * The __table_row_id property is used by getRowKey() to provide consistent row keys
     * which improves selection state persistence
     */
    if (isArray(localData)) {
        // Add a unique __table_row_id to each row
        localData = localData.map((row: RowData) => ({
            ...row,
            __table_row_id: uuid()
        }));
    } else if (typeof localData === 'object' && localData.data) {
        // Handle paginated response format
        localData.data = localData.data.map((row: RowData) => ({
            ...row,
            __table_row_id: uuid()
        }));
    }

    if (pagination.value && currentPaginationData.value) {
        // For Frontend Pagination case
        if (uiDataControl.value === true) {
            let fullDataset: any[] = [];
            // we will assume that we will be receving an array of objects
            if (isArray(localData)) {
                fullDataset = cloneDeep(localData);
            }
            // Assign directly to data.value for ZDataTable internal processing
            data.value = cloneDeep(fullDataset);
            currentPaginationData.value.itemCount = fullDataset.length;
            currentPaginationData.value.prefix = ({ itemCount, startIndex, endIndex }) => {
                const actualStartIndex = Math.min(startIndex + 1, Number(itemCount));
                const actualEndIndex = Math.min(endIndex + 1, Number(itemCount));
                return Number(itemCount) > 0
                    ? `${actualStartIndex} - ${actualEndIndex} of ${itemCount}`
                    : '0 items';
            };
        } else {
            // Let's store the data for pagination
            if (currentPaginationData.value.page) {
                dataStore.setData(currentPaginationData.value.page, localData);
            }
            // we will assume that it will follow one of the pagination response structures Angelos supported
            /**
             * Angelos supports the following pagination cases
             * 1. We get total count and data array
             * 2. We get data array and hasNext flag, but no data count
             * 3. We get data array but no hasNext flag and no data count
             */

            if (typeof localData === 'object' && localData.total && localData.total > 0) {
                // case 1
                // Update the total count
                currentPaginationData.value.itemCount = localData.total;
                data.value = localData.data;
                currentPaginationData.value.prefix = ({ itemCount, startIndex, endIndex }) => {
                    return `${startIndex + 1} - ${endIndex + 1} of ${itemCount}`;
                };
                currentPaginationData.value = {
                    ...currentPaginationData.value,
                    itemCount: localData.total,
                    prefix: ({ itemCount, startIndex, endIndex }) => {
                        return `${startIndex + 1} - ${endIndex + 1} of ${itemCount}`;
                    }
                };
            } else if (typeof localData === 'object' && 'hasNext' in localData) {
                // case 2
                data.value = localData.data as any;
                currentPaginationData.value = {
                    ...currentPaginationData.value,
                    prefix: ({ startIndex, endIndex }) => {
                        return `${startIndex + 1} - ${endIndex + 1}`;
                    },
                    hasNext: localData.hasNext
                };
            } else if (isArray(localData)) {
                // case 3
                /** not catering now */
            } else {
                data.value = [];
            }
        }
    } else {
        data.value = localData;
    }
    if (data.value.length) {
        sourceData.value = cloneDeep(data.value) as any;
        clientFilterData.value = cloneDeep(data.value) as any;
        filterConfig.value = filterConfig.value ? createFilterConfig(filterConfig.value) : [];

        processPreselectedRows(data.value);
    } else {
        data.value = [];
    }
    emit('on-data', data.value);
    isLoading.value = false;
}
/**
 * Handle error while getting the data for the table
 *
 */
function handleError(e: CustomError) {
    console.error(e);
    const statusCode = e?.error?.status as StatusCode;
    const errorDetails = getErrorDetails(statusCode);
    errorObj.value = {
        statusCode: statusCode ? statusCode : 'warning',
        message: errorDetails.errorName,
        description: errorDetails.userFriendlyMessage
    };
    hasError.value = true;
    isLoading.value = false;
    data.value = [];
}

/**
 * Method invoked when a column is sorted
 */
function sortingUpdated(ev: DataTableSortState | null) {
    if (uiDataControl.value !== true && pagination.value) {
        sortingState.value = {
            key: (ev?.columnKey || '') as string,
            order: ev?.order || false
        };
        getData();
    }
}

function handleCancelSearch() {
    searchQuery.value = '';
    handleSearchInput();
}

/**
 * Function to process preselected rows using selectionHandler
 * This function uses executeTransformerFunction to determine which rows should be preselected
 */
function processPreselectedRows(data: RowData[]) {
    if (!data || !data.length) return;

    // Find the selection column to get the selectionHandler
    const selectionColumn = viewConfig.value?.tableConfig?.columns?.find(
        (column: TableColumn) => column.type === 'selection'
    );

    if (!selectionColumn || !selectionColumn.selectionHandler) {
        return;
    }
    const inputParams = getInputParams();

    data.forEach((row: RowData) => {
        try {
            // Execute the selection handler function for each row
            const shouldBeSelected = executeTransformerFunction(selectionColumn.selectionHandler, {
                row,
                context: inputParams.context,
                params: inputParams.params
            });

            // If the handler returns true, add the row to the global selection map.
            if (shouldBeSelected === true) {
                globallySelectedRows.value.set(row.__table_row_id, row);
            }
        } catch (error) {
            console.error('Error executing selection handler for row:', error, row);
        }
    });
}
</script>
<style scoped lang="scss">
* {
    font-family: 'IBM Plex Sans';
}
.topbar {
    display: flex;
    flex-direction: column;
    gap: 24px;
    .header {
        &__title,
        &__description {
            margin: 0px;
            line-height: unset;
            color: #020d4d;
        }
    }
    .filter {
        &.z-space {
            justify-content: space-between;
        }

        margin-bottom: 16px;
    }

    // justify-content: space-between;

    // &__title {
    //     margin: 0px;
    //     color: #020D4D;
    // }
}
.angelos-table {
    :deep(.z-data-table-th__title) {
        font-size: 14px;
        font-weight: 500;
    }
}
:deep(.z-data-table-td) {
    &.z-data-table-td--selection {
        padding: var(--z-td-padding);
    }
}
:deep(.z-data-table-th) {
    &.z-data-table-th--selection {
        padding: var(--z-th-padding);
    }
}

.filter-divider {
    height: 20px;
}
.filter-search-input {
    width: 300px;
    top: -3px;

    &__close-icon {
        cursor: pointer;
    }
}
</style>
