<template>
    <GdsRootComponent>
        <AngelosEntityConfigProvider
            :entityId="entityId"
            :tenantId="tenantId"
            :componentType="componentTypeRef"
            @data="onConfigData"
        >
            <div v-if="!isLoading">
                <component
                    :is="dynamicComponent"
                    :data="entityData"
                    :context="parsedContext"
                    :params="parsedParams"
                    :entityId="entityId"
                    :tenantId="tenantId"
                />
            </div>

            <div v-else>
                <DynamicViewSkeleton />
            </div>
            <template #loader>
                <DynamicViewSkeleton />
            </template>
        </AngelosEntityConfigProvider>
    </GdsRootComponent>
</template>

<script setup lang="ts">
import { chainDataConfigs, coerceArray } from '@/core/utils';
import { useEntityCore, useDynamicComponent } from '@/composable';
import GdsRootComponent from '@/common/GdsRootComponent.vue';
import AngelosEntityConfigProvider from '@/common/AngelosEntityConfigProvider.vue';
import DynamicViewSkeleton from './DynamicViewSkeleton.vue';
import { ref, toRefs, provide } from 'vue';
import { StoreKey, useComponentStore } from '@/composable/useComponentStore';
import { ANGELOS_COMPONENT_TYPES } from '@/core/constants';

const props = defineProps<{
    entityId: string;
    tenantId: string;
    context: any;
    params: any;
}>();
const { entityId, tenantId, context, params } = toRefs(props);

const emit = defineEmits(['data-error']);

const componentTypeRef = ref(ANGELOS_COMPONENT_TYPES.DYNAMIC_VIEW);
const store = useComponentStore(entityId.value, tenantId.value, componentTypeRef.value);
provide(StoreKey, store);

const { parsedContext, parsedParams, attachCss, getHarRequestParams } = useEntityCore({
    context: context?.value || {},
    params: params?.value || {},
    tenantId: tenantId?.value,
    entityId: entityId?.value,
    componentType: componentTypeRef.value,
    store
});
const { generateDynamicComponent } = useDynamicComponent();

const isLoading = ref(true);
const entityData = ref({});
const dynamicComponent = ref<any>(null);

async function getResponseData(dataConfigs: any[]) {
    try {
        isLoading.value = true;
        const { response } = await chainDataConfigs({
            configList: dataConfigs,
            context: getHarRequestParams(),
            requestData: {},
            executeResponseTransformer: true
        });
        return response;
    } catch (error) {
        emit('data-error', { error });
        console.error('Error in fetching data', error);
        return {};
    } finally {
        isLoading.value = false;
    }
}

const onConfigData = async (data: any) => {
    const dataConfigs = coerceArray(data.dataConfig);
    const response = await getResponseData(dataConfigs);
    entityData.value = { ...response };
    attachCss(data.viewConfig.css);
    renderDynamicComponent(data);
};

function renderDynamicComponent(data: any) {
    const { viewConfig } = data;
    dynamicComponent.value =
        generateDynamicComponent(viewConfig.template, entityData.value, [
            'entityId',
            'tenantId',
            'context',
            'params',
            'data'
        ]) || '';
}
</script>
