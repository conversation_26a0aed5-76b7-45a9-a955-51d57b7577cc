<template>
    <GdsRootComponent>
        <AngelosForm
            :componentType="componentTypeRef"
            :entityId="entityId"
            :tenantId="tenantId"
            :context="context"
            :params="params"
            :externalSubmit="externalSubmit"
            :hide-form-actions="hideFormActions"
            :app-timezone="appTimezone"
            @cancel-success="(e: any) => $emit('cancel-success', e)"
            @reset-success="(e: any) => $emit('reset-success', e)"
            @before-submit="(e: any) => $emit('before-submit', e)"
            @submit-success="(e: any) => $emit('submit-success', e)"
            @submit-error="(e: any) => $emit('submit-error', e)"
            @after-submit="(e: any) => $emit('after-submit', e)"
            @prefill-success="(e) => $emit('prefill-success', e)"
            @prefill-error="(e) => $emit('prefill-error', e)"
            @after-file-select="(e) => $emit('after-file-select', e)"
        />
    </GdsRootComponent>
</template>

<script setup lang="ts">
import AngelosForm from '@/common/AngelosForm.vue';
import loadComponents from '@/plugins/components-loader';
import { getCurrentInstance, onMounted, ref, toRefs } from 'vue';
import GdsRootComponent from '../../common/GdsRootComponent.vue';
import { ANGELOS_COMPONENT_TYPES } from '@/core/constants';

interface Props {
    entityId: string;
    tenantId: string;
    context?: string | object;
    params?: string | object;
    externalSubmit?: (data: any) => void;
    appTimezone?: string;
    showFormActions: string;
}

const props = defineProps<Props>();
defineEmits<{
    (e: 'cancel-success', value: any): void;
    (e: 'reset-success', value: any): void;
    (e: 'before-submit', value: any): void;
    (e: 'submit-success', value: any): void;
    (e: 'submit-error', value: any): void;
    (e: 'after-submit', value: any): void;
    (e: 'prefill-success', value: any): void;
    (e: 'prefill-error', value: any): void;
    (e: 'after-file-select', value: any): void;
}>();
const { entityId, tenantId, context, params, externalSubmit, showFormActions } = toRefs(props);
let hideFormActions = ref(false);
const componentTypeRef = ref(ANGELOS_COMPONENT_TYPES.CREATE_FORM);

onMounted(() => {
    hideFormActions.value =
        showFormActions.value !== undefined && showFormActions.value === 'false';
    loadComponents(getCurrentInstance()!.appContext.app);
});
</script>
