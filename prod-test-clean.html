<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link
            rel="stylesheet"
            href="https://hercules-assets.mum1-pp.zetaapps.in/common-assets/3.0.144/fonts/ibmplex.min.css"
        />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans" />
        <title>Angelos SDK - Production Build Test (Clean)</title>

        <!-- Use the production build -->
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            // Minimal configuration without problematic dependencies
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
            
            // Set some basic context directly
            localStorage.setItem('AT', 'test-token');
        </script>
    </head>
    <body>
        <div id="app">
            <h1>🚀 Angelos SDK - Production Build Test (Clean)</h1>
            
            <div style="margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 5px">
                <h3>🎛️ Component Testing</h3>
                <p><strong>✅ No context loading errors</strong> - Clean production test</p>
                <button onclick="addFormComponent()">Add Create Form</button>
                <button onclick="addTableComponent()">Add Data Table</button>
                <button onclick="addDetailsComponent()">Add Details View</button>
                <button onclick="clearComponents()">Clear All</button>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: #fffacd; border-radius: 5px">
                <h3>📊 Network Monitoring</h3>
                <p><strong>Open DevTools → Network tab to see chunks loading!</strong></p>
                <div id="chunk-loading-status"></div>
            </div>

            <!-- Container for dynamically added components -->
            <div id="dynamic-components"></div>
        </div>

        <script>
            let chunkCount = 0;
            
            function updateChunkStatus(message) {
                const status = document.getElementById('chunk-loading-status');
                status.innerHTML += `<div>📦 ${message}</div>`;
                console.log(`📦 ${message}`);
            }

            // Wait for SDK to initialize
            function waitForSDK() {
                return new Promise((resolve) => {
                    if (window.AngelosSDK) {
                        resolve(window.AngelosSDK);
                        return;
                    }
                    
                    const checkSDK = () => {
                        if (window.AngelosSDK) {
                            updateChunkStatus('✅ Angelos SDK initialized');
                            resolve(window.AngelosSDK);
                        } else {
                            setTimeout(checkSDK, 100);
                        }
                    };
                    checkSDK();
                });
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', async () => {
                updateChunkStatus('🚀 DOM loaded, waiting for SDK...');
                const sdk = await waitForSDK();
                updateChunkStatus('🎉 SDK ready for component testing');
            });

            async function addFormComponent() {
                updateChunkStatus('🔄 Adding form component - expect forms-*.js chunk');
                const container = document.getElementById('dynamic-components');
                const formDiv = document.createElement('div');
                formDiv.style.cssText =
                    'margin: 20px 0; padding: 15px; border: 2px solid #4CAF50; border-radius: 5px;';
                formDiv.innerHTML = `
                    <h3>📝 Create Form Component</h3>
                    <zwe-angelos-create-form-v3
                        entity-id="test-entity"
                        tenant-id="0"
                        show-form-actions="true"
                    ></zwe-angelos-create-form-v3>
                `;
                container.appendChild(formDiv);
                chunkCount++;
                updateChunkStatus(`✅ Form component #${chunkCount} added to DOM`);
            }

            async function addTableComponent() {
                updateChunkStatus('🔄 Adding table component - expect data-table-*.js chunk');
                const container = document.getElementById('dynamic-components');
                const tableDiv = document.createElement('div');
                tableDiv.style.cssText =
                    'margin: 20px 0; padding: 15px; border: 2px solid #2196F3; border-radius: 5px;';
                tableDiv.innerHTML = `
                    <h3>📊 Data Table Component</h3>
                    <zwe-angelos-data-table-v3
                        entity-id="test-entity"
                        tenant-id="0"
                    ></zwe-angelos-data-table-v3>
                `;
                container.appendChild(tableDiv);
                chunkCount++;
                updateChunkStatus(`✅ Table component #${chunkCount} added to DOM`);
            }

            async function addDetailsComponent() {
                updateChunkStatus('🔄 Adding details component - expect details-view-*.js chunk');
                const container = document.getElementById('dynamic-components');
                const detailsDiv = document.createElement('div');
                detailsDiv.style.cssText =
                    'margin: 20px 0; padding: 15px; border: 2px solid #FF9800; border-radius: 5px;';
                detailsDiv.innerHTML = `
                    <h3>👁️ Details View Component</h3>
                    <zwe-angelos-details-view-v3
                        entity-id="test-entity"
                        tenant-id="0"
                    ></zwe-angelos-details-view-v3>
                `;
                container.appendChild(detailsDiv);
                chunkCount++;
                updateChunkStatus(`✅ Details component #${chunkCount} added to DOM`);
            }

            function clearComponents() {
                document.getElementById('dynamic-components').innerHTML = '';
                document.getElementById('chunk-loading-status').innerHTML = '';
                chunkCount = 0;
                updateChunkStatus('🗑️ Components cleared - ready for fresh test');
            }
        </script>
    </body>
</html>
