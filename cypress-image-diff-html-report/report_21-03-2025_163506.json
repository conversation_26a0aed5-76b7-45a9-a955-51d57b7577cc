{"total": 6, "totalPassed": 6, "totalFailed": 0, "totalSuites": 4, "suites": [{"name": "basic-custom-comp.cy.js", "path": "cypress/e2e/form/basic-custom-comp.cy.js", "tests": [{"status": "pass", "name": "basic-custom-comp.cy-form-basic", "percentage": 0, "failureThreshold": 0, "specPath": "cypress/e2e/form/basic-custom-comp.cy.js", "specFilename": "basic-custom-comp.cy.js", "baselinePath": "cypress-image-diff-screenshots/baseline/basic-custom-comp.cy-form-basic.png", "diffPath": "", "comparisonPath": "cypress-image-diff-screenshots/comparison/basic-custom-comp.cy-form-basic.png"}, {"status": "pass", "name": "basic-custom-comp.cy-search-clicked", "percentage": 0, "failureThreshold": 0, "specPath": "cypress/e2e/form/basic-custom-comp.cy.js", "specFilename": "basic-custom-comp.cy.js", "baselinePath": "cypress-image-diff-screenshots/baseline/basic-custom-comp.cy-search-clicked.png", "diffPath": "", "comparisonPath": "cypress-image-diff-screenshots/comparison/basic-custom-comp.cy-search-clicked.png"}]}, {"name": "basic.cy.js", "path": "cypress/e2e/form/basic.cy.js", "tests": [{"status": "pass", "name": "basic.cy-form-basic", "percentage": 0, "failureThreshold": 0, "specPath": "cypress/e2e/form/basic.cy.js", "specFilename": "basic.cy.js", "baselinePath": "cypress-image-diff-screenshots/baseline/basic.cy-form-basic.png", "diffPath": "", "comparisonPath": "cypress-image-diff-screenshots/comparison/basic.cy-form-basic.png"}]}, {"name": "conditional-field.cy.js", "path": "cypress/e2e/form/conditional-field.cy.js", "tests": [{"status": "pass", "name": "conditional-field.cy-hidden-field", "percentage": 0, "failureThreshold": 0, "specPath": "cypress/e2e/form/conditional-field.cy.js", "specFilename": "conditional-field.cy.js", "baselinePath": "cypress-image-diff-screenshots/baseline/conditional-field.cy-hidden-field.png", "diffPath": "", "comparisonPath": "cypress-image-diff-screenshots/comparison/conditional-field.cy-hidden-field.png"}, {"status": "pass", "name": "conditional-field.cy-visible-field", "percentage": 0, "failureThreshold": 0, "specPath": "cypress/e2e/form/conditional-field.cy.js", "specFilename": "conditional-field.cy.js", "baselinePath": "cypress-image-diff-screenshots/baseline/conditional-field.cy-visible-field.png", "diffPath": "", "comparisonPath": "cypress-image-diff-screenshots/comparison/conditional-field.cy-visible-field.png"}]}, {"name": "datepicker-no-app-timezone.cy.js", "path": "cypress/e2e/form/datepicker-no-app-timezone.cy.js", "tests": [{"status": "pass", "name": "datepicker-no-app-timezone.cy-no-offset", "percentage": 0, "failureThreshold": 0, "specPath": "cypress/e2e/form/datepicker-no-app-timezone.cy.js", "specFilename": "datepicker-no-app-timezone.cy.js", "baselinePath": "cypress-image-diff-screenshots/baseline/datepicker-no-app-timezone.cy-no-offset.png", "diffPath": "", "comparisonPath": "cypress-image-diff-screenshots/comparison/datepicker-no-app-timezone.cy-no-offset.png"}]}], "startedAt": "2025-03-21T11:05:06.272Z", "endedAt": "2025-03-21T11:06:24.144Z", "duration": 60018, "browserName": "chrome", "browserVersion": "131.0.6778.140", "cypressVersion": "13.17.0"}