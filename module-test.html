<!DOCTYPE html><html lang="en"><head>    <meta charset="UTF-8">    <meta name="viewport" content="width=device-width, initial-scale=1.0">    <title>🔧 Module Loading Test</title>    <style>        body {            font-family: Arial, sans-serif;            max-width: 800px;            margin: 0 auto;            padding: 20px;            background: #f5f5f5;        }        .container {            background: white;            padding: 20px;            border-radius: 8px;            box-shadow: 0 2px 4px rgba(0,0,0,0.1);        }        .log {            background: #1e1e1e;            color: #d4d4d4;            border: 1px solid #444;            padding: 15px;            font-family: 'Courier New', monospace;            font-size: 12px;            white-space: pre-wrap;            max-height: 400px;            overflow-y: auto;            margin: 10px 0;            border-radius: 4px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Module Loading Diagnostic Test</h1>
        <p>Testing if the Angelos SDK loads correctly as an ES module...</p>

        <button onclick="testModuleLoad()">Test Module Loading</button>
        <button onclick="clearLog()">Clear Log</button>

        <div id="log" class="log">🎬 Module loading test initialized...\n</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logEl.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '🧹 Log cleared...\n';
        }

        async function testModuleLoad() {
            try {
                log('🔄 Testing ES module import...', 'info');
                
                const cacheBuster = Date.now();
                log(`📦 Cache buster: ${cacheBuster}`, 'info');
                
                // Try to import the module
                const module = await import(`./dist/angelos.min.js?cb=${cacheBuster}`);
                log('✅ ES module imported successfully!', 'success');
                
                // Check what's in the module
                log(`📋 Module keys: ${Object.keys(module).join(', ')}`, 'info');
                
                // Wait for AngelosSDK to be available on window
                let attempts = 0;
                while (attempts < 50) {
                    if (window.AngelosSDK) {
                        log('✅ window.AngelosSDK is available!', 'success');
                        log(`🔧 SDK keys: ${Object.keys(window.AngelosSDK).join(', ')}`, 'info');
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                if (!window.AngelosSDK) {
                    log('❌ window.AngelosSDK not available after 5 seconds', 'error');
                } else {
                    log('🎉 Module loading test PASSED!', 'success');
                }
                
            } catch (error) {
                log(`❌ Module loading failed: ${error.message}`, 'error');
                console.error('Module loading error:', error);
            }
        }

        log('🎯 Ready to test module loading!', 'info');
    </script>
</body>
</html>
