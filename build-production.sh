#!/bin/bash

# Angelos SDK Production Build Script
echo "🚀 Building Angelos SDK for production..."

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist/*

# Build the SDK
echo "🔨 Building SDK with Vite..."
npm run build

# Copy mock data for testing
echo "📦 Copying mock data..."
cp -r mocks dist/

# Copy any additional assets
echo "📁 Copying additional assets..."
if [ -d "public" ]; then
    cp -r public/* dist/ 2>/dev/null || true
fi

# Verify build
echo "✅ Build verification..."
if [ -f "dist/angelos.min.js" ]; then
    echo "✅ Main SDK file: dist/angelos.min.js"
    
    # Check file size
    SIZE=$(du -h dist/angelos.min.js | cut -f1)
    echo "📊 SDK size: $SIZE"
    
    # Check if it's a valid ES module
    if head -c 100 dist/angelos.min.js | grep -q "import\|export\|const.*=.*import\|__vite"; then
        echo "✅ Valid ES module detected"
    else
        echo "⚠️  ES module structure not detected in first 100 chars"
    fi
else
    echo "❌ Build failed: angelos.min.js not found"
    exit 1
fi

if [ -d "dist/assets" ]; then
    ASSET_COUNT=$(ls -1 dist/assets | wc -l)
    echo "✅ Assets directory: $ASSET_COUNT files"
else
    echo "⚠️  No assets directory found"
fi

if [ -d "dist/mocks" ]; then
    echo "✅ Mock data copied"
else
    echo "⚠️  Mock data not found"
fi

echo ""
echo "🎯 Build completed successfully!"
echo "📂 Output directory: ./dist/"
echo "🌐 To test: python -m http.server 8080 --directory dist"
echo "🔗 Test URLs:"
echo "   - http://localhost:8080/final-test.html (comprehensive test)"
echo "   - http://localhost:8080/manual-test.html (manual testing)"
echo ""
echo "📋 SDK Usage:"
echo "   <script type=\"module\" src=\"./angelos.min.js\"></script>"
echo "   window.AngelosSDK.loadAngelosComponent(containerId, options)"
