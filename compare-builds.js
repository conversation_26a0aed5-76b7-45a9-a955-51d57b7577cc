#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('📊 Angelos SDK - Build Optimization Comparison\n');

// Helper function to format file sizes
function formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Load baseline metrics
let baseline = null;
try {
    baseline = JSON.parse(fs.readFileSync('baseline-metrics.json', 'utf8'));
} catch (error) {
    console.error('❌ Could not load baseline metrics. Run analyze-chunks.js first.');
    process.exit(1);
}

// Analyze current build
const distDir = path.join(__dirname, 'dist');
const assetsDir = path.join(distDir, 'assets');

const mainBundle = path.join(distDir, 'angelos.min.js');
let currentSize = 0;
let componentChunks = 0;
let monacoChunks = 0;
let totalChunks = 0;

if (fs.existsSync(mainBundle)) {
    currentSize = fs.statSync(mainBundle).size;
}

// Count chunks
if (fs.existsSync(assetsDir)) {
    const files = fs.readdirSync(assetsDir);
    
    files.forEach(file => {
        if (file.endsWith('.js')) {
            totalChunks++;
            
            if (file.includes('component-')) {
                componentChunks++;
            } else if (file.includes('monaco-')) {
                monacoChunks++;
            }
        }
    });
}

// Calculate improvements
const sizeReduction = baseline.totalSize - currentSize;
const percentReduction = ((sizeReduction / baseline.totalSize) * 100);

console.log('🎯 OPTIMIZATION RESULTS:');
console.log('=' .repeat(50));
console.log(`📦 Before: ${formatSize(baseline.totalSize)} (${baseline.files.monacoLanguages + 1} files)`);
console.log(`📦 After:  ${formatSize(currentSize)} (${totalChunks + 1} files)`);
console.log(`📉 Reduction: ${formatSize(sizeReduction)} (${percentReduction.toFixed(1)}%)`);

console.log('\n🧩 CHUNK ANALYSIS:');
console.log('=' .repeat(50));
console.log(`📝 Component Chunks: ${componentChunks}`);
console.log(`🎨 Monaco Chunks: ${monacoChunks}`);
console.log(`📚 Other Chunks: ${totalChunks - componentChunks - monacoChunks}`);
console.log(`📊 Total Chunks: ${totalChunks}`);

console.log('\n⚡ PERFORMANCE IMPROVEMENTS:');
console.log('=' .repeat(50));

// Initial load comparison
const initialLoadBefore = baseline.coreSize;
const initialLoadAfter = currentSize;
const initialLoadReduction = ((initialLoadBefore - initialLoadAfter) / initialLoadBefore * 100);

console.log(`🚀 Initial Load:`);
console.log(`   Before: ${formatSize(initialLoadBefore)}`);
console.log(`   After:  ${formatSize(initialLoadAfter)}`);
console.log(`   Improvement: ${initialLoadReduction.toFixed(1)}% smaller`);

// Monaco optimization
const monacoBefore = baseline.monacoSize;
const monacoAfter = 0; // Monaco is now lazy-loaded
console.log(`\n🎨 Monaco Editor:`);
console.log(`   Before: ${formatSize(monacoBefore)} (loaded immediately)`);
console.log(`   After:  Lazy-loaded on demand`);
console.log(`   Improvement: 100% deferred from initial load`);

// Component loading
console.log(`\n📦 Component Loading:`);
console.log(`   Before: All components loaded immediately`);
console.log(`   After:  ${componentChunks} component chunks load on-demand`);
console.log(`   Improvement: Components load only when needed`);

console.log('\n🎯 LAZY LOADING BENEFITS:');
console.log('=' .repeat(50));
console.log('✅ Faster initial page load');
console.log('✅ Reduced memory usage');
console.log('✅ Better caching strategy');
console.log('✅ Improved user experience');
console.log('✅ Bandwidth savings for unused features');

console.log('\n📋 TESTING INSTRUCTIONS:');
console.log('=' .repeat(50));
console.log('1. 🌐 Start test server:');
console.log('   node serve-prod.js');
console.log('');
console.log('2. 🧪 Open test page:');
console.log('   http://localhost:8080/test-optimized.html');
console.log('');
console.log('3. 🔍 Monitor Network tab:');
console.log('   - Initial load: Only main bundle');
console.log('   - Click buttons: Watch chunks load on-demand');
console.log('   - Monaco: Loads only when code editor is used');
console.log('');
console.log('4. 📊 Compare performance:');
console.log('   - Measure initial load time');
console.log('   - Check memory usage');
console.log('   - Test component loading speed');

console.log('\n🎉 OPTIMIZATION SUCCESS:');
console.log('=' .repeat(50));
console.log(`🚀 ${percentReduction.toFixed(1)}% reduction in initial bundle size`);
console.log(`⚡ ${componentChunks} components now load on-demand`);
console.log(`🎨 Monaco Editor fully lazy-loaded`);
console.log(`📦 ${totalChunks} optimized chunks for better caching`);

// Save comparison data
const comparisonData = {
    timestamp: new Date().toISOString(),
    baseline: {
        totalSize: baseline.totalSize,
        coreSize: baseline.coreSize,
        monacoSize: baseline.monacoSize,
        files: baseline.files
    },
    optimized: {
        totalSize: currentSize,
        componentChunks,
        monacoChunks,
        totalChunks
    },
    improvements: {
        sizeReduction,
        percentReduction,
        initialLoadReduction
    }
};

fs.writeFileSync('optimization-results.json', JSON.stringify(comparisonData, null, 2));
console.log('\n💾 Comparison data saved to optimization-results.json');
