<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link
            rel="stylesheet"
            href="https://hercules-assets.mum1-pp.zetaapps.in/common-assets/3.0.144/fonts/ibmplex.min.css"
        />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans" />
        <title>Angelos SDK - Production Build Test</title>

        <!-- Use the production build instead of dev source -->
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
        </script>
        <!-- Remove problematic setContextAndParams.js for production test -->
        <!-- <script type="module" src="./mocks/setContextAndParams.js"></script> -->
    </head>
    <body>
        <div id="app">
            <h1>🚀 Angelos SDK - Production Build Test</h1>
            
            <div style="margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 5px">
                <h3>🎛️ Component Testing</h3>
                <button onclick="addFormComponent()">Add Create Form</button>
                <button onclick="addTableComponent()">Add Data Table</button>
                <button onclick="addDetailsComponent()">Add Details View</button>
                <button onclick="clearComponents()">Clear All</button>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: #fffacd; border-radius: 5px">
                <h3>📊 Network Monitoring</h3>
                <p><strong>Open DevTools → Network tab to see chunks loading!</strong></p>
                <ul>
                    <li>📦 <strong>Initial load:</strong> Only angelos.min.js (~85KB)</li>
                    <li>🔄 <strong>Forms:</strong> Watch for forms-*.js chunk when adding form</li>
                    <li>📋 <strong>Tables:</strong> Watch for data-table-*.js chunk when adding table</li>
                    <li>👁️ <strong>Details:</strong> Watch for details-view-*.js chunk when adding details</li>
                    <li>⚡ <strong>Monaco:</strong> Watch for monaco-editor-*.js chunk when code editor loads</li>
                </ul>
            </div>

            <div style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 5px">
                <h3>🔍 Chunk Loading Info</h3>
                <div id="chunk-info">
                    <p>Loading status will appear here...</p>
                </div>
            </div>

            <!-- Container for dynamically added components -->
            <div id="dynamic-components"></div>
        </div>

        <script>
            // Track network requests to show chunk loading
            const originalFetch = window.fetch;
            const loadedChunks = new Set();
            
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string' && url.includes('.js')) {
                    const chunkName = url.split('/').pop();
                    if (!loadedChunks.has(chunkName)) {
                        loadedChunks.add(chunkName);
                        updateChunkInfo(`📦 Loading chunk: ${chunkName}`);
                    }
                }
                return originalFetch.apply(this, args);
            };

            // Monitor dynamic imports
            const originalImport = window.__vitePreload || (() => {});
            
            function updateChunkInfo(message) {
                const infoDiv = document.getElementById('chunk-info');
                const timestamp = new Date().toLocaleTimeString();
                infoDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                console.log(`🔍 ${message}`);
            }

            // Wait for SDK to initialize
            function waitForSDK() {
                return new Promise((resolve) => {
                    if (window.AngelosSDK) {
                        resolve(window.AngelosSDK);
                        return;
                    }
                    
                    const checkSDK = () => {
                        if (window.AngelosSDK) {
                            updateChunkInfo('✅ Angelos SDK initialized');
                            resolve(window.AngelosSDK);
                        } else {
                            setTimeout(checkSDK, 100);
                        }
                    };
                    checkSDK();
                });
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', async () => {
                updateChunkInfo('🚀 DOM loaded, waiting for SDK...');
                const sdk = await waitForSDK();
                updateChunkInfo('🎉 SDK ready for component testing');
            });

            async function addFormComponent() {
                updateChunkInfo('🔄 Adding form component...');
                const container = document.getElementById('dynamic-components');
                const formDiv = document.createElement('div');
                formDiv.style.cssText =
                    'margin: 20px 0; padding: 15px; border: 2px solid #4CAF50; border-radius: 5px;';
                formDiv.innerHTML = `
                    <h3>📝 Create Form Component</h3>
                    <zwe-angelos-create-form-v3
                        entity-id="test-entity"
                        tenant-id="0"
                        show-form-actions="true"
                    ></zwe-angelos-create-form-v3>
                `;
                container.appendChild(formDiv);
                updateChunkInfo('✅ Form component added to DOM');
            }

            async function addTableComponent() {
                updateChunkInfo('🔄 Adding table component...');
                const container = document.getElementById('dynamic-components');
                const tableDiv = document.createElement('div');
                tableDiv.style.cssText =
                    'margin: 20px 0; padding: 15px; border: 2px solid #2196F3; border-radius: 5px;';
                tableDiv.innerHTML = `
                    <h3>📊 Data Table Component</h3>
                    <zwe-angelos-data-table-v3
                        entity-id="test-entity"
                        tenant-id="0"
                    ></zwe-angelos-data-table-v3>
                `;
                container.appendChild(tableDiv);
                updateChunkInfo('✅ Table component added to DOM');
            }

            async function addDetailsComponent() {
                updateChunkInfo('🔄 Adding details component...');
                const container = document.getElementById('dynamic-components');
                const detailsDiv = document.createElement('div');
                detailsDiv.style.cssText =
                    'margin: 20px 0; padding: 15px; border: 2px solid #FF9800; border-radius: 5px;';
                detailsDiv.innerHTML = `
                    <h3>👁️ Details View Component</h3>
                    <zwe-angelos-details-view-v3
                        entity-id="test-entity"
                        tenant-id="0"
                    ></zwe-angelos-details-view-v3>
                `;
                container.appendChild(detailsDiv);
                updateChunkInfo('✅ Details component added to DOM');
            }

            function clearComponents() {
                document.getElementById('dynamic-components').innerHTML = '';
                updateChunkInfo('🗑️ Components cleared');
            }
        </script>
    </body>
</html>
