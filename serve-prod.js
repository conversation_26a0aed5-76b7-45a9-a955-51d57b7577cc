#!/usr/bin/env node

const express = require('express');
const path = require('path');
const compression = require('compression');

const app = express();
const PORT = process.env.PORT || 8080;

// Enable gzip compression
app.use(compression());

// Serve static files from dist directory
app.use(
    '/dist',
    express.static(path.join(__dirname, 'dist'), {
        maxAge: '1y', // Cache chunks for 1 year
        etag: true,
        lastModified: true
    })
);

// Serve test files
app.use(
    express.static(__dirname, {
        maxAge: '1h' // Cache test files for 1 hour
    })
);

// CORS headers for development
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header(
        'Access-Control-Allow-Headers',
        'Origin, X-Requested-With, Content-Type, Accept, Authorization'
    );
    next();
});

// Serve the main index.html at root
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Serve the test page at /test
app.get('/test', (req, res) => {
    res.sendFile(path.join(__dirname, 'test-optimized.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        port: PORT
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Angelos SDK Production Test Server');
    console.log('='.repeat(50));
    console.log(`📡 Server running at: http://localhost:${PORT}`);
    console.log(`🧪 Test page: http://localhost:${PORT}/test-optimized.html`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log('');
    console.log('📋 Testing Instructions:');
    console.log('1. Open the test page in your browser');
    console.log('2. Open DevTools → Network tab');
    console.log('3. Click component buttons to test lazy loading');
    console.log('4. Watch chunks load on-demand!');
    console.log('');
    console.log('Press Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});
