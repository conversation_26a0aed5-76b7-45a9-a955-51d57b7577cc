#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080;

// MIME type mapping
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.ttf': 'font/ttf',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2'
};

function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // Default to clean prod test for root
    if (pathname === '/') {
        pathname = '/prod-test-clean.html';
    }
    
    // Handle favicon requests
    if (pathname === '/favicon.ico') {
        pathname = '/dist/favicon.ico';
    }
    
    // Remove leading slash and resolve file path
    const filePath = path.join(__dirname, pathname.substring(1));
    
    console.log(`📡 ${req.method} ${pathname} → ${filePath}`);
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            console.error(`❌ Error serving ${pathname}:`, err.message);
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <h1>404 - File Not Found</h1>
                <p>Could not find: ${pathname}</p>
                <p>Tried: ${filePath}</p>
                <a href="/">← Back to home</a>
            `);
            return;
        }
        
        const contentType = getContentType(filePath);
        res.writeHead(200, { 
            'Content-Type': contentType,
            'Access-Control-Allow-Origin': '*',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        });
        res.end(data);
        console.log(`✅ Served ${pathname} (${contentType})`);
    });
});

server.listen(PORT, () => {
    console.log(`🚀 Production test server running at:`);
    console.log(`   http://localhost:${PORT}                     (Clean test)`);
    console.log(`   http://localhost:${PORT}/debug-test.html     (Debug test)`);
    console.log(`   http://localhost:${PORT}/fixed-test.html     (Fixed test with cache-busting)`);
    console.log(`   http://localhost:${PORT}/prod-test.html      (Original test)`);
    console.log(``);
    console.log(`📋 To test chunk loading:`);
    console.log(`   1. Open DevTools → Network tab`);
    console.log(`   2. Clear network log`);
    console.log(`   3. Click component buttons to see chunks load`);
    console.log(``);
    console.log(`🐛 To debug component issues:`);
    console.log(`   1. Use debug-test.html`);
    console.log(`   2. Click "Debug Components" button`);
    console.log(`   3. Check console and debug info panel`);
    console.log(``);
    console.log(`Press Ctrl+C to stop`);
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use. Try a different port.`);
    } else {
        console.error('❌ Server error:', err);
    }
});
