<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="./dist/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Web Component Debug Test</title>
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            // Minimal config
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            localStorage.setItem('AT', 'test-token');
            
            // Comprehensive debugging
            let logs = [];
            function log(message, level = 'info') {
                const timestamp = new Date().toISOString();
                const logEntry = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
                logs.push(logEntry);
                
                // Update UI
                const logDiv = document.getElementById('debug-output');
                if (logDiv) {
                    logDiv.innerHTML = logs.slice(-50).map(l => `<div class="${level}">${l}</div>`).join('');
                    logDiv.scrollTop = logDiv.scrollHeight;
                }
                
                // Also log to console
                console[level === 'error' ? 'error' : 'log'](logEntry);
            }
            
            // Monitor network requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                log(`🌐 FETCH: ${args[0]}`);
                return originalFetch.apply(this, args)
                    .then(response => {
                        log(`✅ FETCH SUCCESS: ${args[0]} - ${response.status}`);
                        return response;
                    })
                    .catch(error => {
                        log(`❌ FETCH ERROR: ${args[0]} - ${error.message}`, 'error');
                        throw error;
                    });
            };
            
            // Monitor dynamic imports
            const originalImport = window.__vitePreload || function() {};
            if (window.__vitePreload) {
                window.__vitePreload = function(...args) {
                    log(`📦 DYNAMIC IMPORT: ${JSON.stringify(args)}`);
                    return originalImport.apply(this, args)
                        .then(result => {
                            log(`✅ IMPORT SUCCESS: ${JSON.stringify(args)}`);
                            return result;
                        })
                        .catch(error => {
                            log(`❌ IMPORT ERROR: ${JSON.stringify(args)} - ${error.message}`, 'error');
                            throw error;
                        });
                };
            }
            
            // Override console methods to capture all logs
            ['log', 'warn', 'error', 'info'].forEach(method => {
                const original = console[method];
                console[method] = function(...args) {
                    if (args[0] && typeof args[0] === 'string') {
                        if (args[0].includes('🚀') || args[0].includes('✅') || args[0].includes('🔄') || 
                            args[0].includes('⏳') || args[0].includes('❌') || args[0].includes('⚠️')) {
                            log(args.join(' '), method === 'error' ? 'error' : 'info');
                        }
                    }
                    original.apply(console, args);
                };
            });
            
            // Listen for all custom events
            ['angelos:component-loaded', 'angelos:component-error'].forEach(eventName => {
                window.addEventListener(eventName, (e) => {
                    log(`🎯 EVENT: ${eventName} - ${JSON.stringify(e.detail)}`);
                });
            });
            
            // Monitor custom elements
            const originalDefine = customElements.define;
            customElements.define = function(name, constructor, options) {
                log(`🏷️ CUSTOM ELEMENT DEFINED: ${name}`);
                return originalDefine.call(this, name, constructor, options);
            };
            
            const originalUpgrade = customElements.upgrade;
            customElements.upgrade = function(element) {
                log(`⬆️ CUSTOM ELEMENT UPGRADE: ${element.tagName}`);
                return originalUpgrade.call(this, element);
            };
        </script>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
            .debug-output { height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; font-family: monospace; font-size: 12px; }
            .debug-output .error { color: red; }
            .debug-output .info { color: blue; }
            .component-area { border: 2px solid #4CAF50; padding: 20px; min-height: 200px; }
            button { padding: 10px 20px; margin: 5px; font-size: 16px; cursor: pointer; }
            .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
        </style>
    </head>
    <body>
        <h1>🔍 Web Component Loading Debug</h1>
        
        <div style="margin: 20px 0;">
            <button onclick="addComponent()">➕ Add Form Component</button>
            <button onclick="checkSDK()">🔍 Check SDK</button>
            <button onclick="checkCustomElements()">🏷️ Check Custom Elements</button>
            <button onclick="forceLoad()">⚡ Force Load</button>
            <button onclick="inspectDOM()">🔍 Inspect DOM</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
        
        <div class="status" id="status">Initializing...</div>
        
        <div class="container">
            <div>
                <h3>📝 Component Test Area</h3>
                <div class="component-area" id="component-container">
                    <p style="color: #666;">Click "Add Form Component" to test...</p>
                </div>
                <div style="margin-top: 10px; font-size: 14px;">
                    <strong>Component Status:</strong>
                    <div id="component-status">No component added</div>
                </div>
            </div>
            
            <div>
                <h3>📊 Debug Output</h3>
                <div class="debug-output" id="debug-output">
                    Waiting for initialization...
                </div>
            </div>
        </div>

        <script>
            let componentCounter = 0;
            
            function updateStatus(message) {
                document.getElementById('status').textContent = message;
                log(`📊 STATUS: ${message}`);
            }
            
            function addComponent() {
                componentCounter++;
                const container = document.getElementById('component-container');
                
                log(`🆕 Creating component #${componentCounter}`);
                
                // Create the custom element
                const element = document.createElement('zwe-angelos-create-form-v3');
                element.setAttribute('entity-id', 'test-entity');
                element.setAttribute('tenant-id', '0');
                element.setAttribute('show-form-actions', 'true');
                element.id = `test-form-${componentCounter}`;
                
                log(`🏗️ Element created: ${element.tagName} with id ${element.id}`);
                
                // Add to DOM
                container.appendChild(element);
                log(`✅ Element added to DOM`);
                
                // Check immediate status
                setTimeout(() => {
                    checkElementStatus(element);
                }, 100);
                
                updateComponentStatus();
            }
            
            function checkElementStatus(element) {
                log(`🔍 Checking element status:`);
                log(`   - Tag: ${element.tagName}`);
                log(`   - Constructor: ${element.constructor.name}`);
                log(`   - Connected: ${element.isConnected}`);
                log(`   - Children: ${element.children.length}`);
                log(`   - InnerHTML length: ${element.innerHTML.length}`);
                log(`   - Custom element defined: ${customElements.get('zwe-angelos-create-form-v3') ? 'YES' : 'NO'}`);
                
                if (element.innerHTML.trim() === '') {
                    log(`⚠️ Element is empty - component not rendered`, 'error');
                } else {
                    log(`✅ Element has content`);
                }
            }
            
            function checkSDK() {
                log(`🔍 Checking Angelos SDK:`);
                if (window.AngelosSDK) {
                    log(`✅ SDK available`);
                    const stats = window.AngelosSDK.getStats();
                    log(`📊 Stats: ${JSON.stringify(stats, null, 2)}`);
                } else {
                    log(`❌ SDK not available`, 'error');
                }
            }
            
            function checkCustomElements() {
                log(`🏷️ Checking custom elements:`);
                const elements = ['zwe-angelos-create-form-v3', 'zwe-angelos-update-form-v3', 'zwe-angelos-data-table-v3'];
                elements.forEach(name => {
                    const defined = customElements.get(name);
                    log(`   - ${name}: ${defined ? 'DEFINED' : 'NOT DEFINED'}`);
                });
            }
            
            function forceLoad() {
                if (window.AngelosSDK && window.AngelosSDK.debug) {
                    log(`⚡ Forcing load of form component...`);
                    window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3')
                        .then(() => {
                            log(`✅ Force load completed`);
                            setTimeout(() => {
                                const element = document.querySelector('zwe-angelos-create-form-v3');
                                if (element) checkElementStatus(element);
                            }, 500);
                        })
                        .catch(error => {
                            log(`❌ Force load failed: ${error.message}`, 'error');
                        });
                } else {
                    log(`❌ SDK debug methods not available`, 'error');
                }
            }
            
            function inspectDOM() {
                log(`🔍 DOM Inspection:`);
                const components = document.querySelectorAll('zwe-angelos-create-form-v3');
                log(`   - Found ${components.length} form components`);
                
                components.forEach((comp, index) => {
                    log(`   - Component ${index + 1}:`);
                    log(`     * Constructor: ${comp.constructor.name}`);
                    log(`     * Children: ${comp.children.length}`);
                    log(`     * InnerHTML: ${comp.innerHTML.length} chars`);
                });
            }
            
            function updateComponentStatus() {
                const components = document.querySelectorAll('zwe-angelos-create-form-v3');
                const statusDiv = document.getElementById('component-status');
                
                if (components.length === 0) {
                    statusDiv.innerHTML = 'No components found';
                } else {
                    const component = components[0];
                    statusDiv.innerHTML = `
                        <strong>Count:</strong> ${components.length}<br>
                        <strong>Constructor:</strong> ${component.constructor.name}<br>
                        <strong>Children:</strong> ${component.children.length}<br>
                        <strong>Content:</strong> ${component.innerHTML.length} chars<br>
                        <strong>Defined:</strong> ${customElements.get('zwe-angelos-create-form-v3') ? 'Yes' : 'No'}
                    `;
                }
            }
            
            function clearLogs() {
                logs = [];
                document.getElementById('debug-output').innerHTML = 'Logs cleared...';
            }
            
            // Initialize
            document.addEventListener('DOMContentLoaded', () => {
                log(`📄 DOM loaded`);
                updateStatus('DOM ready, waiting for SDK...');
                
                function waitForSDK() {
                    if (window.AngelosSDK) {
                        log(`🎉 Angelos SDK detected`);
                        updateStatus('SDK ready');
                        checkSDK();
                        checkCustomElements();
                    } else {
                        setTimeout(waitForSDK, 100);
                    }
                }
                
                waitForSDK();
            });
        </script>
    </body>
</html>
