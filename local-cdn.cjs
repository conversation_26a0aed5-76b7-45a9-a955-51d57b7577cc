const express = require('express');
const path = require('path');
const net = require('net');
const fs = require('fs');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));

const app = express();

// Default port or from environment
const DEFAULT_PORT = 3000;
const MAX_PORT_ATTEMPTS = 10;
const PORT = process.env.PORT || DEFAULT_PORT;
const VERSION = packageJson.version;

// Function to check if port is available
const isPortAvailable = (port) => {
    return new Promise((resolve) => {
        const server = net.createServer();

        server.once('error', () => {
            resolve(false);
        });

        server.once('listening', () => {
            server.close();
            resolve(true);
        });

        server.listen(port);
    });
};

// Function to find available port
const findAvailablePort = async (startPort) => {
    for (let port = startPort; port < startPort + MAX_PORT_ATTEMPTS; port++) {
        if (await isPortAvailable(port)) {
            return port;
        }
    }
    throw new Error(
        `No available ports found between ${startPort} and ${startPort + MAX_PORT_ATTEMPTS - 1}`
    );
};

// Enable CORS for all routes
app.use((req, res, next) => {
    console.log('\x1b[36m%s\x1b[0m', `Request received: ${req.method} ${req.url}`);
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
});

// Serve static files from dist directory
app.use(express.static('dist'));

// Serve all SDK files with version
app.get(`/angelos-sdk/${VERSION}/:fileName`, (req, res) => {
    const fileName = req.params.fileName;
    const filePath = path.join(__dirname, 'dist', fileName);

    res.sendFile(filePath, (err) => {
        if (err) {
            console.error('\x1b[31m%s\x1b[0m', `Error serving file ${fileName}: ${err.message}`);
            res.status(404).send(`File ${fileName} not found`);
        } else {
            console.log('\x1b[32m%s\x1b[0m', `Served file: ${fileName}`);
        }
    });
});

// Start server with port fallback
const startServer = async () => {
    try {
        const availablePort = await findAvailablePort(PORT);
        app.listen(availablePort, () => {
            console.log('\x1b[32m%s\x1b[0m', `Server running at http://localhost:${availablePort}`);
            console.log(
                '\x1b[36m%s\x1b[0m',
                `SDK available at: http://localhost:${availablePort}/angelos-sdk/${VERSION}/angelos.min.js`
            );
        });
    } catch (error) {
        console.error('\x1b[31m%s\x1b[0m', `Error starting server: ${error.message}`);
        process.exit(1);
    }
};

// Handle server errors
process.on('uncaughtException', (error) => {
    if (error.code === 'EADDRINUSE') {
        console.error('\x1b[31m%s\x1b[0m', `Port ${PORT} is in use, trying alternative port...`);
    } else {
        console.error('\x1b[31m%s\x1b[0m', `Fatal error: ${error.message}`);
        process.exit(1);
    }
});

startServer();