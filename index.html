<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link
            rel="stylesheet"
            href="https://hercules-assets.mum1-pp.zetaapps.in/common-assets/3.0.144/fonts/ibmplex.min.css"
        />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans" />
        <title>Vite App</title>

        <script type="module" src="/src/main.ts"></script>
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
        </script>
        <script type="module" src="./mocks/setContextAndParams.js"></script>
    </head>
    <body>
        <div id="app">
            <button id="externalValidate">External Validate</button>
            <button id="externalReport">External Report Validate</button>
            <button id="externalSubmit">External Submit</button>
            <button id="externalReset">External Reset</button>
            <!-- <zwe-angelos-create-form-v3
                id="angelosEntityComponent"
                entity-id="angelos-testing-entity"
                tenant-id="0"
                show-form-actions="true"
            ></zwe-angelos-create-form-v3> -->
            <zwe-angelos-update-form-v3
                entity-id="rulemanager"
                tenant-id="0"
                show-form-actions="true"
                params='{
                    "baseUrl": "https://sb1-god-prepaidcore.mum1-pp.zetaapps.in/osmi/osmium/cards",
                    "tenantId": 600309,
                    "optumRuleSchemaId": "rule-schema-id-2-chess"
                  }'
                context='{
                    "ruleId": "Rule-pp168",
                    "name": "rule name test",
                    "optumRuleSchemaId": "rule schema test id",
                    "riskScore": 120,
                    "maximumAmountUSD": 200,
                    "allowedMCCs": "1234, 1234",
                    "disallowedMCCs": "1234, 2131",
                    "allowedMIDs": "1234, 2131",
                    "disallowedMIDs": "1234, 2131",
                    "createdAt": 1751902553482,
                    "updatedAt": 1751902553482
                  }'
            >
            </zwe-angelos-update-form-v3>
            <!-- <zwe-angelos-update-form
            id="angelosUpdateForm"
            entity-id="test-entity"
            tenant-id="0"
            display-form-actions="false"
            app-timezone="Europe/Berlin"
        ></zwe-angelos-update-form> -->
        </div>
    </body>
</html>
