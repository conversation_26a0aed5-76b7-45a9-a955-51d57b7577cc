<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link
            rel="stylesheet"
            href="https://hercules-assets.mum1-pp.zetaapps.in/common-assets/3.0.144/fonts/ibmplex.min.css"
        />
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans" />
        <title>Vite App</title>

        <!-- <script type="module" src="./src/main.ts"></script> -->
        <script type="module" src="./dist/angelos.min.js"></script>
        <script>
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'http://localhost:4000',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            window.herculesCDNPath = 'https://hercules-assets.mum1-pp.zetaapps.in';
        </script>
        <script type="module" src="./mocks/setContextAndParams.js"></script>

        <script>
            // Debug functions for lazy loading
            function checkSDKStatus() {
                console.log('🔍 Checking SDK Status...');
                if (window.AngelosSDK) {
                    const stats = window.AngelosSDK.getStats();
                    console.log('✅ AngelosSDK available:', stats);
                    alert(
                        `SDK Status: Ready\nComponents loaded: ${stats.loadedComponents}/${stats.totalComponents}\nLoading attempts: ${stats.loadingAttempts}`
                    );
                } else {
                    console.log('❌ AngelosSDK not available');
                    alert('SDK Status: Not Ready');
                }
            }

            function checkCustomElements() {
                console.log('🔍 Checking Custom Elements...');
                const components = [
                    'zwe-angelos-create-form-v3',
                    'zwe-angelos-data-table-v3',
                    'zwe-angelos-update-form-v3',
                    'zwe-angelos-details-view-v3',
                    'zwe-angelos-dynamic-view-v3'
                ];

                let defined = 0;
                let message = 'Custom Elements Status:\n';

                components.forEach((name) => {
                    const constructor = customElements.get(name);
                    if (constructor) {
                        console.log(`✅ ${name} is defined`);
                        message += `✅ ${name}\n`;
                        defined++;
                    } else {
                        console.log(`❌ ${name} is NOT defined`);
                        message += `❌ ${name}\n`;
                    }
                });

                message += `\nTotal: ${defined}/${components.length} defined`;
                alert(message);
            }

            function forceUpgrade() {
                console.log('🔧 Force upgrading all elements...');
                const components = ['zwe-angelos-create-form-v3', 'zwe-angelos-data-table-v3'];

                components.forEach((name) => {
                    const elements = document.querySelectorAll(name);
                    console.log(`Found ${elements.length} ${name} elements`);

                    elements.forEach((el) => {
                        const constructor = customElements.get(name);
                        if (constructor) {
                            customElements.upgrade(el);
                            console.log(`🔧 Upgraded ${name} element`);
                        }
                    });
                });

                alert('Force upgrade completed - check console for details');
            }

            function showStats() {
                console.log('📊 Showing all stats...');

                let message = 'Lazy Loading Stats:\n\n';

                if (window.AngelosSDK) {
                    const sdkStats = window.AngelosSDK.getStats();
                    message += `SDK: ${sdkStats.loadedComponents}/${sdkStats.totalComponents} loaded\n`;
                }

                if (window.MonacoLazyLoader) {
                    const monacoStats = window.MonacoLazyLoader.getStats();
                    message += `Monaco: ${monacoStats.coreLoaded ? 'Loaded' : 'Not loaded'}\n`;
                }

                // Icon lazy loading removed - icons are now statically imported

                alert(message);
            }
        </script>
    </head>
    <body>
        <div id="app">
            <div style="background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px">
                <h3>🧪 Lazy Loading Debug Controls</h3>
                <button onclick="checkSDKStatus()" style="margin: 5px; padding: 8px 16px">
                    Check SDK Status
                </button>
                <button onclick="checkCustomElements()" style="margin: 5px; padding: 8px 16px">
                    Check Custom Elements
                </button>
                <button onclick="forceUpgrade()" style="margin: 5px; padding: 8px 16px">
                    Force Upgrade
                </button>
                <button onclick="showStats()" style="margin: 5px; padding: 8px 16px">
                    Show Stats
                </button>
            </div>

            <div style="background: #e7f3ff; padding: 15px; margin: 10px 0; border-radius: 5px">
                <h3>📋 Original Controls</h3>
                <button id="externalValidate">External Validate</button>
                <button id="externalReport">External Report Validate</button>
                <button id="externalSubmit">External Submit</button>
                <button id="externalReset">External Reset</button>
            </div>
            <zwe-angelos-create-form-v3
                id="angelosEntityComponent"
                entity-id="test-entity"
                tenant-id="0"
                show-form-actions="true"
            ></zwe-angelos-create-form-v3>
            <!-- <zwe-angelos-data-table-v3
                id="angelosEntityComponent"
                entity-id="test-entity"
                tenant-id="0"
                show-form-actions="true"
                params='{
                    "baseUrl": "https://sb1-god-prepaidcore.mum1-pp.zetaapps.in/osmi/osmium/cards",
                    "tenantId": 600309,
                    "optumRuleSchemaId": "rule-schema-id-2-chess"
                  }'
                context='{
                    "ruleId": "Rule-pp168",
                    "name": "rule name test",
                    "optumRuleSchemaId": "rule schema test id",
                    "riskScore": 120,
                    "maximumAmountUSD": 200,
                    "allowedMCCs": "1234, 1234",
                    "disallowedMCCs": "1234, 2131",
                    "allowedMIDs": "1234, 2131",
                    "disallowedMIDs": "1234, 2131",
                    "createdAt": 1751902553482,
                    "updatedAt": 1751902553482
                  }'
            >
            </zwe-angelos-data-table-v3> -->
            <!-- <zwe-angelos-update-form
            id="angelosUpdateForm"
            entity-id="test-entity"
            tenant-id="0"
            display-form-actions="false"
            app-timezone="Europe/Berlin"
        ></zwe-angelos-update-form> -->
        </div>
    </body>
</html>
