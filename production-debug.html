<!DOCTYPE html>
<html>
<head>
    <title>Production Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ccc; margin: 10px 0; padding: 15px; }
        .log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .component-area { border: 2px solid #4CAF50; padding: 15px; min-height: 100px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; font-size: 14px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Production Build Debug Test</h1>
    
    <div class="test-section">
        <h3>Setup & Initialization</h3>
        <button onclick="initTest()">Initialize Test</button>
        <button onclick="checkSDK()">Check SDK Status</button>
        <button onclick="loadComponent()">Load Form Component</button>
        <button onclick="createComponent()">Create Component Element</button>
        <button onclick="inspectComponent()">Inspect Component</button>
    </div>
    
    <div class="test-section">
        <h3>Component Area</h3>
        <div id="component-area" class="component-area">
            <p style="color: #666; font-style: italic;">Components will appear here...</p>
        </div>
    </div>
    
    <div class="test-section">
        <h3>Debug Log</h3>
        <div id="debug-log" class="log">Waiting for test initialization...</div>
    </div>

    <script type="module" src="./dist/angelos.min.js"></script>
    
    <script>
        let testInitialized = false;
        let componentCreated = false;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            logDiv.innerHTML += `${timestamp}: ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`${timestamp}: ${message}`);
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        window.initTest = function() {
            clearLog();
            log('🚀 Initializing test environment...');
            
            // Set up required globals
            window.__zeta__ = {
                angelos: {
                    SERVICE_BASE_URL: 'https://0-0-hercules.mum1-pp.zetaapps.in/angelos/',
                    OMS_SERVICE_BASE_URL: 'http://localhost:5000'
                }
            };
            window.__HERCULES__ = {
                $assetsBaseUrl: 'https://hercules-assets.mum1-pp.zetaapps.in',
                $store: {}
            };
            localStorage.setItem('AT', 'test-token');
            
            log('✅ Globals configured');
            testInitialized = true;
            
            // Wait for SDK
            setTimeout(() => {
                if (window.AngelosSDK) {
                    log('✅ AngelosSDK detected');
                    log('📊 SDK Stats: ' + JSON.stringify(window.AngelosSDK.getStats(), null, 2));
                } else {
                    log('❌ AngelosSDK not found');
                }
            }, 500);
        };
        
        window.checkSDK = function() {
            if (!testInitialized) {
                log('❌ Please initialize test first');
                return;
            }
            
            log('🔍 Checking SDK status...');
            
            if (window.AngelosSDK) {
                const stats = window.AngelosSDK.getStats();
                log('✅ SDK available');
                log('📊 Total components: ' + stats.totalComponents);
                log('📦 Loaded components: ' + stats.loadedComponents);
                log('📋 Available: ' + stats.availableComponents.join(', '));
                log('✅ Loaded: ' + stats.loadedList.join(', '));
            } else {
                log('❌ SDK not available');
            }
            
            // Check custom element definition
            const isFormDefined = customElements.get('zwe-angelos-create-form-v3');
            log('🔍 Form custom element defined: ' + !!isFormDefined);
        };
        
        window.loadComponent = function() {
            if (!window.AngelosSDK) {
                log('❌ SDK not available');
                return;
            }
            
            log('📦 Loading form component...');
            
            window.AngelosSDK.debug.forceLoadAndUpgrade('zwe-angelos-create-form-v3')
                .then(() => {
                    log('✅ Component load completed');
                    const isNowDefined = customElements.get('zwe-angelos-create-form-v3');
                    log('🔍 Custom element now defined: ' + !!isNowDefined);
                })
                .catch(error => {
                    log('❌ Component load failed: ' + error.message);
                    console.error(error);
                });
        };
        
        window.createComponent = function() {
            const isFormDefined = customElements.get('zwe-angelos-create-form-v3');
            if (!isFormDefined) {
                log('❌ Custom element not defined. Load component first.');
                return;
            }
            
            log('🏗️ Creating component element...');
            
            const container = document.getElementById('component-area');
            container.innerHTML = ''; // Clear existing content
            
            const component = document.createElement('zwe-angelos-create-form-v3');
            component.setAttribute('entity-id', 'test-entity');
            component.setAttribute('tenant-id', '0');
            component.setAttribute('show-form-actions', 'true');
            component.id = 'test-form-component';
            
            container.appendChild(component);
            componentCreated = true;
            
            log('✅ Component element created and added to DOM');
            log('🔍 Element ID: ' + component.id);
            log('🔍 Element tag: ' + component.tagName);
            log('🔍 Element attributes: ' + Array.from(component.attributes).map(a => `${a.name}="${a.value}"`).join(', '));
        };
        
        window.inspectComponent = function() {
            if (!componentCreated) {
                log('❌ No component created yet');
                return;
            }
            
            const component = document.getElementById('test-form-component');
            if (!component) {
                log('❌ Component element not found');
                return;
            }
            
            log('🔍 Inspecting component...');
            log('  📍 Constructor: ' + component.constructor.name);
            log('  📍 Connected to DOM: ' + component.isConnected);
            log('  📍 Children count: ' + component.children.length);
            log('  📍 InnerHTML length: ' + component.innerHTML.length);
            log('  📍 ClientHeight: ' + component.clientHeight);
            log('  📍 ClientWidth: ' + component.clientWidth);
            
            if (component.innerHTML.length > 0) {
                log('  📍 InnerHTML preview: ' + component.innerHTML.substring(0, 200) + '...');
            } else {
                log('  ⚠️ Component has no inner content');
            }
            
            // Check shadow DOM
            if (component.shadowRoot) {
                log('  📍 Shadow DOM present');
                log('  📍 Shadow children: ' + component.shadowRoot.children.length);
            } else {
                log('  📍 No shadow DOM');
            }
        };
        
        // Auto-initialize on load
        document.addEventListener('DOMContentLoaded', () => {
            log('📄 DOM loaded');
        });
        
        // Listen for SDK events
        window.addEventListener('angelos:component-loaded', (e) => {
            log(`🎉 Component loaded event: ${e.detail.componentName} (${e.detail.loadTime}ms)`);
        });
        
        window.addEventListener('angelos:component-error', (e) => {
            log(`❌ Component error event: ${e.detail.componentName} - ${e.detail.error}`);
        });
    </script>
</body>
</html>
