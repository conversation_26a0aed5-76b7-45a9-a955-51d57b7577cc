import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'node:path';
import { viteVueCE } from 'unplugin-vue-ce';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
// import federation from '@originjs/vite-plugin-federation'; // Temporarily disabled
import pkg from './package.json';
export default defineConfig({
    base: './',
    define: {
        'process.env.NODE_ENV': '"production"',
        'process.env.__MODULE_ID__': JSON.stringify(pkg.name),
        'process.env.__MODULE_VERSION__': JSON.stringify(pkg.version),
        'process.env.__FEDERATED_DEPENDENCIES__': JSON.stringify({})
    },
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        }
    },
    build: {
        cssCodeSplit: true,
        target: 'esnext',
        sourcemap: false,
        minify: 'terser', // Use terser for better minification

        // Optimize chunk splitting
        chunkSizeWarningLimit: 1000,

        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            name: 'angelos-sdk',
            fileName: () => 'angelos.min.js',
            formats: ['es']
        },

        rollupOptions: {
            // Don't externalize anything - bundle everything for proper lazy loading
            external: [],

            output: {
                // Advanced chunk splitting strategy
                manualChunks: (id) => {
                    // Monaco Editor chunks
                    if (id.includes('monaco-editor')) {
                        if (id.includes('worker')) {
                            return 'monaco-workers';
                        }
                        if (id.includes('basic-languages')) {
                            const match = id.match(/basic-languages\/([^\/]+)/);
                            if (match) {
                                return `monaco-lang-${match[1]}`;
                            }
                        }
                        return 'monaco-core';
                    }

                    // Vue and core dependencies
                    if (id.includes('vue') && !id.includes('node_modules/@zeta')) {
                        return 'vue-core';
                    }

                    // Zeta UI components
                    if (id.includes('@zeta-gds/components')) {
                        return 'zeta-ui';
                    }

                    // Zeta icons
                    if (id.includes('@zeta/icons')) {
                        return 'zeta-icons';
                    }

                    // Lodash utilities
                    if (id.includes('lodash')) {
                        return 'lodash-utils';
                    }

                    // Date utilities
                    if (id.includes('dayjs') || id.includes('date-fns')) {
                        return 'date-utils';
                    }

                    // Form validation
                    if (id.includes('vee-validate') || id.includes('yup')) {
                        return 'form-validation';
                    }

                    // Component entries
                    if (id.includes('/entries/')) {
                        if (id.includes('forms')) return 'component-forms';
                        if (id.includes('data-table')) return 'component-data-table';
                        if (id.includes('details-view')) return 'component-details-view';
                        if (id.includes('dynamic-view')) return 'component-dynamic-view';
                    }

                    // Large vendor libraries
                    if (id.includes('node_modules')) {
                        return 'vendor';
                    }
                },

                // Optimize chunk naming
                chunkFileNames: (chunkInfo) => {
                    const name = chunkInfo.name || 'chunk';
                    return `assets/${name}-[hash].js`;
                },

                // Optimize asset naming
                assetFileNames: 'assets/[name]-[hash][extname]'
            }
        },

        // Terser options for better compression
        terserOptions: {
            compress: {
                drop_console: false, // Keep console logs for debugging
                drop_debugger: true,
                pure_funcs: ['console.debug'],
                passes: 2
            },
            mangle: {
                safari10: true
            },
            format: {
                comments: false
            }
        }
    },
    plugins: [
        cssInjectedByJsPlugin(),
        vue({ customElement: true }),
        viteVueCE({ isESCSS: true })
        // federation({
        //     shared: ['vue', 'lodash']
        // }) // Temporarily disabled - using our own lazy loading
    ]
});
