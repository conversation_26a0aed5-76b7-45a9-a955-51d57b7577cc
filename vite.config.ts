import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'node:path';
import { viteVueCE } from 'unplugin-vue-ce';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
// import federation from '@originjs/vite-plugin-federation'; // Temporarily disabled
import pkg from './package.json';
export default defineConfig({
    base: './',
    define: {
        'process.env.NODE_ENV': '"production"',
        'process.env.__MODULE_ID__': JSON.stringify(pkg.name),
        'process.env.__MODULE_VERSION__': JSON.stringify(pkg.version),
        'process.env.__FEDERATED_DEPENDENCIES__': JSON.stringify({})
    },
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        }
    },
    build: {
        cssCodeSplit: true,
        target: 'esnext',
        sourcemap: false,
        minify: 'terser', // Use terser for better minification

        // Optimize chunk splitting
        chunkSizeWarningLimit: 1000,

        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            name: 'angelos-sdk',
            fileName: () => 'angelos.min.js',
            formats: ['es']
        },

        rollupOptions: {
            // Simplified approach - don't externalize core dependencies
            external: [],

            output: {
                // Disable chunking to avoid Vue RefImpl circular dependency issues
                // This creates a single bundle that avoids the complex dependency resolution
                manualChunks: undefined,

                // Optimize chunk naming
                chunkFileNames: (chunkInfo) => {
                    const name = chunkInfo.name || 'chunk';
                    return `assets/${name}-[hash].js`;
                },

                // Optimize asset naming
                assetFileNames: 'assets/[name]-[hash][extname]'
            }
        },

        // Terser options for better compression
        terserOptions: {
            compress: {
                drop_console: false, // Keep console logs for debugging
                drop_debugger: true,
                pure_funcs: ['console.debug'],
                passes: 2
            },
            mangle: {
                safari10: true
            },
            format: {
                comments: false
            }
        }
    },
    plugins: [
        cssInjectedByJsPlugin(),
        vue({ customElement: true }),
        viteVueCE({ isESCSS: true })
        // federation({
        //     shared: ['vue', 'lodash']
        // }) // Temporarily disabled - using our own lazy loading
    ]
});
