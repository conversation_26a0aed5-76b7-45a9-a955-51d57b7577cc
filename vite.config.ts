import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'node:path';
import { viteVueCE } from 'unplugin-vue-ce';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import federation from '@originjs/vite-plugin-federation';
import pkg from './package.json';
export default defineConfig({
    base: './',
    define: {
        'process.env.NODE_ENV': '"production"',
        'process.env.__MODULE_ID__': JSON.stringify(pkg.name),
        'process.env.__MODULE_VERSION__': JSON.stringify(pkg.version),
        'process.env.__FEDERATED_DEPENDENCIES__': JSON.stringify({})
    },
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
            vue: 'vue/dist/vue.esm-bundler.js' // Required for creating dynamic components
        }
    },
    build: {
        cssCodeSplit: true,
        target: 'esnext',
        sourcemap: false,
        minify: 'terser',

        // Production optimizations
        reportCompressedSize: false, // Faster builds
        chunkSizeWarningLimit: 500, // Stricter chunk size control
        assetsInlineLimit: 4096, // Inline smaller assets as base64

        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            name: 'angelos-sdk',
            fileName: () => 'angelos.min.js',
            formats: ['es']
        },

        rollupOptions: {
            // Keep it simple - bundle everything for SDK distribution
            external: [],

            output: {
                // Single bundle approach - better for SDK distribution
                manualChunks: undefined,

                // Simple file naming with shorter hashes
                chunkFileNames: (chunkInfo) => {
                    const name = chunkInfo.name || 'chunk';
                    return `assets/${name}-[hash:8].js`;
                },

                // Simple asset naming
                assetFileNames: 'assets/[name]-[hash:8][extname]'
            }
        },

        // Production-optimized Terser configuration
        terserOptions: {
            compress: {
                drop_console: true, // Remove console logs in production
                drop_debugger: true,
                pure_funcs: ['console.debug'],
                passes: 2
            },
            mangle: {
                safari10: true
            },
            format: {
                comments: false
            }
        }
    },
    plugins: [
        cssInjectedByJsPlugin(),
        vue({ customElement: true }),
        viteVueCE({ isESCSS: true }),
        federation({
            shared: ['vue', 'lodash']
        })
    ]
});
