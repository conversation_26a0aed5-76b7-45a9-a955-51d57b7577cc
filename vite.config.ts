import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'node:path';
import { viteVueCE } from 'unplugin-vue-ce';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import federation from '@originjs/vite-plugin-federation';
import pkg from './package.json';
export default defineConfig({
    base: './',
    define: {
        'process.env.NODE_ENV': '"production"',
        'process.env.__MODULE_ID__': JSON.stringify(pkg.name),
        'process.env.__MODULE_VERSION__': JSON.stringify(pkg.version),
        'process.env.__FEDERATED_DEPENDENCIES__': JSON.stringify({})
    },
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
            vue: 'vue/dist/vue.esm-bundler.js' // Required for creating dynamic components
        }
    },
    build: {
        cssCodeSplit: true,
        target: 'esnext', // Ensure ES modules work properly
        sourcemap: false, // Enable source maps for debugging
        minify: true, // Disable minification

        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            name: 'angelos-sdk',
            fileName: () => 'angelos.min.js',
            formats: ['es']
        },
        rollupOptions: {
            external: ['node_modules']
        }
    },
    plugins: [
        cssInjectedByJsPlugin(),
        vue({ customElement: true }),
        viteVueCE({ isESCSS: true }),
        federation({
            shared: ['vue', 'lodash']
        })
    ]
});
